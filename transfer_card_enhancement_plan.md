# 「信用卡帳單檢核」頁面功能增強與優化計劃

## 1. 資料模型

*   繼續使用現有的 `temp_card_data` 資料表，不新增 `temp_card_data2` 表。
*   `temp_card_data` 表中欄位用途明確化：
    *   **正向查核 (CSV -> Dispatch)**:
        *   `check`: 布林值，標記此 CSV 記錄是否匹配到派車單。
        *   `data_id`: 整數，儲存匹配到的 `dispatch.id`。
    *   **反向查核 (Dispatch -> CSV)**:
        *   `check2`: 布林值，標記此 CSV 記錄是否被某派車單反向匹配到。
        *   `data_id2`: 整數，儲存反向匹配此 CSV 記錄的 `dispatch.id`。

## 2. 核心邏輯實現 (在 `app/Filament/Pages/TransferCard.php` 中)

*   **`check()` 方法 (正向查核)**: 維持或優化現有邏輯，確保正確更新 `temp_card_data.check` 和 `temp_card_data.data_id`。
*   **`checkReverse()` 方法 (反向查核)**:
    *   仔細檢視並優化現有的比對邏輯 (日期、時間、金額)。
    *   **移除或通用化**目前存在的針對特定 `Dispatch ID 4833` 和 `TempCardData ID 74` 的硬式編碼邏輯，使其適用於所有資料。
    *   確保此方法正確更新 `temp_card_data.check2` 和 `temp_card_data.data_id2`。
*   **`queryTable()` 方法 (用於顯示反向查核結果的 Dispatch 列表)**:
    *   **實施 N+1 查詢優化**: 修改 `check_status` 欄位的 `getStateUsing` 邏輯，透過預先載入當月已匹配的 `Dispatch` ID 集合（來自 `TempCardData` 的 `data_id2` 欄位且 `check2=true`），避免對每行 `Dispatch` 記錄都執行資料庫查詢。

## 3. 使用者介面 (View: `filament.pages.transfer-card`)

*   確保第一個標籤頁正確使用 `table()` 方法的結果，顯示 `TempCardData` 及其正向查核狀態。
*   確保第二個標籤頁正確使用 `queryTable()` 方法的結果，顯示 `Dispatch` 記錄及其反向查核狀態 (`check_status`)。
*   確保觸發反向查核的按鈕能正確呼叫 `checkReverse()` PHP 方法。

## 4. 資料流程示意圖

```mermaid
graph LR
    subgraph Tab1_ForwardCheck["標籤頁 1: 正向查核 (以 TempCardData 為中心)"]
        direction LR
        CSV_Upload["上傳 CSV 檔案"] --> TC_Table["暫存至 temp_card_data 表"]
        TC_Table -- 對每筆 TempCardData --> Match_Dispatch{"與 Dispatch 比對 (日期, 金額)"}
        Match_Dispatch --> Dispatch_Table["Dispatch 表"]
        Match_Dispatch -- 匹配成功 --> Update_TC_Check1["更新 TempCardData.check=1, TempCardData.data_id=Dispatch.id"]
        Match_Dispatch -- 匹配失敗 --> Update_TC_Check0["TempCardData.check=0"]
        TC_Table --> Display_TC_Table["顯示 TempCardData 表格 (含 'check' 狀態)"]
    end

    subgraph Tab2_ReverseCheck["標籤頁 2: 反向查核 (以 Dispatch 為中心)"]
        direction LR
        Select_Month["選擇月份"] --> Load_Dispatch["載入當月 Dispatch 記錄"]
        Load_Dispatch -- 對每筆 Dispatch --> Match_TC{"與 TempCardData 比對 (日期, 金額)"}
        Match_TC --> TC_Table2["temp_card_data 表"]
        Match_TC -- 匹配成功 --> Update_TC_Check2["更新 TempCardData.check2=1, TempCardData.data_id2=Dispatch.id"]
        Match_TC -- 匹配失敗 --> No_Update_TC["(TempCardData 無對應更新或標記)"]
        Load_Dispatch --> Display_Dispatch_Table["顯示 Dispatch 表格 (含 'check_status' 狀態)"]
    end

    style TC_Table fill:#f9f, stroke:#333, stroke-width:2px
    style Dispatch_Table fill:#ccf, stroke:#333, stroke-width:2px
    style TC_Table2 fill:#f9f, stroke:#333, stroke-width:2px
    style Display_TC_Table fill:#9f9, stroke:#333, stroke-width:2px
    style Display_Dispatch_Table fill:#9f9, stroke:#333, stroke-width:2px
