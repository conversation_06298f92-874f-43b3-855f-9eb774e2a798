<?php

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\DispatchController;

Route::get('/', function () {
    return view('welcome');
});
Route::get('/admin/dispatches/create/{name}/{mobile}/{price}', [DispatchController::class, 'create'])
    ->name('dispatches.create');

Route::post('/submit', function (Request $request) {
    // 獲取表單資料
    $name = $request->name;
    $email = $request->email;
    $message = $request->message;
    die('123');
    // 發送郵件
    // Mail::send('emails.contact', ['name' => $name, 'email' => $email, 'message' => $message], function($message) {
    //     $message->to('<EMAIL>', '收件人姓名')
    //             ->subject('新訊息通知');
    // });

    // return '訊息已送出！';
});
// 發票列印
Route::get('/invoice/print', [InvoiceController::class, 'printInvoice'])->name('invoice.print');

Route::get('/sendorder', [CheckoutController::class, 'sendOrder']);
Route::post('/success', [CheckoutController::class, 'success']);
Route::post('/callback', [CheckoutController::class, 'callback']);

