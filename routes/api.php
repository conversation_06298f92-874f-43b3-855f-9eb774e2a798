<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TestController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\LineBotController;
use App\Http\Controllers\ReserveController;
use App\Http\Controllers\DispatchController;

// Route::get('/user', function (Request $request) {
//     return $request->user();
// })->middleware('auth:sanctum');

Route::post('/webhook', [LineBotController::class, 'webhook']);

Route::post('/register', [DriverController::class, 'store']);
Route::post('/edit', [DriverController::class, 'edit']);

Route::post('/list', [DispatchController::class, 'list']);
Route::post('/uploadsignature', [DispatchController::class, 'uploadsignature']);
Route::post('/uploadsignature2', [DispatchController::class, 'uploadsignature2']);
// 自訂空白簽單
Route::post('/uploadsignature3', [DispatchController::class, 'uploadsignature3']);
// 瑞陞 外派司機寫入簽單
Route::post('/uploadsignature5', [DispatchController::class, 'uploadsignature5']);
Route::post('/calluptime', [DispatchController::class, 'calluptime']);
Route::post('/callfinishtime', [DispatchController::class, 'callfinishtime']);
Route::post('/callstartuptime', [DispatchController::class, 'callstartuptime']);
Route::post('/uploadnote', [DispatchController::class, 'uploadnote']);
Route::post('/getdispatch', [DispatchController::class, 'getdispatch']);
Route::post('/getdispatchbyzs', [DispatchController::class, 'getdispatchbyzs']);
Route::post('/callordertime', [DispatchController::class, 'callordertime']);
// Route::get('/myview', [DispatchController::class, 'myview']);
Route::get('/mkepaper', [DispatchController::class, 'mkepaper']);
Route::post('/drawdriverinfo', [DispatchController::class, 'drawdriverinfo']);
Route::get('/excel/testdata', [DispatchController::class, 'testdata']);
Route::get('/excel/customer', [DispatchController::class, 'getcustomer']);
Route::post('/excel/customer', [DispatchController::class, 'savecustomer']);
Route::post('/excel/drawepaper', [DispatchController::class, 'drawepaper']);
Route::post('/txtcreatedispatch', [DispatchController::class, 'txtcreatedispatch']);
// web 表單會員寫訂單
Route::post('/getvendor', [TestController::class, 'getvendor']);
Route::post('/saveorder', [TestController::class, 'saveorder']);
Route::post('/createtoken', [TestController::class, 'createtoken']);
Route::post('/checktoken', [TestController::class, 'checktoken']);
Route::post('/texttocase', [TestController::class, 'texttocase']);
// airport order
Route::post('/getdriver', [DriverController::class, 'getdriver']);
Route::post('/reserve', [ReserveController::class, 'store']);
Route::post('/myorder', [ReserveController::class, 'order']);
Route::post('/getcitys', [ReserveController::class, 'getcitys']);
Route::post('/getquote', [ReserveController::class, 'getquote']);
Route::post('/carorder', [ReserveController::class, 'carorder']);
Route::post('/getquobyorder', [ReserveController::class, 'getquobyorder']);


// invoice
Route::post('/invoicetest', [ReserveController::class, 'invoicetest']);
Route::get('/outputxls', [ReserveController::class, 'outputxls']);

// line liff 價格管理
// Route::post('/getcitys', [ReserveController::class, 'getcitys']);

// Route::get('/phpinfo', function () {
//     echo phpinfo();
// });
