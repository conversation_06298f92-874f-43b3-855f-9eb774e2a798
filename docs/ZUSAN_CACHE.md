# ZuSan 快取系統

## 概述

ZuSan 快取系統是為了提升派車單列表頁面的載入速度而設計的。它主要快取各個 tab 的計數資料，避免每次頁面載入時都執行多次資料庫查詢。

## 功能特點

### 1. 智能快取
- 自動快取各個 tab 的計數（今日、明日、本周、本月、未指派、已完成、全部）
- 支援 Redis cache tags 和一般快取驅動
- 可配置的快取過期時間
- 支援 `deleted_at` 過濾條件

### 2. 自動快取失效
- 當派車單被建立、更新或刪除時，自動清除相關快取
- 確保資料的即時性和準確性

### 3. 可配置性
- 可以透過配置檔案控制快取行為
- 支援開發環境停用快取

## 配置

### 環境變數

在 `.env` 檔案中可以設定以下變數：

```env
# 快取過期時間（秒），預設 300 秒（5分鐘）
ZUSAN_TAB_COUNT_CACHE_TTL=300

# 是否啟用快取，預設 true
ZUSAN_CACHE_ENABLED=true
```

### 配置檔案

配置檔案位於 `config/zusan.php`：

```php
return [
    'cache' => [
        'tab_count_ttl' => env('ZUSAN_TAB_COUNT_CACHE_TTL', 300),
        'enabled' => env('ZUSAN_CACHE_ENABLED', true),
        'tag_prefix' => 'zusan',
    ],
];
```

## 使用方式

### 自動運作

快取系統會自動運作，不需要額外的設定。當你：

1. 瀏覽 ZuSan 列表頁面時，系統會自動快取 tab 計數
2. 建立、編輯或刪除派車單時，系統會自動清除相關快取

### 手動清除快取

如果需要手動清除快取，可以使用以下方法：

#### 1. Artisan 命令

```bash
# 清除所有 ZuSan 快取
php artisan zusan:clear-cache

# 清除特定 vendor 的快取
php artisan zusan:clear-cache --vendor=1

# 使用統一命令清除
php artisan cache:clear-tabs --type=zusan
```

#### 2. 程式碼中清除

```php
use App\Traits\ClearsZuSanCache;

// 清除特定 vendor 的快取
ClearsZuSanCache::clearZuSanTabCache($vendorId);

// 清除所有快取
ClearsZuSanCache::clearAllZuSanCache();
```

## Tab 說明

系統支援以下 tab 的快取：

- **今日**：今天的派車單（包含 deleted_at 過濾）
- **明日**：明天的派車單（包含 deleted_at 過濾）
- **本周**：本週的派車單（包含 deleted_at 過濾）
- **本月**：本月的派車單（包含 deleted_at 過濾）
- **未指派**：狀態為 0 的派車單（包含 deleted_at 過濾）
- **已完成**：狀態為 2 的派車單（包含 deleted_at 過濾）
- **全部**：所有派車單（包含 deleted_at 過濾）

## 快取策略

### Redis 環境
- 使用 cache tags 來管理快取
- 標籤格式：`zusan_vendor_{vendor_id}` 和 `zusan_counts`
- 支援精確的快取清除

### 非 Redis 環境
- 使用一般的快取鍵
- 快取鍵格式：`zusan_count_{vendor_id}_{query_hash}`
- 資料更新時會清除所有快取（較為保守的策略）

## 效能影響

### 改善前
- 每次頁面載入執行 7 次 COUNT 查詢
- 查詢時間隨資料量增長

### 改善後
- 首次載入執行查詢並快取結果
- 後續載入直接從快取取得，大幅提升速度
- 資料更新時自動失效，確保準確性

## 與其他系統的差異

### 與 SinSheng 的差異
- **相同點**：都包含 `deleted_at` 過濾條件
- **相同點**：都有相同的 tab 結構（除了沒有「異常」tab）
- **不同點**：使用不同的快取標籤前綴 (`zusan_` vs `sinsheng_`)

### 與 Dispatch 的差異
- **不同點**：ZuSan 包含 `deleted_at` 過濾，Dispatch 沒有
- **不同點**：ZuSan 沒有「異常」tab，Dispatch 有
- **不同點**：使用不同的快取標籤前綴 (`zusan_` vs `dispatch_`)

## 監控與除錯

### 檢查快取狀態

```php
// 檢查特定快取是否存在
$cacheKey = "zusan_count_1_abc123";
$exists = Cache::has($cacheKey);

// 取得快取值
$value = Cache::get($cacheKey);
```

### 停用快取（開發環境）

在開發環境中，如果需要確保資料即時性，可以停用快取：

```env
ZUSAN_CACHE_ENABLED=false
```

## 注意事項

1. **資料一致性**：系統會在資料異動時自動清除快取，但如果有直接操作資料庫的情況，可能需要手動清除快取

2. **deleted_at 過濾**：所有查詢都包含 `deleted_at` 過濾條件，確保只計算未刪除的記錄

3. **記憶體使用**：快取會佔用一定的記憶體空間，但相對於效能提升來說是值得的

4. **快取時間**：預設快取 5 分鐘，可以根據業務需求調整

5. **Redis 建議**：建議在生產環境使用 Redis 作為快取驅動，以獲得更好的快取管理功能

## 測試

執行測試來確保快取功能正常：

```bash
php artisan test tests/Feature/ZuSanCacheTest.php
```

## 故障排除

### 快取沒有生效
1. 檢查 `ZUSAN_CACHE_ENABLED` 是否為 `true`
2. 檢查快取驅動是否正常運作
3. 檢查快取權限設定

### 資料不即時
1. 檢查是否有直接操作資料庫的程式碼
2. 手動清除快取：`php artisan zusan:clear-cache`
3. 檢查 model events 是否正常觸發

### 記憶體不足
1. 調整快取過期時間
2. 考慮使用更高效的快取驅動
3. 監控快取使用量

## 最佳實踐

### 1. 生產環境建議
- 使用 **Redis** 作為快取驅動
- 設定適當的快取過期時間（5-10分鐘）
- 定期監控快取效能

### 2. 開發環境建議
- 可以停用快取確保資料即時性
- 使用測試來驗證快取邏輯
- 定期清除快取避免測試干擾

### 3. 維護建議
- 定期檢查快取命中率
- 監控記憶體使用情況
- 在重大更新後清除相關快取
