# SinSheng 快取系統

## 概述

SinSheng 快取系統是為了提升派車單列表頁面的載入速度而設計的。它主要快取各個 tab 的計數資料，避免每次頁面載入時都執行多次資料庫查詢。

## 功能特點

### 1. 智能快取
- 自動快取各個 tab 的計數（今日、明日、本周、本月、未指派、已完成、全部）
- 支援 Redis cache tags 和一般快取驅動
- 可配置的快取過期時間

### 2. 自動快取失效
- 當派車單被建立、更新或刪除時，自動清除相關快取
- 確保資料的即時性和準確性

### 3. 可配置性
- 可以透過配置檔案控制快取行為
- 支援開發環境停用快取

## 配置

### 環境變數

在 `.env` 檔案中可以設定以下變數：

```env
# 快取過期時間（秒），預設 300 秒（5分鐘）
SINSHENG_TAB_COUNT_CACHE_TTL=300

# 是否啟用快取，預設 true
SINSHENG_CACHE_ENABLED=true
```

### 配置檔案

配置檔案位於 `config/sinsheng.php`：

```php
return [
    'cache' => [
        'tab_count_ttl' => env('SINSHENG_TAB_COUNT_CACHE_TTL', 300),
        'enabled' => env('SINSHENG_CACHE_ENABLED', true),
        'tag_prefix' => 'sinsheng',
    ],
];
```

## 使用方式

### 自動運作

快取系統會自動運作，不需要額外的設定。當你：

1. 瀏覽 SinSheng 列表頁面時，系統會自動快取 tab 計數
2. 建立、編輯或刪除派車單時，系統會自動清除相關快取

### 手動清除快取

如果需要手動清除快取，可以使用以下方法：

#### 1. Artisan 命令

```bash
# 清除所有 SinSheng 快取
php artisan sinsheng:clear-cache

# 清除特定 vendor 的快取
php artisan sinsheng:clear-cache --vendor=1
```

#### 2. 程式碼中清除

```php
use App\Traits\ClearsSinShengCache;

// 清除特定 vendor 的快取
ClearsSinShengCache::clearSinShengTabCache($vendorId);

// 清除所有快取
ClearsSinShengCache::clearAllSinShengCache();
```

## 快取策略

### Redis 環境
- 使用 cache tags 來管理快取
- 標籤格式：`sinsheng_vendor_{vendor_id}` 和 `sinsheng_counts`
- 支援精確的快取清除

### 非 Redis 環境
- 使用一般的快取鍵
- 快取鍵格式：`sinsheng_count_{vendor_id}_{query_hash}`
- 資料更新時會清除所有快取（較為保守的策略）

## 效能影響

### 改善前
- 每次頁面載入執行 7 次 COUNT 查詢
- 查詢時間隨資料量增長

### 改善後
- 首次載入執行查詢並快取結果
- 後續載入直接從快取取得，大幅提升速度
- 資料更新時自動失效，確保準確性

## 監控與除錯

### 檢查快取狀態

```php
// 檢查特定快取是否存在
$cacheKey = "sinsheng_count_1_abc123";
$exists = Cache::has($cacheKey);

// 取得快取值
$value = Cache::get($cacheKey);
```

### 停用快取（開發環境）

在開發環境中，如果需要確保資料即時性，可以停用快取：

```env
SINSHENG_CACHE_ENABLED=false
```

## 注意事項

1. **資料一致性**：系統會在資料異動時自動清除快取，但如果有直接操作資料庫的情況，可能需要手動清除快取

2. **記憶體使用**：快取會佔用一定的記憶體空間，但相對於效能提升來說是值得的

3. **快取時間**：預設快取 5 分鐘，可以根據業務需求調整

4. **Redis 建議**：建議在生產環境使用 Redis 作為快取驅動，以獲得更好的快取管理功能

## 測試

執行測試來確保快取功能正常：

```bash
php artisan test tests/Feature/SinShengCacheTest.php
```

## 故障排除

### 快取沒有生效
1. 檢查 `SINSHENG_CACHE_ENABLED` 是否為 `true`
2. 檢查快取驅動是否正常運作
3. 檢查快取權限設定

### 資料不即時
1. 檢查是否有直接操作資料庫的程式碼
2. 手動清除快取：`php artisan sinsheng:clear-cache`
3. 檢查 model events 是否正常觸發

### 記憶體不足
1. 調整快取過期時間
2. 考慮使用更高效的快取驅動
3. 監控快取使用量
