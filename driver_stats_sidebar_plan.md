# 司機接案月統計右側邊欄功能計畫

## 1. 功能需求

實現一個可全局訪問的右側滑動邊欄 (Slide-over)，用於顯示每位司機在本月、上個月及上上個月的接案次數統計。

**具體需求確認：**

*   **日期欄位**: 使用 `Dispatch` 模型的 `start_date` 欄位進行月份統計。
*   **案件狀態**: 僅計算 `status = 2` (已完成) 的案件。
*   **訪問範圍**: 統計邊欄將是全局可訪問的，透過一個放置在頂部導覽列的按鈕觸發。

## 2. 技術方案

將使用 Filament v3 的 Livewire 元件和 Slide-over Action 功能來實現。

## 3. 詳細計畫步驟

### 步驟 3.1: 建立 Livewire 元件 (`DriverMonthlyStatsWidget`)

*   **PHP 類別檔案路徑**: `app/Livewire/DriverMonthlyStatsWidget.php`
    *   (或者 `app/Filament/Widgets/DriverMonthlyStatsWidget.php`，並在 Panel Provider 中以不同方式註冊)
*   **Blade 視圖檔案路徑**: `resources/views/livewire/driver-monthly-stats-widget.blade.php`

#### 3.1.1 PHP 類別 (`DriverMonthlyStatsWidget.php`)

```php
<?php

namespace App\Livewire; // 或 App\Filament\Widgets;

use App\Models\Driver;
use App\Models\Dispatch;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB; // 如果需要原生查詢
use Livewire\Component;

class DriverMonthlyStatsWidget extends Component
{
    public $stats = [];

    public function mount()
    {
        $this->loadStats();
    }

    public function loadStats()
    {
        // 假設只統計 active = 1 的司機
        $drivers = Driver::where('active', 1)->orderBy('name')->get();
        $this->stats = [];

        $currentMonthStart = Carbon::now()->startOfMonth();
        $lastMonthStart = Carbon::now()->subMonthNoOverflow()->startOfMonth();
        $monthBeforeLastStart = Carbon::now()->subMonthsNoOverflow(2)->startOfMonth();

        foreach ($drivers as $driver) {
            $driverStats = [
                'name' => $driver->name,
                'current_month_count' => Dispatch::where('driver_id', $driver->id)
                    ->where('status', 2) // 已完成
                    ->where('start_date', '>=', $currentMonthStart->toDateString())
                    ->where('start_date', '<', $currentMonthStart->copy()->addMonthNoOverflow()->toDateString())
                    ->count(),
                'last_month_count' => Dispatch::where('driver_id', $driver->id)
                    ->where('status', 2) // 已完成
                    ->where('start_date', '>=', $lastMonthStart->toDateString())
                    ->where('start_date', '<', $lastMonthStart->copy()->addMonthNoOverflow()->toDateString())
                    ->count(),
                'month_before_last_count' => Dispatch::where('driver_id', $driver->id)
                    ->where('status', 2) // 已完成
                    ->where('start_date', '>=', $monthBeforeLastStart->toDateString())
                    ->where('start_date', '<', $monthBeforeLastStart->copy()->addMonthNoOverflow()->toDateString())
                    ->count(),
            ];
            $this->stats[] = $driverStats; // 改為陣列以方便 Blade 迭代
        }
    }

    public function render()
    {
        return view('livewire.driver-monthly-stats-widget');
    }
}

```

#### 3.1.2 Blade 視圖 (`driver-monthly-stats-widget.blade.php`)

```html
<div class="p-6"> <!-- 增加 Slide-over 內邊距 -->
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold leading-tight text-gray-900 dark:text-white">
            司機接案月統計
        </h3>
        <!-- 可以添加一個刷新按鈕 -->
        <button wire:click="loadStats" type="button" class="text-primary-600 hover:text-primary-500 dark:text-primary-500 dark:hover:text-primary-400">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
            </svg>
        </button>
    </div>
    <div class="overflow-x-auto bg-white dark:bg-gray-800 shadow-md rounded-lg">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700/50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">司機名稱</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">本月完成</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">上月完成</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">上上月完成</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @forelse ($stats as $driverStat)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $driverStat['name'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">{{ $driverStat['current_month_count'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">{{ $driverStat['last_month_count'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">{{ $driverStat['month_before_last_count'] }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">尚無司機數據</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
```

### 步驟 3.2: 在 Panel Provider 中註冊全局 Action

*   **檔案**: `app/Providers/Filament/AdminPanelProvider.php`
*   **修改**: 在 `panel()` 方法中，使用 `->topNavigationItems([...])` 添加一個 Action。

```php
<?php

namespace App\Providers\Filament;

use App\Livewire\DriverMonthlyStatsWidget; // 確認 Livewire 元件的命名空間
use Filament\Facades\Filament;
use Filament\Pages;
use Filament\Panel;
use Filament\Widgets;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\Facades\FilamentIcon;
use App\Http\Middleware\SecurityHeaders;
use Filament\Http\Middleware\Authenticate;
use Rmsramos\Activitylog\ActivitylogPlugin;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Saade\FilamentFullCalendar\FilamentFullCalendarPlugin;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Filament\Navigation\NavigationItem;         // 新增
use Filament\Actions\Action as FilamentAction;  // 新增，避免與 Laravel Action 衝突

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->colors([
                'primary' => Color::Indigo,
            ])
            ->favicon(asset('images/favicon-32x32.png'))
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                \App\Filament\Widgets\StatsOverview::class,
                \App\Filament\Widgets\CalendarWidget::class,
            ])
            ->middleware([
                // ... (中間件列表)
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                // ... (插件列表)
            ])
            ->icons([
                'actions::copy' => 'heroicon-o-clipboard',
            ])
            ->topNavigationItems([ // 新增頂部導覽項目
                NavigationItem::make('司機月統計')
                    ->icon('heroicon-o-chart-bar-square')
                    ->group('報表') // 可選，用於分組
                    ->sort(10)      // 可選，用於排序
                    ->action(
                        FilamentAction::make('viewDriverMonthlyStats')
                            ->label('查看司機月統計') // Slide-over 標題會使用這個
                            ->slideOver()
                            ->modalContent(DriverMonthlyStatsWidget::class) // 直接使用 Livewire 元件類名
                            ->modalSubmitAction(false) // 不需要 "Save" 按鈕
                            ->modalCancelAction(fn (FilamentAction $action) => $action->label('關閉'))
                            ->modalWidth('2xl') // 調整寬度 (sm, md, lg, xl, 2xl, ..., 7xl)
                    )
            ])
            ->databaseNotifications()
            ->maxContentWidth(MaxWidth::Full)
            ->sidebarFullyCollapsibleOnDesktop()
            ->sidebarWidth('11rem');
    }
}

```

## 4. Mermaid 流程圖

```mermaid
graph TD
    subgraph FilamentPanel as "Filament 管理後台"
        A[頂部導覽列按鈕 "司機月統計"]
    end

    subgraph SlideOverPanel as "滑動邊欄 (Slide-over)"
        C[DriverMonthlyStatsWidget Livewire 元件]
        I[統計表格 (HTML)]
    end

    subgraph BackendLogic as "後端邏輯"
        D[DriverMonthlyStatsWidget PHP 類別]
        E[Driver Model: 查詢所有活躍司機]
        G[Dispatch Model: 按條件查詢接案次數]
    end

    A -- UserClick --> B{FilamentAction: 開啟 Slide-over};
    B -- Loads --> C;
    C -- Calls mount()/render() --> D;
    D -- Executes loadStats() --> E;
    E -- ForEachDriver --> F{計算統計數據};
    F -- Queries --> G;
    G -- ReturnsData --> D;
    D -- PassesDataToView --> I;
```

## 5. 注意事項與可選優化

*   **效能**: 如果司機或派車單數量非常大，直接在 `loadStats()` 中進行多次資料庫查詢可能會有效能瓶頸。可以考慮使用更優化的單一查詢（例如使用 `withCount` 結合子查詢或 `DB::raw`）來一次性獲取所有司機的各月份統計。
*   **錯誤處理**: 在 Livewire 元件中可以加入更完善的錯誤處理。
*   **樣式**: 可以根據需要進一步調整表格和 Slide-over 的樣式。
*   **即時刷新**: 如果希望數據能更即時地刷新，可以在 Livewire 元件中加入輪詢 (`wire:poll`) 或監聽特定事件來觸發 `loadStats()`。
*   **權限控制**: 如果需要，可以為訪問此統計功能添加權限控制。

此計畫提供了實現該功能的核心步驟和程式碼範例。
