<div class="flex items-center px-2" x-data>
    <button
        type="button"
        x-on:click="$wire.mountAction('statistikAction')"
        class="fi-btn fi-btn-size-sm fi-btn-color-gray inline-flex items-center justify-center gap-1 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-950 shadow-sm outline-none hover:bg-gray-50 focus:ring-4 focus:ring-primary-500/50 disabled:pointer-events-none disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:border-gray-500 dark:hover:bg-gray-700 dark:focus:ring-primary-500/50"
    >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="fi-btn-icon h-5 w-5">
            <path fill-rule="evenodd" d="M2 2.75A.75.75 0 012.75 2h14.5a.75.75 0 01.75.75v1.5a.75.75 0 01-.75.75H2.75a.75.75 0 01-.75-.75v-1.5zm0 5A.75.75 0 012.75 7h14.5a.75.75 0 01.75.75v1.5a.75.75 0 01-.75.75H2.75a.75.75 0 01-.75-.75v-1.5zm0 5A.75.75 0 012.75 12h14.5a.75.75 0 01.75.75v1.5a.75.75 0 01-.75.75H2.75a.75.75 0 01-.75-.75v-1.5zM2.75 17a.75.75 0 000 1.5h14.5a.75.75 0 000-1.5H2.75z" clip-rule="evenodd" /> <!-- heroicon-o-chart-bar-square is complex, using a simpler one for now -->
        </svg>
        <span>司機月統計</span>
    </button>

    {{-- This is necessary to render the modal itself when the action is mounted --}}
    <x-filament-actions::modals />
</div>
