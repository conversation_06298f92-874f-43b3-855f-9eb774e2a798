<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
    <div style="width:720px; margin:0 auto; text-align:center;">
        <img src="{{ public_path('storage/images/ss_logo.png') }}" alt="公司Logo" width="50%"  style="padding-top: 10px"/>
        <div>
            <h4>客戶 {{ $data['customer_title'] }} 對帳單:</h4>
            <h5 style="margin-top: -20px; font-size: small;">統計日期: {{ $data['start_date'] }} ~ {{ $data['end_date'] }}</h5>
        </div>
        <table style="border-collapse: collapse; width: 720px;">
            <tr style="border-bottom: 1px #000 solid; border-bottom: 1px #000 solid; font-size: medium;">
                <th style="font-size: small; width: 3%">序號</th>
                <th style="font-size: small; width: 10%">日期 / 時間</th>
                <th style="font-size: small; width: 50%">搭乘地點</th>
                <th style="font-size: small; width: 10%">航班</th>
                <th style="font-size: small; width: 10%">車資</th>
                <th style="font-size: small; width: 17%">人員 / 電話</th>
            </tr>

            @php
                use Carbon\Carbon;
                $total = 0;
            @endphp
                @foreach ($data['items'] as $key => $item2)
                    <tr>
                        <td style="font-size: small; text-align:center;">{{ $key + 1 }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ Carbon::parse($item2->start_date)->format('Y-m-d') }}<br/>{{ Carbon::parse($item2->start_date)->format('H:i') }}</td>
                        <td style="font-size: xx-small; text-align: center;">{{ $item2->route }}</td>
                        {{-- <td style="font-size: xx-small; text-align:center;">{{ $item2->car_license }}</td> --}}
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->flight_no }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->rental_cost }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->passenger_name }}<br/>{{ $item2->passenger_mobile }}</td>
                    </tr>
                    @php
                        $subtotal = $item2->rental_cost;
                        $total += $subtotal;
                    @endphp
                @endforeach
            <tr style="border-top: 1px #000 solid; border-bottom: 1px #000 solid;">
                {{-- <td>{{ $item->customer_id }}</td> --}}
                <td></td>
                <td></td>

                <td colspan="2">共計搭乘: {{ COUNT($data['items']) }}  次</td>
                <td colspan="2" style="color: red; font-size: medium;">
                    合計: {{ $total }}<br/>
                    稅額: {{ $total * 0.05 }}<br/>
                    總計: {{ $total * 1.05 }}
                </td>
                <td></td>
            </tr>
        </table>
    </div>
</div>
