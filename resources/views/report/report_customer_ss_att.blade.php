@php
    use Illuminate\Support\Facades\Storage;
@endphp
<style>
    @font-face {
        font-family: 'msyh';
        src: url('{{ storage_path('fonts/msyh.ttf') }}') format('truetype');
        font-weight: normal;
        font-style: normal;
    }
    body {
        font-family: 'msyh', Deja<PERSON><PERSON> Sans, sans-serif;
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">

    {{-- <div class="page_break"></div> --}}
    <div style="width:600px; margin:0 auto; text-align:center;">
        <h3>附件: 簽單</h3>
        @foreach ($data['items'] as $key => $item3)
            @php
                $imgPath = public_path('storage/'.$item3->image_path);
            @endphp
            @if($item3->image_path && file_exists($imgPath) && !is_dir($imgPath))
                <img src="{{ $imgPath }}" alt="派車單圖檔" width="600" style="padding-top: 10px"/>
            @endif
        @endforeach
    </div>
    {{-- {{ var_dump($aa[0]) }} --}}
