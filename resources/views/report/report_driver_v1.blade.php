<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
    <div style="width:720px; margin:0 auto; text-align:center;">
        <div>
            <h4>司機 {{ $data['driver_name'] }} 載客統計表:</h4>
            <h5 style="margin-top: -20px; font-size: small;">統計日期: {{ $data['start_date'] }} ~ {{ $data['end_date'] }}</h5>
        </div>
        <table style="border-collapse: collapse; width: 720px;">
            <tr style="border-bottom: 1px #000 solid; border-bottom: 1px #000 solid; font-size: medium;">
                <th style="font-size: small; width: 3%">序號</th>
                <th style="font-size: small; width: 10%">日期 / 時間</th>
                <th style="font-size: small; width: 50%">搭乘地點</th>
                <th style="font-size: small; width: 7%">車資</th>
                @if (isset($data[0]['vendor_id']) && $data[0]['vendor_id'] == 4)
                    <th style="font-size: small; width: 8%">付款方式</th>
                @else
                    <th style="font-size: small; width: 8%">司機車資</th>
                @endif
                {{-- <th style="font-size: small; width: 7%">回金</th> --}}
                <th style="font-size: small; width: 17%">人員 / 電話</th>
            </tr>

            @php
                use Carbon\Carbon;
                $total = 0;
                $totalRental = 0;
                function getPaytypeChinese($type){
                    if($type == 0){
                        return '月結';
                    }else if($type == 1){
                        return '現金';
                    }else if($type == 2){
                        return 'LINE PAY';
                    }else if($type == 3){
                        return '匯款';
                    }else if($type == 4){
                        return '刷卡';
                    }else if($type == 5){
                        return '入房帳';
                    }
                };
                // dd($data['items']);
            @endphp
                @foreach ($data['items'] as $key => $item2)
                    <tr>
                        <td style="font-size: small; text-align:center;">{{ $key + 1 }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ Carbon::parse($item2->start_date)->format('Y-m-d') }}<br/>{{ Carbon::parse($item2->start_date)->format('H:i') }}</td>
                        <td style="font-size: xx-small; text-align: center;">{{ $item2->route }}</td>
                        {{-- <td style="font-size: xx-small; text-align:center;">{{ $item2->car_license }}</td> --}}
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->rental_cost }}</td>
                        @if (isset($item2['vendor_id']) && $item2['vendor_id'] == 4)
                            <td style="font-size: xx-small; text-align:center;">{{ getPaytypeChinese($item2->pay_type) }}</td>
                        @else
                            <td style="font-size: xx-small; text-align:center;">{{ $item2->driver_fee }}</td>
                        @endif
                        {{-- <td style="font-size: xx-small; text-align:center;">{{ $item2->return_fee }}</td> --}}
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->passenger_name }}<br/>{{ $item2->passenger_mobile }}</td>
                    </tr>
                    @php
                        if (isset($item2->driver_fee) && is_numeric($item2->driver_fee)) {
                            $total += $item2->driver_fee;
                        }
                        if (isset($item2->rental_cost) && is_numeric($item2->rental_cost)) {
                            $totalRental += $item2->rental_cost;
                        }
                    @endphp
                @endforeach
            <tr style="border-top: 1px #000 solid; border-bottom: 1px #000 solid;">
                {{-- <td>{{ $item->customer_id }}</td> --}}
                <td></td>
                <td></td>

                <td colspan="2">共計搭乘: {{ COUNT($data['items']) }}  次</td>
                <td>
                    營業總計: {{ $totalRental }}
                </td>
                @if (isset($data['vendor_id']) && $data['vendor_id'] == 4)
                    <td></td> {{-- If vendor_id is 4, "付款方式" column typically isn't summed --}}
                @else
                    <td style="color: red; font-size: larger;">總計: {{ $total }}</td>
                @endif
                <td></td>
            </tr>
        </table>

    </div>
    {{-- <div class="page_break"></div>
    <div style="width:600px; margin:0 auto; text-align:center;">
        <h3>附件: 簽單</h3>
        @foreach ($data['items'] as $key => $item3)
            <img src="{{ public_path('storage/'.$item3->image_path) }}" alt="派車單圖檔" width="600"  style="padding-top: 10px"/>
        @endforeach
    </div> --}}
    {{-- {{ var_dump($aa[0]) }} --}}
