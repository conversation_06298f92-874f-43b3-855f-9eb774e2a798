<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
    <div style="width:720px; margin:0 auto; text-align:center;">
        <div>
            <h4>司機 {{ $data['driver_name'] }} 載客統計表:</h4>
            <h5 style="margin-top: -20px; font-size: small;">統計日期: {{ $data['start_date'] }} ~ {{ $data['end_date'] }}</h5>
        </div>
        <table style="border-collapse: collapse; width: 720px;">
            <tr style="border-bottom: 1px #000 solid; border-bottom: 1px #000 solid; font-size: medium;">
                <th style="font-size: small; width: 3%">序號</th>
                <th style="font-size: small; width: 10%">日期 / 時間</th>
                <th style="font-size: small; width: 50%">搭乘地點</th>
                {{-- <th style="font-size: small; width: 7%">車資</th> --}}
                <th style="font-size: small; width: 8%">司機車資</th>
                {{-- <th style="font-size: small; width: 7%">回金</th> --}}
                <th style="font-size: small; width: 17%">人員 / 電話</th>
            </tr>

            @php
                use Carbon\Carbon;
                $total = 0;
                $totalRental = 0;
            @endphp
                @foreach ($data['items'] as $key => $item2)
                    <tr>
                        <td style="font-size: small; text-align:center;">{{ $key + 1 }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ Carbon::parse($item2->start_date)->format('Y-m-d') }}<br/>{{ Carbon::parse($item2->start_date)->format('H:i') }}</td>
                        <td style="font-size: xx-small; text-align: center;">{{ $item2->route }}</td>
                        {{-- <td style="font-size: xx-small; text-align:center;">{{ $item2->car_license }}</td> --}}
                        {{-- <td style="font-size: xx-small; text-align:center;">{{ $item2->rental_cost }}</td> --}}
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->driver_fee }}</td>
                        {{-- <td style="font-size: xx-small; text-align:center;">{{ $item2->return_fee }}</td> --}}
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->passenger_name }}<br/>{{ $item2->passenger_mobile }}</td>
                    </tr>
                    @php
                        $subtotal = $item2->driver_fee;
                        // $subtotalRental = $item2->rental_cost;
                        $total += $subtotal;
                        // $totalRental += $subtotalRental;
                    @endphp
                @endforeach
            <tr style="border-top: 1px #000 solid; border-bottom: 1px #000 solid;">
                {{-- <td>{{ $item->customer_id }}</td> --}}
                <td></td>
                <td></td>

                <td colspan="2">共計搭乘: {{ COUNT($data['items']) }}  次</td>
                {{-- <td>
                    營業總計: {{ $totalRental }}
                </td> --}}
                <td style="color: red; font-size: larger;">總計: {{ $total }}</td>
                <td></td>
            </tr>
        </table>

    </div>
    {{-- <div class="page_break"></div>
    <div style="width:600px; margin:0 auto; text-align:center;">
        <h3>附件: 簽單</h3>
        @foreach ($data['items'] as $key => $item3)
            <img src="{{ public_path('storage/'.$item3->image_path) }}" alt="派車單圖檔" width="600"  style="padding-top: 10px"/>
        @endforeach
    </div> --}}
    {{-- {{ var_dump($aa[0]) }} --}}
