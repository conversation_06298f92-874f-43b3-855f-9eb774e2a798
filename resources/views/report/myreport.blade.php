<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
    <div style="width:600px; margin:0 auto; padding-top: 230px; text-align:center;">
        <div>
            <h1>{{ $data['customer_title'] }} 乘車月報表</h1>
        </div><br />
        <div>
            <h2>{{ $data['start_end_date'] }}</h2>
        </div><br />
        <div>
            <h2>統一編號: {{ $data['company_id'] }}</h2>
        </div><br />
        <div>
            <h3>製表單位: 鳳凰小客車租賃有限公司</h3>
        </div><br />
    </div>
    <div class="page_break"></div>
    <div style="width:720px; margin:0 auto; text-align:center;">
        <div>
            <h4>乘車統計表:</h4>
        </div>
        <table style="border-collapse: collapse; width: 720px;">
            <tr style="border-bottom: 1px #000 solid; border-bottom: 1px #000 solid; font-size: medium;">
                <th style="font-size: small; width: 3%">序號</th>
                <th style="font-size: small; width: 10%">日期</th>
                <th style="font-size: small; width: 40%">搭乘地點</th>
                <th style="font-size: small; width: 10%">車號</th>
                <th style="font-size: small; width: 10%">航班</th>
                <th style="font-size: small; width: 10%">車資</th>
                <th style="font-size: small; width: 17%">人員 / 電話</th>
            </tr>

            @php
                use Carbon\Carbon;
                $total = 0;
            @endphp
                @foreach ($data['items'] as $key => $item2)
                    <tr>
                        <td style="font-size: small; text-align:center;">{{ $key + 1 }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ Carbon::parse($item2->start_date)->format('Y-m-d') }}<br/>{{ Carbon::parse($item2->start_date)->format('H:i') }}</td>
                        <td style="font-size: xx-small; text-align: center;">{{ $item2->route }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->car_license }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->flight_no }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->rental_cost }}</td>
                        <td style="font-size: xx-small; text-align:center;">{{ $item2->passenger_name }}<br/>{{ $item2->passenger_mobile }}</td>
                    </tr>
                    @php
                        $userSubtotal = $item2->rental_cost;
                        $total += $userSubtotal;
                    @endphp
                @endforeach
            <tr style="border-top: 1px #000 solid; border-bottom: 1px #000 solid;">
                {{-- <td>{{ $item->customer_id }}</td> --}}
                <td></td>
                <td></td>
                <td></td>

                <td colspan="2">
                    共計搭乘: {{ $data['count'] }}  次
                </td>
                <td></td>
                <td style="color: red; font-size: larger;">總計: {{ $total }}</td>
            </tr>
        </table>

    </div>
    <div class="page_break"></div>
    <div style="width:600px; margin:0 auto; text-align:center;">
        <h3>附件: 簽單</h3>
        @foreach ($data['items'] as $key => $item3)
        {{-- <h6>NO: {{ $key+ 1 }}</h6> --}}
            <img src="{{ public_path('storage/'.$item3->image_path) }}" alt="派車單圖檔" width="600"  style="padding-top: 10px"/>
        @endforeach
    </div>
    {{-- {{ var_dump($aa[0]) }} --}}
