<div
    x-data="{
        textToCopy: @js($getRecord()->copy_text_1 ?? ''),
        showNotification: false,
        notificationType: '',
        notificationMessage: '',
        notify(type, message) {
            this.notificationType = type;
            this.notificationMessage = message;
            this.showNotification = true;
            setTimeout(() => {
                this.showNotification = false;
            }, 3000); // 3 seconds
        },
        copy() {
            if (!this.textToCopy) {
                this.notify('warning', '目前無文字內容');
                return;
            }

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(this.textToCopy).then(() => {
                    this.notify('success', '已拷貝到剪貼簿中!');
                }, () => {
                    this.notify('danger', '拷貝失敗');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = this.textToCopy;
                textArea.style.position = 'fixed';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    this.notify('success', '已拷貝到剪貼簿中!');
                } catch (e) {
                    this.notify('danger', '拷貝失敗');
                }
                document.body.removeChild(textArea);
            }
        }
    }"
>
    <button
        type="button"
        x-on:click="copy"
        class="filament-button filament-button-size-md inline-flex items-center justify-center py-1 gap-1 font-medium rounded-lg border transition-colors outline-none focus:ring-offset-2 focus:ring-2 focus:ring-inset min-h-[2.25rem] px-4 text-sm text-white shadow focus:ring-white border-transparent bg-primary-600 hover:bg-primary-500 focus:bg-primary-700 focus:ring-offset-primary-700 dark:bg-primary-500 dark:hover:bg-primary-400 dark:focus:bg-primary-600 dark:focus:ring-offset-primary-600"
    >
        <x-heroicon-o-document-duplicate class="h-4 w-4 mr-1" />
        拷貝文字
    </button>

    <div
        x-show="showNotification"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-90"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-300"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-90"
        class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg text-white z-50"
        :style="
            'background-color: #333; border: 1px solid #555;'
        "
    >
        <span x-text="notificationMessage"></span>
    </div>
</div>
