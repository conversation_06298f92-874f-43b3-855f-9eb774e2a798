<div class="flex space-x-4">
    <button onclick="selectTab('tab1')" :class="currentTab === 'tab1' ? 'text-blue-600' : 'text-gray-600'">
        Tab 1
    </button>
    <button onclick="selectTab('tab2')" :class="currentTab === 'tab2' ? 'text-blue-600' : 'text-gray-600'">
        Tab 2
    </button>
    <button onclick="selectTab('tab3')" :class="currentTab === 'tab3' ? 'text-blue-600' : 'text-gray-600'">
        Tab 3
    </button>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 從 sessionStorage 中獲取當前 tab
        const currentTab = sessionStorage.getItem('currentTab') || 'tab1';
        selectTab(currentTab);
    });

    function selectTab(tab) {
        sessionStorage.setItem('currentTab', tab);
        // 刷新頁面或 AJAX 加載相應的數據
        window.location.href = '?tab=' + tab;
    }
</script>
