<x-filament::page>
    <div>1. 請先選擇檔案上傳!</div>
    <form wire:submit.prevent="save" method="post">
        {{ $this->form }}
        {{-- <x-filament::form.actions :actions="$this->getFormActions()" /> --}}
        <div class="pt-4">
            <button type="submit" class="mt-4 px-4 py-2 font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-75" style="background-color: #38a169; color: white;">
                Save
            </button>
        </div>
    </form>
    <div class="mt-8">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex" aria-label="Tabs">
                <button wire:click="setActiveTab('check')" style="{{ $activeTab === 'check' ? 'background-color: #4f46e5; color: white; border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem;' : 'background-color: #f3f4f6; color: #374151; border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem;' }}"
                    class="py-4 px-10 text-center font-medium text-base transition-all duration-200 border-2 {{ $activeTab === 'check' ? 'border-indigo-500 rounded-b-none shadow-md' : 'border-gray-300 border-b-0' }}">
                    <span class="px-3">信用卡對帳單檢查 (正向)</span>
                </button>
                <button wire:click="setActiveTab('query')" style="{{ $activeTab === 'query' ? 'background-color: #2563eb; color: white; border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem;' : 'background-color: #f3f4f6; color: #374151; border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem;' }}"
                    class="py-4 px-10 text-center font-medium text-base transition-all duration-200 border-2 ml-2 {{ $activeTab === 'query' ? 'border-blue-500 rounded-b-none shadow-md' : 'border-gray-300 border-b-0' }}">
                    <span class="px-3">派車單檢查 (反向)</span>
                </button>
            </nav>
        </div>

        <div class="mt-4">
            <!-- 載入中圖示 -->
            <div wire:loading class="flex justify-center items-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                <span class="ml-3 text-lg text-indigo-500 font-medium">載入中...</span>
            </div>

            <!-- 第一個標籤頁內容 -->
            <div id="tab-check" class="{{ $activeTab === 'check' ? 'block' : 'hidden' }} {{ $isLoading ? 'opacity-50' : '' }}"
                 wire:loading.class="opacity-50">
                <div>2. 開始進行檢查! {{ $isFileUploaded ? '已上傳' : '未上傳' }}</div>
                <button type="button" class="mt-4 px-4 py-2 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-75"
                    style="background-color: {{ $isFileUploaded ? '#38a169' : '#d1d5db' }}; color: white;"
                    {{ $isFileUploaded ? '' : 'disabled' }} wire:click="check">
                    開始檢查
                </button>
                <div class="mb-6 mt-4">
                    <div class="flex flex-col md:flex-row md:items-center md:space-x-8">
                        <label class="block text-sm font-medium text-gray-700 mb-2 md:mb-0">正向檢查結果：</label>
                        <div class="flex flex-wrap gap-8">
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-5 w-5 text-indigo-600" name="checkFilter" value="99" wire:model="checkFilter" wire:change="setCheckFilter(99)" {{ $checkFilter == 99 ? 'checked' : '' }}>
                                <span class="ml-6 text-base">全部 ({{ $totalTempCardCount }})</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-5 w-5 text-indigo-600" name="checkFilter" value="1" wire:model="checkFilter" wire:change="setCheckFilter(1)" {{ $checkFilter == 1 ? 'checked' : '' }}>
                                <span class="ml-6 text-base">有匹配 ({{ $matchedTempCardCount }})</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-5 w-5 text-indigo-600" name="checkFilter" value="0" wire:model="checkFilter" wire:change="setCheckFilter(0)" {{ $checkFilter == 0 ? 'checked' : '' }}>
                                <span class="ml-6 text-base">無匹配 ({{ $unmatchedTempCardCount }})</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">時間</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">司機</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路程</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金額</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">檢查結果</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">派車單ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($tempCardRecords as $record)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->mydate }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->mytime }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->driver }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->route }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->cost }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($record->check)
                                        <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    @else
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->data_id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <button wire:click="manualCheckTempCard({{ $record->id }})" class="px-4 py-2 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-opacity-75"
                                        style="background-color: {{ $record->check ? '#ef4444' : '#38a169' }}; color: white;"
                                    >
                                        {{ $record->check ? '取消匹配' : '標記匹配' }}
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 第二個標籤頁內容 -->
            <div id="tab-query" class="{{ $activeTab === 'query' ? 'block' : 'hidden' }} {{ $isLoading ? 'opacity-50' : '' }}"
                 wire:loading.class="opacity-50">
                <div class="mb-4">2. 派車單檢查 (以派車單為主，檢查是否存在於信用卡對帳單)</div>
                <button type="button" class="mt-4 mb-4 px-4 py-2 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
                    style="background-color: #3b82f6; color: white;"
                    wire:click="checkReverse">
                    開始反向檢查
                </button>
                <div class="mb-6 mt-4">
                    <div class="flex flex-col md:flex-row md:items-center md:space-x-8">
                        <label class="block text-sm font-medium text-gray-700 mb-2 md:mb-0">反向檢查結果：</label>
                        <div class="flex flex-wrap gap-8">
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-5 w-5 text-indigo-600" name="dispatchCheckFilter" value="99" wire:model="dispatchCheckFilter" wire:change="setDispatchCheckFilter(99)" {{ $dispatchCheckFilter == 99 ? 'checked' : '' }}>
                                <span class="ml-6 text-base">全部 ({{ $totalDispatchCount }})</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-5 w-5 text-indigo-600" name="dispatchCheckFilter" value="1" wire:model="dispatchCheckFilter" wire:change="setDispatchCheckFilter(1)" {{ $dispatchCheckFilter == 1 ? 'checked' : '' }}>
                                <span class="ml-6 text-base">有匹配 ({{ $matchedDispatchCount }})</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-5 w-5 text-indigo-600" name="dispatchCheckFilter" value="0" wire:model="dispatchCheckFilter" wire:change="setDispatchCheckFilter(0)" {{ $dispatchCheckFilter == 0 ? 'checked' : '' }}>
                                <span class="ml-6 text-base">無匹配 ({{ $unmatchedDispatchCount }})</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">時間</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">司機</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路程</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金額</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">檢查結果</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TempCardData ID</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($dispatchRecords as $record)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->start_date }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->start_time }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ optional($record->driver)->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->route }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{{ $record->rental_cost }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $exists = \App\Models\TempCardData::where('data_id2', $record->id)
                                            ->where('check2', 1)
                                            ->exists();
                                    @endphp
                                    @if($exists)
                                        <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    @else
                                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $tempCardData = \App\Models\TempCardData::where('data_id2', $record->id)
                                            ->where('check2', 1)
                                            ->first();
                                        $exists = $tempCardData ? true : false;
                                    @endphp
                                    @if($tempCardData)
                                        <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">{{ $tempCardData->id }}</a>
                                    @else
                                        <span class="text-gray-400">-</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $exists = $tempCardData ? true : false;
                                    @endphp
                                    <button wire:click="manualCheckDispatch({{ $record->id }})" class="px-4 py-2 font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-opacity-75"
                                        style="background-color: {{ $exists ? '#ef4444' : '#38a169' }}; color: white;"
                                    >
                                        {{ $exists ? '取消匹配' : '標記匹配' }}
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-filament::page>
<script>
    // document.addEventListener('livewire:load', function () {
        Livewire.on('refreshComponent', () => {
            @this.set('isFileUploaded', true);
        });
    // });
</script>
