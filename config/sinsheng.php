<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SinSheng 快取設定
    |--------------------------------------------------------------------------
    |
    | 這裡定義了 SinSheng 相關功能的快取設定
    |
    */

    'cache' => [
        /*
        |--------------------------------------------------------------------------
        | Tab 計數快取時間 (秒)
        |--------------------------------------------------------------------------
        |
        | 定義 tab 計數快取的過期時間，預設為 5 分鐘 (300 秒)
        | 你可以根據資料更新頻率來調整這個值
        |
        */
        'tab_count_ttl' => env('SINSHENG_TAB_COUNT_CACHE_TTL', 300),

        /*
        |--------------------------------------------------------------------------
        | 是否啟用快取
        |--------------------------------------------------------------------------
        |
        | 設定是否啟用 SinSheng 的快取功能
        | 在開發環境中可能會想要停用快取來確保資料即時性
        |
        */
        'enabled' => env('SINSHENG_CACHE_ENABLED', true),

        /*
        |--------------------------------------------------------------------------
        | 快取標籤前綴
        |--------------------------------------------------------------------------
        |
        | 用於 Redis cache tags 的前綴
        |
        */
        'tag_prefix' => 'sinsheng',
    ],
];
