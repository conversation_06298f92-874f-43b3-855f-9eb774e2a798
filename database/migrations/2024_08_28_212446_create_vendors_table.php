<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('名稱');
            $table->string('contact')->comment('聯絡人');
            $table->string('mobile')->nullable()->comment('聯絡手機');
            $table->string('tel')->nullable()->comment('聯絡電話');
            $table->string('fax')->nullable()->comment('聯絡傳真');
            $table->string('address')->nullable()->comment('聯絡地址');
            $table->string('join_code')->nullable()->comment('邀請碼');
            $table->string('rm_id')->nullable()->comment('RichMenu ID');
            $table->string('note')->nullable()->comment('備註');
            $table->unsignedTinyInteger('status')->default(0)->comment('狀態');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
