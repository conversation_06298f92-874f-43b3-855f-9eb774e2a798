<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('driver_id')->constrained('drivers')->cascadeOnDelete()->comment('司機ID');
            $table->string('brand')->nullable()->comment('廠牌');
            $table->string('model')->nullable()->comment('型號');
            $table->string('license')->nullable()->comment('車牌號碼');
            $table->string('color')->nullable()->comment('顏色');
            $table->string('note')->nullable()->comment('備註');
            $table->unsignedTinyInteger('status')->default(0)->comment('狀態');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cars');
    }
};
