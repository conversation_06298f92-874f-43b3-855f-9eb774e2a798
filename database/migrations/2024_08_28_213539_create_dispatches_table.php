<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dispatches', function (Blueprint $table) {
            $table->id();
            $table->string('dispatch_no')->comment('派車單號');
            $table->unsignedBigInteger('customer_id')->comment('客戶編號');
            $table->unsignedBigInteger('department_id')->comment('部門編號');
            $table->string('Passenger_name')->nullable()->comment('乘客姓名');
            $table->string('Passenger_mobile')->nullable()->comment('乘客電話');
            $table->foreignId('driver_id')->constrained('drivers')->cascadeOnDelete()->comment('司機ID');
            $table->date('start_date')->nullable()->comment('開始日期');
            $table->time('start_time')->nullable()->comment('開始時間');
            $table->date('end_date')->nullable()->comment('結束日期');
            $table->time('end_time')->nullable()->comment('結束時間');
            $table->string('start_location')->nullable()->comment('上車地點');
            $table->string('end_location')->nullable()->comment('下車地點');
            $table->string('route')->nullable()->comment('行駛路徑');
            $table->string('rental_cost')->nullable()->comment('車資');
            $table->string('flight_no')->nullable()->comment('航班');
            $table->string('note')->nullable()->comment('備註');
            $table->integer('vendor_id')->nullable()->comment('車行編號');
            $table->string('image_path')->nullable()->comment('派車單圖檔位置');
            $table->integer('signature_path')->nullable()->comment('簽名圖檔位置');
            $table->unsignedTinyInteger('status')->default(0)->comment('狀態');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dispatches');
    }
};
