<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('名稱');
            $table->string('address')->nullable()->comment('地址');
            $table->string('telephone')->nullable()->comment('電話');
            $table->string('contact')->nullable()->comment('聯絡人');
            $table->string('contact_mobile')->nullable()->comment('手機');
            $table->string('note')->nullable()->comment('備註');
            $table->boolean('status')->default(1)->comment('狀態: 1=啟用 0=停用');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
