<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drivers', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable()->comment('姓名');
            $table->string('sex')->default(0)->comment('性別');
            $table->string('mobile')->comment('聯絡手機');
            $table->string('person_id')->comment('身分證字號');
            $table->string('address')->nullable()->comment('聯絡手地址');
            $table->string('line_id')->unique()->comment('LINE ID');
            $table->string('nickname')->nullable()->comment('LINE 暱稱');
            $table->string('avatar')->nullable()->comment('LINE 頭像');
            $table->unsignedTinyInteger('active')->default(0);
            $table->foreignId('vendor_id')->constrained('vendors')->cascadeOnDelete()->comment('車行ID');
            $table->string('note')->nullable()->comment('備註');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drivers');
    }
};
