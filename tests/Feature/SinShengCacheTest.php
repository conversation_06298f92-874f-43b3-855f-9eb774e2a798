<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Dispatch;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Resources\SinShengResource\Pages\ListSinShengs;

class SinShengCacheTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 建立測試用戶
        $this->user = User::factory()->create([
            'vendor_id' => 1
        ]);
    }

    /** @test */
    public function it_caches_tab_counts()
    {
        $this->actingAs($this->user);
        
        // 建立測試資料
        Dispatch::factory()->create([
            'vendor_id' => 1,
            'start_date' => now(),
            'status' => 0,
            'deleted_at' => null
        ]);

        $listPage = new ListSinShengs();
        
        // 第一次查詢應該會執行資料庫查詢並快取結果
        $query = Dispatch::query()->where('vendor_id', 1)
            ->whereDate('start_date', '=', now()->toDateString())
            ->where('deleted_at', null);
            
        $count1 = $listPage->getFilteredCount($query);
        
        // 第二次查詢應該從快取取得
        $count2 = $listPage->getFilteredCount($query);
        
        $this->assertEquals($count1, $count2);
        $this->assertEquals(1, $count1);
    }

    /** @test */
    public function it_clears_cache_when_dispatch_is_created()
    {
        $this->actingAs($this->user);
        
        // 清除所有快取
        Cache::flush();
        
        $listPage = new ListSinShengs();
        
        // 建立查詢
        $query = Dispatch::query()->where('vendor_id', 1)
            ->whereDate('start_date', '=', now()->toDateString())
            ->where('deleted_at', null);
        
        // 第一次查詢，應該是 0
        $count1 = $listPage->getFilteredCount($query);
        $this->assertEquals(0, $count1);
        
        // 建立新的 dispatch，這應該會清除快取
        Dispatch::create([
            'vendor_id' => 1,
            'start_date' => now(),
            'status' => 0,
            'customer_id' => 1,
            'cartype_id' => 1,
            'location_type' => 1,
            'start_location' => 'Test Location',
            'end_location' => 'Test Destination',
            'rental_cost' => 1000,
        ]);
        
        // 再次查詢，應該會重新從資料庫取得新的計數
        $count2 = $listPage->getFilteredCount($query);
        $this->assertEquals(1, $count2);
    }

    /** @test */
    public function it_clears_cache_when_dispatch_is_updated()
    {
        $this->actingAs($this->user);
        
        // 建立測試資料
        $dispatch = Dispatch::create([
            'vendor_id' => 1,
            'start_date' => now(),
            'status' => 0,
            'customer_id' => 1,
            'cartype_id' => 1,
            'location_type' => 1,
            'start_location' => 'Test Location',
            'end_location' => 'Test Destination',
            'rental_cost' => 1000,
        ]);

        $listPage = new ListSinShengs();
        
        // 查詢未指派的派車單
        $unassignedQuery = Dispatch::query()->where('vendor_id', 1)
            ->where('deleted_at', null)
            ->where('status', 0);
            
        $completedQuery = Dispatch::query()->where('vendor_id', 1)
            ->where('status', 2)
            ->where('deleted_at', null);
        
        // 初始狀態：1個未指派，0個已完成
        $unassignedCount1 = $listPage->getFilteredCount($unassignedQuery);
        $completedCount1 = $listPage->getFilteredCount($completedQuery);
        
        $this->assertEquals(1, $unassignedCount1);
        $this->assertEquals(0, $completedCount1);
        
        // 更新狀態為已完成，這應該會清除快取
        $dispatch->update(['status' => 2]);
        
        // 再次查詢，計數應該更新
        $unassignedCount2 = $listPage->getFilteredCount($unassignedQuery);
        $completedCount2 = $listPage->getFilteredCount($completedQuery);
        
        $this->assertEquals(0, $unassignedCount2);
        $this->assertEquals(1, $completedCount2);
    }
}
