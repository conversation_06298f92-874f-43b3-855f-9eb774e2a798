<?php

namespace App\Imports;

use Log;
use DateTime;
use Carbon\Carbon;
use App\Models\Lscar;
use App\Models\Customer;
use App\Traits\CreateDispatch;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\Importable;

class LscarsImport implements ToModel
{
    use Importable;
    use CreateDispatch;
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        $data = [];
        if($row[0]=='入檔人' || $row[0]==''){

        }else{
            // dd($row);
            // Carbon::parse($row[5]);
            $tmp_start_date = static::transformDate($row[5]);
            $tmp_start_time = static::transformTime($row[6]);
            $tmp_start_date = $tmp_start_date.' '.$tmp_start_time;
            $tmpStartDateTime = new DateTime($tmp_start_date);
            $data['start_date'] = $tmpStartDateTime->format('Y-m-d H:i:s');
            $endDateTime = $tmpStartDateTime->modify('+2 hours');
            $data['end_date'] = $endDateTime->format('Y-m-d H:i:s');
            $customer = Customer::where('title', 'like','%'.$row[7].'%')->first();
            $data['customer_id'] = $this->getCustomerId('a'); //$customer->id;
            $data['vendor_id'] = 7; //auth()->user()->vendor_id;
            $data['start_location'] = $row[8];
            $data['end_location'] = $row[9];
            $data['passenger_name'] = $this->parsePassengerInfo('name', $row[10]);
            $data['passenger_mobile'] = $this->parsePassengerInfo('mobile', $row[10]);
            $data['cartype_id'] = $row[11];
            $data['paytype_id'] = $row[13];
            $data['rental_cost'] = $row[14];
            $data['location_type'] = 1;
            dd($data);
        }
        // return new Lscar([
        //     //
        // ]);
    }
    private function getCustomerId($value)
    {
        return 1;
    }
    function parsePassengerInfo($type, $input) {
        $pattern = '/[\n,]+/'; // 使用 preg_split 根據正則表達式進行拆分
        $parts = preg_split($pattern, $input); // 去除多餘的空格和換行符號
        $name = isset($parts[0]) ? trim($parts[0]) : '';
        $mobile = isset($parts[1]) ? trim($parts[1]) : '';
        if($type == 'name'){
            return $name;
        }
        if($type == 'mobile'){
            return $mobile;
        }
    }
    function splitDriverInfo($input) {
         // 定義正則表達式匹配車號、駕駛姓名和手機號碼
        $pattern = '/^([A-Z]{3}-\d{4})([^\d]+)(\d{4}-\d{3}-\d{3})$/';
         // 使用 preg_match 進行匹配
        if (preg_match($pattern, $input, $matches)) {
            return [ '車號' => $matches[1], '駕駛姓名' => $matches[2], '手機號碼' => $matches[3] ];
        } else {
            return null;
        }
    }

}
