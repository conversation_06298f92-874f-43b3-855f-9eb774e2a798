<?php

namespace App\Imports;

use Log;
use Carbon\Carbon;
use App\Models\Driver;
use App\Models\Mytest;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Traits\CreateDispatch;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\Importable;

class MytestImport implements ToModel
{
    use Importable;
    use CreateDispatch;
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */

    public function model(array $row)
    {
        // 公版 excel
        // dd($row[0], $row[1], $row[2], $row[3], $row[4]);
        if($row[0]=='類別' || $row[0]==''){

        }else{
            Carbon::parse($row[1]);
            // $date = Carbon::createFromDate(1900, 1, 1)->addDays($row[1] - 2)->format('Y-m-d');
            // $time = Carbon::createFromFormat('Hi', $row[2])->format('H:i');
            $date = static::transformDate(Carbon::createFromDate(1900, 1, 1)->addDays($row[1] - 2)->format('Y-m-d'));
            $time = static::transformTime($row[2]);
            $startTime = Carbon::createFromFormat('H:i', $time);
            // $dispatch_no = Dispatch::generateDispatchNumber();
            $vendor_id = auth()->user()->vendor_id;
            $customer_id = static::getCompanyId($vendor_id, $row[3]) ?? $row[3];
            $cartype_id = static::getCartypeId($row[9]);
            $paytype_id = static::getPaytypeId($row[10]);
            // dd($paytype_id);
            $route = $row[6] . ' >> '.$row[7];
            $data = [
                // 'dispatch_no' => $dispatch_no,
                'source' => 3,
                'customer_id' => $customer_id,
                'passenger_name' => $row[4],
                'passenger_mobile' => $row[5],
                'cartype_id' => $cartype_id,
                'driver_id' => 1,
                'paytype_id' => $paytype_id,
                'location_type' => static::getLocationType($vendor_id, $row[0]),
                'route' => $route,
                'start_date' => $date.' '.$startTime->format('H:i'),
                'vendor_id' => $vendor_id,
                'status' => 102,
                'rental_cost' => $row[11],
                'flight_no' => $row[12]
            ];
            // dd($data);
            try {
                Log::info( 'aaa',['data' => $data]);
                $rs = new Mytest($data);
                $rs->save();
            } catch (\Exception $e) {
                Log::error($e->getMessage());
                // return $e->getMessage();
            }
        }
        // return $rs;
    }
    public static function getCompanyId($vendor_id, $location_text)
    {
        $customer_name = '貴賓';
        $cid = Customer::where('vendor_id', '=', $vendor_id)
            ->where('title', 'LIKE', "%{$customer_name}%")->first()->id ?? 0;
        // dd($cid);
        return optional(Customer::where('vendor_id', '=', $vendor_id)
                        ->where('title', 'LIKE', "%{$location_text}%")
                        ->first())->id ?? $cid;
    }
    public static function getDriverId($vendor_id, $location_text)
    {
        return optional(Driver::where('vendor_id', '=', $vendor_id)
                        ->where('name', 'LIKE', "%{$location_text}%")
                        ->first())->id ?? 0;
    }
    public static function getLocationType($vendor_id, $location_text)
    {
        // 1.接,2.送,3.包車,0.其他
        if (strpos($location_text, '1') !== false && strpos($location_text, '接機') !== false) {
            return 1;
        } elseif (strpos($location_text, '2') !== false && strpos($location_text, '送機') !== false) {
            return 2;
        } elseif (strpos($location_text, '3') !== false && strpos($location_text, '包車') !== false) {
            return 3;
        } else {
            return 4; // 找不到
        }
    }

}
