<?php

namespace App\Actions\Tables;

use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification as FilamentNotification;
use App\Models\Dispatch; // 確保 Dispatch 模型被正確引入
// 如果您的表單中用到了其他模型，例如 Customer, Department, Driver, Cartype，也需要引入
use App\Models\Customer;
use App\Models\Department;
use App\Models\Driver;
use App\Models\Cartype;
use Illuminate\Database\Eloquent\Builder; // 確保 Builder 被引入 (用於 relationship 的 query)
use Filament\Facades\Filament; // 確保 Filament Facade 被引入

class CopyIdAction extends Action
{
    protected string | \Illuminate\Contracts\Support\Htmlable | \Closure | null $label = '複製並編輯';

    protected function setUp(): void
    {
        parent::setUp();
        // Log::info('CopyIdAction setUp: Initializing action.'); // 新增日誌，確認 setUp 被調用

        $this->icon('heroicon-o-document-duplicate');
        $this->modalHeading('複製派車單');
        $this->modalSubmitActionLabel('儲存複製');
        $this->modalWidth('4xl'); // 您可以根據需要調整 Modal 寬度

        // 定義 Modal 中的表單欄位
        $this->form(function (Model $record) { // $record 是被複製的原始記錄
            Log::info('CopyIdAction form() closure called for record ID: ' . $record->id); // 新增日誌，確認 form 閉包被調用
            $currentVendorId = Filament::auth()->user()->vendor_id;

            // 這裡應該是您之前提供的完整表單欄位定義
            // 為了簡潔，我先放一個簡單的例子，您可以替換回您完整的表單
            return [
                Components\Hidden::make('vendor_id')->default($currentVendorId),
                // Components\Section::make('基本資訊 (測試 Modal)')
                //     ->schema([
                //         Components\TextInput::make('passenger_name')
                //             ->label('乘客名稱 (來自 Modal)')
                //             ->default($record->passenger_name) // 從原始記錄預填
                //             ->required(),
                //         Components\TextInput::make('rental_cost')
                //             ->label('費用 (來自 Modal)')
                //             ->numeric()
                //             ->default($record->rental_cost) // 從原始記錄預填
                //             ->required(),
                //         // ... 您可以逐步加回其他欄位進行測試
                //     ])->columns(1),
                 // 例如：

                 Components\Grid::make(2)->schema([
                     Components\Section::make('客戶與乘客資訊')
                         ->schema([
                             Components\Select::make('customer_id')
                                 ->relationship('customer', 'title', fn (Builder $query) => $query->where('vendor_id', $currentVendorId)->orderBy('sequence', 'asc'))
                                 // ... 其他客戶欄位設定 ...
                                 ->default($record->customer_id)
                                 ->required(),
                             // ... 其他乘客資訊欄位 ...
                             Components\TextInput::make('passenger_name')
                                 ->label('乘客名稱')
                                 ->default($record->passenger_name),
                         ])->columns(2),
                     // ... 其他 Sections 和欄位 ...
                 ]),
                 Components\Textarea::make('note')
                     ->label('備註')
                     ->default($record->note)
                     ->columnSpanFull(),

            ];
        });

        // 定義 Modal 提交後的動作
        $this->action(function (Model $originalRecord, array $data): void {
            Log::info('CopyIdAction action() called after modal submission.', ['original_id' => $originalRecord->id, 'form_data' => $data]);

            try {
                $newDispatchData = $data; // 從 Modal 表單獲取的資料

                // 根據您的完整表單，處理地點等欄位
                if (isset($data['location_type'])) {
                    $locationType = $data['location_type'];
                    $newDispatchData['start_location'] = ($locationType == '1' && isset($data['start_location_airport'])) ? $data['start_location_airport'] : ($data['start_location_custom'] ?? null);
                    $newDispatchData['end_location'] = ($locationType == '2' && isset($data['end_location_airport'])) ? $data['end_location_airport'] : ($data['end_location_custom'] ?? null);
                    unset($newDispatchData['start_location_airport'], $newDispatchData['start_location_custom'], $newDispatchData['end_location_airport'], $newDispatchData['end_location_custom']);
                }


                $newDispatch = new Dispatch();
                $newDispatch->fill($newDispatchData); // 使用表單數據填充

                // 覆寫特定欄位
                $newDispatch->dispatch_no = Dispatch::generateDispatchNumber(); // 確保 Dispatch 模型有此方法
                $newDispatch->status = 0; // 重設狀態
                $newDispatch->vendor_id = $data['vendor_id'] ?? $originalRecord->vendor_id; // 確保 vendor_id

                // 清除不應複製或自動生成的欄位
                $fieldsToClear = ['image_path', 'image2_path', 'image3_path', 'signature_path', 'up_time', 'down_time', 'finish_time', 'confirm_order_time', 'deleted_at', 'copy_text_1', 'row_color'];
                foreach ($fieldsToClear as $field) {
                    $newDispatch->$field = null;
                }
                $newDispatch->created_at = null; // 讓 Eloquent 自動處理
                $newDispatch->updated_at = null; // 讓 Eloquent 自動處理

                $newDispatch->save();

                Log::info('New dispatch created from modal with ID: ' . $newDispatch->id);

                FilamentNotification::make()
                    ->title('派車單已複製並儲存')
                    ->body('新的派車單已成功建立，編號：' . $newDispatch->dispatch_no)
                    ->success()
                    ->send();

            } catch (\Exception $e) {
                Log::error('Error in CopyIdAction modal submission: ' . $e->getMessage(), ['exception' => $e]);
                FilamentNotification::make()
                    ->title('複製派車單失敗')
                    ->body('發生錯誤：' . $e->getMessage())
                    ->danger()
                    ->send();
            }
        });
    }
}
