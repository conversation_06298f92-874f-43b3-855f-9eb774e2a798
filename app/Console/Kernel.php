<?php

namespace App\Console;

use Illuminate\Support\Facades\Log;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule)
    {
        Log::info('1111');
        $schedule->command('check:driverstartstatus')->everyMinute();
        Log::info('222');
    }

    protected function commands()
    {
        Log::info('333');
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
        Log::info('444');
    }
}
