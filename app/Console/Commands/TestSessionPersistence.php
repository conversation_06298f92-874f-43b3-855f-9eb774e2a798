<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Session;

class TestSessionPersistence extends Command
{
    protected $signature = 'test:session-persistence';
    protected $description = '測試 session persistence 的效能影響';

    public function handle()
    {
        $this->info("測試 Session Persistence 效能影響...");
        
        // 模擬篩選器資料
        $filterData = [
            'customer_id' => 1,
            'status' => 0,
            'start_date' => '2024-01-01',
            'driver_id' => 5,
            'location_type' => 1
        ];
        
        // 測試寫入 session
        $start = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            Session::put("table_filters_zusan_{$i}", $filterData);
        }
        $writeTime = microtime(true) - $start;
        
        // 測試讀取 session
        $start = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            $data = Session::get("table_filters_zusan_{$i}");
        }
        $readTime = microtime(true) - $start;
        
        // 測試清除 session
        $start = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            Session::forget("table_filters_zusan_{$i}");
        }
        $clearTime = microtime(true) - $start;
        
        $this->info("結果 (100次操作):");
        $this->line("寫入時間: " . round($writeTime * 1000, 2) . "ms");
        $this->line("讀取時間: " . round($readTime * 1000, 2) . "ms");
        $this->line("清除時間: " . round($clearTime * 1000, 2) . "ms");
        $this->line("平均單次操作: " . round(($writeTime + $readTime) * 1000 / 200, 3) . "ms");
        
        if (($writeTime + $readTime) < 0.01) {
            $this->info("✅ Session persistence 效能影響極小");
        } else {
            $this->warn("⚠️  Session persistence 有一定效能影響");
        }
        
        return 0;
    }
}
