<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Traits\ClearsSinShengCache;

class ClearSinShengCache extends Command
{
    use ClearsSinShengCache;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sinsheng:clear-cache {--vendor= : 指定要清除的 vendor ID，不指定則清除全部}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清除 SinSheng 相關的快取';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $vendorId = $this->option('vendor');

        if ($vendorId) {
            $this->info("正在清除 Vendor ID {$vendorId} 的 SinSheng 快取...");
            static::clearSinShengTabCache((int) $vendorId);
            $this->info("Vendor ID {$vendorId} 的快取已清除完成！");
        } else {
            $this->info("正在清除所有 SinSheng 快取...");
            static::clearAllSinShengCache();
            $this->info("所有 SinSheng 快取已清除完成！");
        }

        return 0;
    }
}
