<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Dispatch;
use Illuminate\Support\Facades\DB;

class TestZuSanPerformance extends Command
{
    protected $signature = 'test:zusan-performance {--vendor=1}';
    protected $description = '測試 ZuSan 載入效能';

    public function handle()
    {
        $vendorId = $this->option('vendor');
        
        $this->info("測試 ZuSan 載入效能 (Vendor ID: {$vendorId})...");
        
        // 清除查詢日誌
        DB::flushQueryLog();
        DB::enableQueryLog();
        
        $start = microtime(true);
        
        // 模擬 ZuSan 列表頁面的查詢
        $dispatches = Dispatch::with([
            'customer',
            'department',
            'driver',
            'car',
            'cartype'
        ])
        ->where('vendor_id', $vendorId)
        ->whereNull('deleted_at')
        ->limit(50)
        ->get();
        
        $queryTime = microtime(true) - $start;
        $queries = DB::getQueryLog();
        DB::disableQueryLog();
        
        $this->info("結果:");
        $this->line("載入時間: " . round($queryTime * 1000, 2) . "ms");
        $this->line("查詢數量: " . count($queries));
        $this->line("記錄數量: " . $dispatches->count());
        
        // 測試 copyableState 效能（模擬）
        $start = microtime(true);
        foreach ($dispatches as $record) {
            // 模擬原本的查詢方式
            // $driver = Driver::find($record->driver_id);
            
            // 新的方式：使用預載入的關聯
            $driver = $record->driver;
        }
        $copyableTime = microtime(true) - $start;
        
        $this->line("CopyableState 處理時間: " . round($copyableTime * 1000, 2) . "ms");
        
        if ($queryTime < 0.1) {
            $this->info("✅ 效能良好！");
        } elseif ($queryTime < 0.5) {
            $this->warn("⚠️  效能普通");
        } else {
            $this->error("❌ 效能需要改善");
        }
        
        return 0;
    }
}
