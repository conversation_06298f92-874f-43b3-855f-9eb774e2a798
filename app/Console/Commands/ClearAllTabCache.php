<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Traits\ClearsZuSanCache;

class ClearAllTabCache extends Command
{
    use ClearsZuSanCache;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-tabs {--vendor= : 指定要清除的 vendor ID，不指定則清除全部} {--type= : 指定要清除的類型 (zusan|all)，預設為 all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清除所有 Tab 相關的快取 (ZuSan)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $vendorId = $this->option('vendor');
        $type = $this->option('type') ?? 'all';

        $this->info("開始清除 Tab 快取...");

        switch ($type) {
            case 'zusan':
                $this->clearZuSanCache($vendorId);
                break;
            case 'all':
            default:
                $this->clearZuSanCache($vendorId);
                break;
        }

        $this->info("Tab 快取清除完成！");
        return 0;
    }

    /**
     * 清除 ZuSan 快取
     */
    private function clearZuSanCache(?string $vendorId): void
    {
        if ($vendorId) {
            $this->line("正在清除 Vendor ID {$vendorId} 的 ZuSan 快取...");
            static::clearZuSanTabCache((int) $vendorId);
            $this->info("✓ Vendor ID {$vendorId} 的 ZuSan 快取已清除");
        } else {
            $this->line("正在清除所有 ZuSan 快取...");
            static::clearAllZuSanCache();
            $this->info("✓ 所有 ZuSan 快取已清除");
        }
    }
}
