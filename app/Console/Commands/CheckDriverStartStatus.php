<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Dispatch;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckDriverStartStatus extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:driverstartstatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '檢查司機是否出發並更新狀態';
    public function __construct() {
        parent::__construct();
    }
    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info("檢查司機未出發狀態任務開始運行");
        Log::info('檢查>> '.Carbon::now()->addMinute());
        $dispatches = Dispatch::where('status', 1)
            ->where('start_date', '<=', Carbon::now()->addMinute())
            ->whereNotNull('driver_id')
            ->where('vendor_id', 6)
            ->get();
        Log::info('檢查司機是否出發並更新狀態', $dispatches->toArray());
        foreach ($dispatches as $dispatch) {
            if (is_null($dispatch->car_start_time)) {
                $dispatch->status = 2; // 更新狀態為未出發
                $dispatch->save();
            }
        }
        $this->info('檢查完成並更新未出發的派車狀態');
        Log::info("檢查司機未出發狀態任務完成運行");
    }
}
