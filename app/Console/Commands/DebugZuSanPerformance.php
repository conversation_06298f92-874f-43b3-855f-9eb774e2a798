<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Dispatch;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Department;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DebugZuSanPerformance extends Command
{
    protected $signature = 'debug:zusan-performance {--vendor=1}';
    protected $description = '檢查 ZuSan 效能問題';

    public function handle()
    {
        $vendorId = $this->option('vendor');
        
        $this->info("開始檢查 ZuSan 效能問題 (Vendor ID: {$vendorId})...");
        
        // 1. 檢查資料量
        $this->checkDataVolume($vendorId);
        
        // 2. 檢查查詢效能
        $this->checkQueryPerformance($vendorId);
        
        // 3. 檢查快取狀態
        $this->checkCacheStatus($vendorId);
        
        // 4. 檢查索引
        $this->checkIndexes();
        
        $this->info("效能檢查完成！");
    }

    private function checkDataVolume($vendorId)
    {
        $this->info("\n=== 資料量檢查 ===");
        
        $dispatchCount = Dispatch::where('vendor_id', $vendorId)->count();
        $activeDispatchCount = Dispatch::where('vendor_id', $vendorId)->whereNull('deleted_at')->count();
        $driverCount = Driver::where('vendor_id', $vendorId)->where('active', 1)->count();
        $customerCount = Customer::where('vendor_id', $vendorId)->count();
        $departmentCount = Department::where('vendor_id', $vendorId)->count();
        
        $this->line("派車單總數: {$dispatchCount}");
        $this->line("有效派車單: {$activeDispatchCount}");
        $this->line("司機數量: {$driverCount}");
        $this->line("客戶數量: {$customerCount}");
        $this->line("部門數量: {$departmentCount}");
    }

    private function checkQueryPerformance($vendorId)
    {
        $this->info("\n=== 查詢效能檢查 ===");
        
        // 模擬 ZuSan 的主要查詢
        DB::enableQueryLog();
        
        $start = microtime(true);
        
        // 1. 主要列表查詢
        $query = Dispatch::with(['customer', 'department', 'driver', 'car', 'cartype'])
            ->where('vendor_id', $vendorId)
            ->whereNull('deleted_at')
            ->limit(50)
            ->get();
            
        $listQueryTime = microtime(true) - $start;
        
        // 2. Tab 計數查詢
        $start = microtime(true);
        $todayCount = Dispatch::where('vendor_id', $vendorId)
            ->whereDate('start_date', '=', now()->toDateString())
            ->where('deleted_at', null)
            ->count();
        $tabQueryTime = microtime(true) - $start;
        
        // 3. 司機選項查詢
        $start = microtime(true);
        $drivers = Driver::where('vendor_id', $vendorId)
            ->where('active', 1)
            ->orderBy('sequence', 'asc')
            ->get();
        $driverQueryTime = microtime(true) - $start;
        
        $queries = DB::getQueryLog();
        DB::disableQueryLog();
        
        $this->line("列表查詢時間: " . round($listQueryTime * 1000, 2) . "ms");
        $this->line("Tab 計數查詢時間: " . round($tabQueryTime * 1000, 2) . "ms");
        $this->line("司機選項查詢時間: " . round($driverQueryTime * 1000, 2) . "ms");
        $this->line("總查詢數量: " . count($queries));
        
        // 顯示慢查詢
        $this->info("\n最近的查詢:");
        foreach (array_slice($queries, -5) as $query) {
            $this->line("時間: {$query['time']}ms - SQL: " . substr($query['query'], 0, 100) . "...");
        }
    }

    private function checkCacheStatus($vendorId)
    {
        $this->info("\n=== 快取狀態檢查 ===");
        
        // 檢查 ZuSan 快取
        $cacheEnabled = config('zusan.cache.enabled', true);
        $cacheTtl = config('zusan.cache.tab_count_ttl', 300);
        
        $this->line("ZuSan 快取啟用: " . ($cacheEnabled ? '是' : '否'));
        $this->line("快取過期時間: {$cacheTtl}秒");
        
        // 檢查司機選項快取
        $driverCacheKey = "driver_options_{$vendorId}";
        $driverCacheExists = Cache::has($driverCacheKey);
        $this->line("司機選項快取存在: " . ($driverCacheExists ? '是' : '否'));
        
        // 檢查 tab 計數快取
        $sampleQuery = Dispatch::query()->where('vendor_id', $vendorId)
            ->whereDate('start_date', '=', now()->toDateString())
            ->where('deleted_at', null);
        $cacheKey = $this->generateCacheKey($sampleQuery, $vendorId);
        $tabCacheExists = Cache::has($cacheKey);
        $this->line("Tab 計數快取存在: " . ($tabCacheExists ? '是' : '否'));
    }

    private function checkIndexes()
    {
        $this->info("\n=== 索引檢查 ===");
        
        // 檢查重要的索引
        $indexes = DB::select("SHOW INDEX FROM dispatches WHERE Key_name != 'PRIMARY'");
        
        $this->line("dispatches 表的索引:");
        foreach ($indexes as $index) {
            $this->line("- {$index->Key_name} ({$index->Column_name})");
        }
        
        // 建議的索引
        $this->info("\n建議的索引:");
        $this->line("- vendor_id + deleted_at");
        $this->line("- vendor_id + start_date");
        $this->line("- vendor_id + status");
    }

    private function generateCacheKey($query, $vendorId)
    {
        $tagPrefix = config('zusan.cache.tag_prefix', 'zusan');
        $sql = $query->toSql();
        $bindings = $query->getBindings();
        $queryHash = md5($sql . serialize($bindings));
        return "{$tagPrefix}_count_{$vendorId}_{$queryHash}";
    }
}
