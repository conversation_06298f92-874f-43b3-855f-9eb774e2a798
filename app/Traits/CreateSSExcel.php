<?php
namespace App\Traits;

use DateTime;
use Carbon\Carbon;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Customer;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

trait CreateSSExcel
{
    public static function generateExcel($type, $records, $filters)
    {
        // dd($filters['driver_id']);
        $data['items'] = $records;
        $vendor_id = $records[0]['vendor_id'];
        $data['start_date'] = $filters['start_date']['start_date'] ?? '';
        $data['end_date'] = $filters['start_date']['end_date'] ?? '';
        // dd( $data['items']);
        if($filters['customer_id']['customer_id'] == null){
            $data['customer_title'] = '';
            $data['customer_telephone'] = '';
            $data['customer_contact'] = '';
            $data['customer_address'] = '';
        }else{
            $customer = Customer::where('id',$filters['customer_id']['customer_id'])->first();
            Log::info('Customer:'.json_encode($customer));
            $data['customer_company_id'] = $customer->company_id;
            $data['customer_title'] = $customer->full_title;
            if(empty($data['customer_company_id'])){

            }else{
                $data['customer_title'] .= " (".$data['customer_company_id'].")";
            }
            $data['customer_telephone'] = $customer->telephone;
            $data['customer_contact'] = $customer->contact;
            $data['customer_address'] = $customer->address;
        }

        if($vendor_id==6){
            $carTypes = [
                20 => '轎車', // 添加其他車型 ID 與名稱
                21 => '進口車', // 添加其他車型 ID 與名稱
                22 => 'VAN', // 添加其他車型 ID 與名稱
                23 => 'BENZ', // 添加其他車型 ID 與名稱
                27 => '九人座', // 添加其他車型 ID 與名稱
            ];
        }else if($vendor_id==9){
            $carTypes = [
                28 => '五人轎車', // 添加其他車型 ID 與名稱
                29 => '九人座', // 添加其他車型 ID 與名稱
                30 => '七人座', // 添加其他車型 ID 與名稱
            ];
        }
         // 轉換資料格式
        $result_data = $data['items']->map(function ($item,$index) use ($carTypes) {
            return [
                'id' => (string)($index +1),
                'date' => date('Y-m-d', strtotime($item['start_date'])),
                'location_type' => static::getLocationTypeChinese($item['location_type']),
                'cartype' => $carTypes[$item['cartype_id']] ?? '未知車型',
                'time' => date('H:i', strtotime($item['start_date'])),
                'flight_no' => $item['flight_no'] ?? '',
                'detail' => static::checkDetail($item['route'] ?? '', $item['pickup_and_dropoff_location']), // Added null coalescing for route as well for safety
                'passenger' => static::checkPassenger($item['passenger_name'] ?? '', $item['pickup_and_dropoff_location']),
                'amount' => (float)$item['rental_cost'],
                'note' => $item['note'], // 你可以根據需要添加註解
            ];
        })->toArray();

        $spreadsheet = new Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();
        $sheet->getPageSetup()->setPaperSize(PageSetup::PAPERSIZE_A4);
        $imagePath = storage_path('app/public/images/ss_logo.png');

        $drawing = new Drawing();
        $drawing->setPath($imagePath);

        // 獲取圖片尺寸 (假設使用 GD 庫)
        list($width, $height) = getimagesize($imagePath);
        // A4 頁面尺寸 (mm)
        $pageWidth = 210;
        $pageHeight = 297;
        // 设置页边距（单位：英寸）
        // $sheet->getPageMargins()->setTop(0.5);
        $sheet->getPageMargins()->setRight(0.5);
        $sheet->getPageMargins()->setLeft(0.5);
        // $sheet->getPageMargins()->setBottom(0.5);
        // 計算縮放比例 (以寬度為基準，確保圖片不會超出頁面)
        $scale = 0.55; //$pageWidth / $width;
        $newHeight = $height * $scale;

        $drawing->setWidth($pageWidth);
        $drawing->setHeight($newHeight);
        $drawing->setName('Logo');
        $drawing->setDescription('Paid');
        $drawing->setPath($imagePath); // put your path and image here
        $drawing->setCoordinates('A1');
        $drawing->setOffsetX(0);
        // $drawing->setWorksheet($spreadsheet->getActiveSheet());
        $drawing->setWorksheet($sheet);

        $sheet->setCellValue('A5', '公 司 : ');
        $sheet->setCellValue('A6', '聯絡人 : ');
        $sheet->setCellValue('A7', '電 話 : ');
        $sheet->setCellValue('A8', '地 址 : ');
        $sheet->setCellValue('B5', $data['customer_title']);
        $sheet->setCellValue('B6', $data['customer_contact']);
        $sheet->setCellValue('B7', $data['customer_telephone']);
        $sheet->setCellValue('B8', $data['customer_address']);

        // 设置表头
        $headers = ['序', '日期', '派車用途', '車型', '時間', '班機號碼', '明細', '乘車人', '金額', '備註'];
        $sheet->fromArray($headers, null, 'A10');

        $row = 11;
        $total = 0;
        // 遍历数据并写入 Excel
        foreach ($result_data as $item) {
            $sheet->fromArray($item, null, 'A' . $row);
            $total += $item['amount'];
            $row++;
        }
        // 在最后一行下方添加横线
        $lastRow = $row;
        $sheet->getStyle('A'.$lastRow.':J'.$lastRow)
            ->getBorders()
            ->getBottom()
                ->setBorderStyle(Border::BORDER_THIN);
        // 自动调整列宽
        // foreach (range('A', 'H') as $columnID) {
        //     $sheet->getColumnDimension($columnID)->setAutoSize(true);
        // }
        $headerRow = 10; // 假设 Header 行是第 11 行
        $sheet->getStyle('A'.$headerRow.':J'.$headerRow)
            ->getAlignment()
            // ->setWrapText(true)
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('E10:G'.$lastRow)
            ->getAlignment()
            ->setWrapText(true);
        $sheet->getStyle('H10:J'.$lastRow)
            ->getAlignment()
            ->setWrapText(true);
        // 添加小计行
        $sheet->setCellValue('H'.($lastRow + 1), '小計');
        $sheet->setCellValue('I'.($lastRow + 1), $total);

        // 添加稅額行
        $tax = $total * 0.05;
        $sheet->setCellValue('H'.($lastRow + 2), '稅額');
        $sheet->setCellValue('I'.($lastRow + 2), $tax);

        // 添加總計行
        $grandTotal = $total + $tax;
        $sheet->setCellValue('H'.($lastRow + 3), '總計');
        $sheet->setCellValue('I'.($lastRow + 3), $grandTotal);
        // 添加 Header 行的上下边框
        $styleArray = [
            'borders' => [
                'top' => ['borderStyle' => Border::BORDER_THIN],
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
            ],
        ];
        $sheet->getStyle('A'.$headerRow.':J'.$headerRow)->applyFromArray($styleArray);
        $sheet->getColumnDimension('A')->setWidth(6);
        $sheet->getColumnDimension('B')->setWidth(10);
        $sheet->getColumnDimension('C')->setWidth(10);  // 派車用途
        $sheet->getColumnDimension('D')->setWidth(8);  // 車型
        $sheet->getColumnDimension('E')->setWidth(8);  // 時間
        $sheet->getColumnDimension('F')->setWidth(10);  // 班機號碼
        $sheet->getColumnDimension('G')->setWidth(34);  // 明細
        $sheet->getColumnDimension('H')->setWidth(10);  // 乘車人
        $sheet->getColumnDimension('I')->setWidth(10);  // 金額
        $sheet->getColumnDimension('J')->setWidth(10);  // 備註
        // Log::info('Hello World');
        $writer = new Xlsx($spreadsheet);
        $now = Carbon::now()->format('YmdHis');
        $filename = 'Excel_請款單_' . $now . '.xlsx';
        $filePath = public_path('storage/excel_outputs/' . $filename);
        $writer->save($filePath);
        // dd($data);
        // 生成 PDF
        // 保存 PDF 到本地
        // $fileName = 'Excel_' .$records[0]->vendor_id .'_'. date('Ymd_His') . '_' . uniqid() . '.pdf';
        // Log::info($fileName);
        // $filePath = storage_path('app/public/driver_outputs/' . $fileName);
        // $pdf->save($filePath);
        return $filename;
    }

    public static function checkDetail(string $route, ?array $pickupAndDropoffLocations): string
    {
        if (empty($pickupAndDropoffLocations) || $pickupAndDropoffLocations == '[]') {
            return $route;
        }

        $routeParts = explode(" >> ", $route);
        $startLocation = $routeParts[0] ?? '';
        $endLocation = $routeParts[1] ?? '';

        $locations = [];
        foreach ($pickupAndDropoffLocations as $location) {
            $locations[] = $location['location'];
        }

        $middleLocations = implode(" >> ", $locations);

        if(empty($middleLocations)){
            return $route;
        }

        return $startLocation . " >> " . $middleLocations . (empty($endLocation) ? "" : " >> " . $endLocation);
    }
    public static function checkPassenger(string $mainPassengerNameInput, ?array $pickupAndDropoffLocations): string
    {
        $passengerList = [];
        $mainPassengerName = trim($mainPassengerNameInput);

        // Add main passenger name if it's not an empty string
        if ($mainPassengerName !== '') {
            $passengerList[] = $mainPassengerName;
        }

        // Add additional passenger names
        if (!empty($pickupAndDropoffLocations) && $pickupAndDropoffLocations !== '[]') {
            foreach ($pickupAndDropoffLocations as $location) {
                if (isset($location['name']) && trim($location['name']) !== '') {
                    $passengerList[] = trim($location['name']);
                }
            }
        }
        return implode("\n", $passengerList);
    }
    public static function getLocationTypeChinese($location_type)
    {
        return match ($location_type) {
            1 => '接機',
            2 => '送機',
            0 => '單程',
            3 => '包車旅遊',
            4 => '短途',
            default => '未知',
        };
    }
}
