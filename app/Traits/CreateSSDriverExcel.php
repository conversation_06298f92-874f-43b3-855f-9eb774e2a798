<?php
namespace App\Traits;

use DateTime;
use Carbon\Carbon;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Customer;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;

trait CreateSSDriverExcel
{
    public static function generateExcel($type, $records, $filters)
    {
        // dd($records);
        $data['items'] = $records;
        $data['start_date'] = $filters['start_date']['start_date'] ?? '';
        $data['end_date'] = $filters['start_date']['end_date'] ?? '';
        // dd($filters);
        if($filters['driver_id'] == null){
            // die('請選擇司機');
        }else{
            $driver = driver::where('id',$filters['driver_id'])->first();
            $data['driver_name'] = $driver->name;
            $data['driver_id'] = $driver->id;
            $data['driver_mobile'] = $driver->mobile;
        }
        // $carTypes = [
        //     20 => '轎車', // 添加其他車型 ID 與名稱
        //     21 => '進口車', // 添加其他車型 ID 與名稱
        //     22 => 'VAN', // 添加其他車型 ID 與名稱
        //     23 => 'BENZ', // 添加其他車型 ID 與名稱
        // ];
         // 轉換資料格式
        $result_data = $data['items']->map(function ($item,$index)  {

            return [
                'id' => (string)($index +1),
                'date' => date('Y-m-d', strtotime($item['start_date'])),
                // 'cartype' => $carTypes[$item['cartype_id']] ?? '未知車型',
                'time' => date('H:i', strtotime($item['start_date'])),
                'detail' => $item['route'],
                'cash' => $item['pay_type'] == 1 ? (float)$item['rental_cost'] : '',
                'amount' => (float)$item['driver_fee'],
                'note' => $item['driver_note'], // 你可以根據需要添加註解
            ];
        })->toArray();

        $spreadsheet = new Spreadsheet();
        $sheet       = $spreadsheet->getActiveSheet();
        $sheet->getPageSetup()->setPaperSize(PageSetup::PAPERSIZE_A4);
        $imagePath = storage_path('app/public/images/ss_logo.png');

        $drawing = new Drawing();
        $drawing->setPath($imagePath);

        // 獲取圖片尺寸 (假設使用 GD 庫)
        list($width, $height) = getimagesize($imagePath);
        // A4 頁面尺寸 (mm)
        $pageWidth = 210;
        $pageHeight = 297;
        // 设置页边距（单位：英寸）
        // $sheet->getPageMargins()->setTop(0.5);
        $sheet->getPageMargins()->setRight(0.5);
        $sheet->getPageMargins()->setLeft(0.5);
        // $sheet->getPageMargins()->setBottom(0.5);
        // 計算縮放比例 (以寬度為基準，確保圖片不會超出頁面)
        $scale = 0.55; //$pageWidth / $width;
        $newHeight = $height * $scale;

        $drawing->setWidth($pageWidth);
        $drawing->setHeight($newHeight);
        $drawing->setName('Logo');
        $drawing->setDescription('Paid');
        $drawing->setPath($imagePath); // put your path and image here
        $drawing->setCoordinates('A1');
        $drawing->setOffsetX(0);
        // $drawing->setWorksheet($spreadsheet->getActiveSheet());
        $drawing->setWorksheet($sheet);

        $sheet->setCellValue('A5', '司機大名 : ');
        $sheet->setCellValue('A6', '聯絡電話 : ');
        $sheet->setCellValue('A7', '日期範圍 : ');
        $sheet->setCellValue('B5', $data['driver_name']);
        $sheet->setCellValue('B6', $data['driver_mobile']);
        $sheet->setCellValue('B7', $data['start_date'] . ' ~ ' . $data['end_date']);

        // 设置表头
        $headers = ['序', '日期', '時間', '明細', '現金', '金額', '備註'];
        $sheet->fromArray($headers, null, 'A9');

        $row = 10;
        $total = 0;
        $cash_total = 0;
        // 遍历数据并写入 Excel
        foreach ($result_data as $item) {
            $sheet->fromArray($item, null, 'A' . $row);
            $cash_total += (int)$item['cash'];
            $total += (int)$item['amount'];
            $row++;
        }
        // 在最后一行下方添加横线
        $lastRow = $row;
        $sheet->getStyle('A'.$lastRow.':G'.$lastRow)
            ->getBorders()
            ->getBottom()
                ->setBorderStyle(Border::BORDER_THIN);
        // 自动调整列宽
        // foreach (range('A', 'H') as $columnID) {
        //     $sheet->getColumnDimension($columnID)->setAutoSize(true);
        // }
        $headerRow = 9; // 假设 Header 行是第 11 行
        $sheet->getStyle('A'.$headerRow.':G'.$headerRow)
            ->getAlignment()
            // ->setWrapText(true)
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        $sheet->getStyle('D9:D'.$lastRow)
            ->getAlignment()
            ->setWrapText(true);
        $sheet->getStyle('G9:G'.$lastRow)
            ->getAlignment()
            ->setWrapText(true);
        // 添加小計
        $sheet->setCellValue('D'.($lastRow + 1), '小計');
        $sheet->setCellValue('E'.($lastRow + 1), $cash_total);
        $sheet->setCellValue('F'.($lastRow + 1), $total);
        // 小計文字靠右
        $sheet->getStyle('D'.($lastRow + 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        // 添加稅額行
        // $tax = (int)($total * 0.05);
        // $sheet->setCellValue('E'.($lastRow + 2), '稅額');
        // $sheet->setCellValue('F'.($lastRow + 2), $tax);

        // 添加總計行
        // $grandTotal = (int)$total + (int)$tax;
        // $sheet->setCellValue('E'.($lastRow + 3), '總計');
        // $sheet->setCellValue('F'.($lastRow + 3), $grandTotal);
        // 添加 Header 行的上下边框
        $styleArray = [
            'borders' => [
                'top' => ['borderStyle' => Border::BORDER_THIN],
                'bottom' => ['borderStyle' => Border::BORDER_THIN],
            ],
        ];
        $sheet->getStyle('A'.$headerRow.':G'.$headerRow)->applyFromArray($styleArray);
        $sheet->getColumnDimension('A')->setWidth(10);
        $sheet->getColumnDimension('B')->setWidth(10);
        $sheet->getColumnDimension('C')->setWidth(10);  // 時間
        $sheet->getColumnDimension('D')->setWidth(34);  // 明細
        $sheet->getColumnDimension('E')->setWidth(10);  // 現金
        $sheet->getColumnDimension('F')->setWidth(10);  // 金額
        $sheet->getColumnDimension('G')->setWidth(10);  // 備註
        // Log::info('Hello World');
        $writer = new Xlsx($spreadsheet);
        $now = Carbon::now()->format('YmdHis');
        $filename = 'Excel_司機對帳單_'.$data['driver_name']. '_' . $now . '.xlsx';
        $filePath = public_path('storage/excel_outputs/' . $filename);
        $writer->save($filePath);
        return $filename;
    }

}
