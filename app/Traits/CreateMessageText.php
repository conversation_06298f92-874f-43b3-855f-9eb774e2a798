<?php
namespace App\Traits;

use App\Models\Customer;
use App\Models\Location;
use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait CreateMessageText
{
    public static function generateMessage($records)
    {
        // dd($records);
        // --- Time Formatting ---
        $timestamp = strtotime($records["ride_time"]);
        $formatted_time = date("H:i", $timestamp);
        $records['ride_time'] = $formatted_time; // Update ride_time in the array for placeholder replacement
        $vendorTitle = ($records['vendor_id']==6)? '瑞陞' : '新生';
        $template = ''; // Initialize template
        $tripCounter = 2; // Default starting counter for intermediate stops

        // --- Select Initial Template Based on location_type ---
        if ($records['location_type'] == '1') { // 接機 (Airport Pickup)
            $tmpStartChinese = static::getAirportChinese($records['start_location']);
            // Simplified template for 接機: 行程1 only shows pickup location (airport)
            $template = "您己成功預約".$vendorTitle."租車所提供的接送服務\n請確認預約資料，如錯誤或變更，請與我們聯絡，謝謝！\n預約車型：{{car_model}}\n乘車日期：{{ride_date}}\n預約類型：{{booking_type}}\n乘車時間：{{ride_time}}\n航班資訊：{{flight_no}}\n車資金額：{{fare_amount}}\n行程資訊：\n行程1：\n上車地點: " . $tmpStartChinese; // Directly insert converted airport name
            // Main passenger details + destination will be added as 行程2 later
            // Intermediate stops will start from 行程3
        } else if ($records['location_type'] == '2') { // 送機 (Airport Drop-off)
            // Template for 送機: 行程1 includes passenger details + pickup location
            $template = "您己成功預約".$vendorTitle."租車所提供的接送服務\n請確認預約資料，如錯誤或變更，請與我們聯絡，謝謝！\n預約車型：{{car_model}}\n乘車日期：{{ride_date}}\n預約類型：{{booking_type}}\n乘車時間：{{ride_time}}\n航班資訊：{{flight_no}}\n車資金額：{{fare_amount}}\n行程資訊：\n行程1：\n{{passenger_name}}, 連絡電話:{{passenger_mobile}}\n上車地點: {{start_location}}";
            // Destination airport will be added last
        } else { // 單程 (One-way), 包車 (Charter), 短途 (Short Trip)
            // Template for others: 行程1 includes passenger details + pickup location
            $template = "您己成功預約".$vendorTitle."租車所提供的接送服務\n請確認預約資料，如錯誤或變更，請與我們聯絡，謝謝！\n預約車型：{{car_model}}\n乘車日期：{{ride_date}}\n預約類型：{{booking_type}}\n乘車時間：{{ride_time}}\n航班資訊：{{flight_no}}\n車資金額：{{fare_amount}}\n行程資訊：\n行程1：\n{{passenger_name}}, 連絡電話:{{passenger_mobile}}\n上車地點: {{start_location}}";
            // Destination will be added last
        }

        // --- Replace General Placeholders ---
        foreach ($records as $key => $value) {
            // Skip keys handled specially (like locations, passenger details for 接機) or not part of the template body
            if (!in_array($key, ['otherTrip', 'start_location', 'end_location', 'passenger_name', 'passenger_mobile', 'location_type', 'return'])) {
                 $template = str_replace('{{' . $key . '}}', $value ?? '', $template); // Use null coalescing
            }
            // Replace specific placeholders only if NOT 接機 (location_type != 1) as they are handled differently
            if ($records['location_type'] != '1') {
                if ($key == 'start_location') {
                    $template = str_replace('{{start_location}}', $value ?? '', $template);
                }
                if ($key == 'passenger_name') {
                    $template = str_replace('{{passenger_name}}', $value ?? '', $template);
                }
                if ($key == 'passenger_mobile') {
                    $template = str_replace('{{passenger_mobile}}', $value ?? '', $template);
                }
            }
        }

        // --- Handle Specific Trip Logic ---

        // ** For 接機 (location_type == 1): Add main passenger + destination as 行程2 **
        if ($records['location_type'] == '1') {
            $tmpEndChinese = static::getAirportChinese($records['end_location']); // Convert destination if it's an airport code
            $formattedTrip2 = '姓名：' . ($records['passenger_name'] ?? '') . '，電話：' . ($records['passenger_mobile'] ?? '') . "\n地址：" . $tmpEndChinese;
            $template .= "\n行程2：\n" . $formattedTrip2;
            $tripCounter = 3; // Set counter for intermediate stops to start from 3
        }

        // --- Add Intermediate Stops (otherTrip) ---
        if (!empty($records['otherTrip']) && is_array($records['otherTrip'])) {
            foreach ($records['otherTrip'] as $otherTrip) {
                // Format each intermediate stop
                $formattedTrip = '姓名：' . ($otherTrip['name'] ?? '') . '，電話：' . ($otherTrip['mobile'] ?? '') . "\n地址：" . ($otherTrip['location'] ?? '');
                $template .= "\n行程" . $tripCounter . "：\n" . $formattedTrip;
                $tripCounter++; // Increment counter for the next stop
            }
        }

        // --- Add Final Destination (Only if NOT 接機) ---
        if ($records['location_type'] != '1') {
            $tmpEndChinese = static::getAirportChinese($records['end_location']); // Convert destination if it's an airport code

            // For 送機 (type 2), just add the destination address as the last stop
            if ($records['location_type'] == '2') {
                 $formattedEndTrip = "下車地點：" . $tmpEndChinese;
            }
            // For 單程 etc. (type 0, 3, 4), add passenger details + destination address if no intermediate stops, otherwise just address
            else {
                 // If there were no intermediate stops, $tripCounter is still 2. Add passenger details.
                 if ($tripCounter == 2 && empty($records['otherTrip'])) {
                      $formattedEndTrip = '姓名：' . ($records['passenger_name'] ?? '') . '，電話：' . ($records['passenger_mobile'] ?? '') . "\n地址：" . $tmpEndChinese;
                 } else { // If there were intermediate stops, just add the address.
                      $formattedEndTrip = "地址：" . $tmpEndChinese;
                 }
            }
            $template .= "\n行程" . $tripCounter . "：\n" . $formattedEndTrip;
        }

        // Clean up any remaining placeholders (optional but good practice)
        $template = preg_replace('/\{\{[^}]+\}\}/', '', $template);

        Log::info('Generated Message: ' . $template);
        return $template;
    }
    public function getBookingTypeName($location_type)
    {
        Log::info('搭乘類別: ' .$location_type);
        if($location_type == 1){
            return '接機';
        }else if($location_type == 2){
            return '送機';
        }else if($location_type == 3){
            return '包車';
        }else if($location_type == 4){
            return '短途';
        }else{
            return '單程';
        }
    }
    public static function getAirportChinese($location) {
        if($location == '1'){
            return '松山機場';
        }else if($location == '2'){
            return '桃園機場 T1';
        }else if($location == '3'){
            return '桃園機場 T2';
        }else if($location == '4'){
            return '桃園機場';
        }else if($location == '5'){
            return '台中機場';
        }else if($location == '6'){
            return '小港機場';
        }
        return $location;
    }
}
