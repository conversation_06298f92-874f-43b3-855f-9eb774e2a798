<?php
namespace App\Traits;

use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Location;
use App\Models\InvoiceApi;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait CreateInvoiceTrait
{
    public static function generateInvoice($id)
    {

        $invoice = Invoice::where('id', $id)->first();
        $invoiceApi = InvoiceApi::where('vendor_id',$invoice->vendor_id)->first();
        $timeStamp = round(microtime(true) * 1000);  // 設定 idno 和 password $idno = '123123'; $password = '123123';
        // $timeStamp2 = microtime(true);  // 設定 idno 和 password $idno = '123123'; $password = '123123';
        // $uncode = '53418005';
        // $idno = 'Giveme08';
        // $password = '2Y7Nh88';
        $idno = empty($invoiceApi->api_no) ? 'Giveme08' : $invoiceApi->api_no; //good59168chen';
        $uncode = empty($invoiceApi->api_uncode) ? '53418005' : $invoiceApi->api_uncode; //'53029218';
        $password = empty($invoiceApi->api_password) ? '2Y7Nh88' : $invoiceApi->api_password; //'Good@59168';
        // dd($password);
        $url = 'https://www.giveme.com.tw/invoice.do?action=addB2B';
        // $url = 'https://www.giveme.com.tw/invoice.do?action=addB2C';
        // 將 timeStamp、idno 和 password 組合成一個字串
        $combinedString = $timeStamp . $idno . $password;
        // 使用 MD5 進行哈希處理，並將結果轉換為大寫ch
        $md5Hash = strtoupper(md5($combinedString));
        Log::info('invouice >> ' . $timeStamp . $idno . $md5Hash);
        // $invoice = Invoice::find($id);
        $customer = Customer::find($invoice->customer_id);
        $invoiceDetails = $invoice->invoiceDetails()->get();
        $postData = [
            'timeStamp' => $timeStamp,
            'uncode' => $uncode,
            'idno' => $idno,
            'sign' => $md5Hash,
            'customerName' => $invoice->company_name,
            'phone' => empty($invoice->company_code) ? '' : $invoice->company_code, // /1234567
            'datetime' => $invoice->invoice_date,
            'email' => empty($invoice->email) ? '' : $invoice->email,
            'state' => '0',
            'donationCode' => '0',
            'taxType' => 0,
            'taxState' => '1',
            'zeroAmount' => 0,
            'sales' => $invoice->sales,
            'amount' => $invoice->amount,
            'totalFee' => $invoice->total_fee,
            'content' => $invoice->note,
            'items' => $invoiceDetails->map(function ($item) {
                return [
                    'name' => $item->name,
                    'money' => $item->money,
                    'number' => $item->number,
                    'remark' => $item->remark,
                ];
            })
        ];
        Log::info('Invoice postData Data: ' . json_encode($postData));
        // 顯示結果
        // echo "timeStamp: " . $timeStamp . PHP_EOL;
        // echo "MD5 Hash: " . $md5Hash . PHP_EOL;
        // $postData = [
        //     'timeStamp' => $timeStamp,
        //     'uncode' => '53418005',
        //     'idno' => $idno,
        //     'sign' => $md5Hash,
        //     'customerName' => '志-測試',
        //     'phone' => '96832636',
        //     'orderCode' => '',
        //     'datetime' => '2024-11-26',
        //     'email' => '<EMAIL>',
        //     'state' => '0',
        //     'donationCode' => '0',
        //     'taxType' => 0,
        //     'companyCode' => '0',
        //     'freeAmount' => 0,
        //     'zeroAmount' => 0,
        //     'sales' => 95,
        //     'amount' => 5,
        //     'totalFee' => 100,
        //     'content' => 'test',
        //     'items' => $invoiceDetails, //[['name' => '測試商品', 'money' => 1, 'number' => 1, 'remark' => '']]
        // ];
        Log::info('payload >> ' . json_encode($postData));
        // die;
        $rs = self::postInvoice($url, $postData);
        Log::info('Invoice 回傳結果 str: ' . $rs);
        $rsArray = json_decode($rs, true);
        // Log::info('Invoice 回傳結果與解析 array: ' . json_encode($rsArray));
        if(isset($rsArray['success']) && $rsArray['success']=='true'){
            $invoice->update(['invoice_no' => $rsArray['code'], 'status' => 1]);
            $returnStr = ['status' => true, 'message' => $rsArray['msg']];
        }else{
            $returnStr = ['status' => false, 'message' => $rsArray['msg']];
        }
        return $returnStr;
    }
    public static function printInvoice($url)
    {

    }
    public static function cancelInvoice($id)
    {
        $invoice = Invoice::find($id);
        $timeStamp = round(microtime(true) * 1000);  // 設定 idno 和 password $idno = '123123'; $password = '123123';
        // $timeStamp2 = microtime(true);  // 設定 idno 和 password $idno = '123123'; $password = '123123';
        $idno = 'Giveme08';
        $password = '2Y7Nh88';
        $url = 'https://www.giveme.com.tw/invoice.do?action=cancelInvoice';
        // 將 timeStamp、idno 和 password 組合成一個字串
        $combinedString = $timeStamp . $idno . $password;
        // 使用 MD5 進行哈希處理，並將結果轉換為大寫ch
        $md5Hash = strtoupper(md5($combinedString));
        $payload = [
            'timeStamp' => $timeStamp,
            'uncode' => '53418005',
            'idno' => $idno,
            'sign' => $md5Hash,
            'code' => $invoice->invoice_no, // 發票號碼
            'remark' => '開錯', // 作廢原因
        ];
        Log::info('作廢發票 payload >> ' . json_encode($payload));
        $rs = self::postInvoice($url, $payload);
        $rsArray = json_decode($rs, true);
        Log::info('作廢發票 回傳結果與解析 array: ' . json_encode($rsArray));
        if(isset($rsArray['success']) && $rsArray['success']=='true'){
            $invoice->update(['status' => 2]);
            $returnStr = ['status' => true, 'message' => $rsArray['msg']];
        }else{
            $returnStr = ['status' => false, 'message' => $rsArray['msg']];
        }
        return $returnStr;
    }
    public static function postInvoice($url, $postData)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postData),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        // Log::info('invouice rs >> ' . $response);
        curl_close($curl);
        return $response;
    }
}
