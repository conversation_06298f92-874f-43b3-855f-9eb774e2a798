<?php
namespace App\Traits;

use App\Models\Customer;
use App\Models\Location;
use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait CreateDispatch
{
    public static function generateRecord($type, $records, $filters)
    {

    }
    public static function getCartypeId($cartypeStr){
        $number = substr($cartypeStr, 0, strpos($cartypeStr, '-'));
        return $number;
    }
    public static function getPaytypeId($paytypeStr){
        $number = substr($paytypeStr, 0, strpos($paytypeStr, '-'));
        return $number;
    }
    public static function getLocationName($typeid){
        return Location::find($typeid)->title;
    }
    private static function transformDate($value)
    {
        if (is_numeric($value)) {
            $year = date('Y');            // 動態取得當前年份
            $month = floor($value / 100); // 取得月份
            $day = $value % 100;          // 取得日期

            // 使用當前年份，並將日期格式化為 "YYYY-MM-DD"
            return sprintf("%d-%02d-%02d", $year, $month, $day);
        }
        return $value;
    }
    private static function transformTime($value)
    {
        if (is_numeric($value)) {
            $hours = floor($value / 100); // 取得小時
            $minutes = $value % 100;      // 取得分鐘
            return sprintf("%d:%02d", $hours, $minutes);
        }
        return $value; // 若無法解析，則返回原值
    }
}
