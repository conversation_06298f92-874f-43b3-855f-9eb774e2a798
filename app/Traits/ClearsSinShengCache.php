<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;

trait ClearsSinShengCache
{
    /**
     * 清除 SinSheng tab 計數快取
     */
    public static function clearSinShengTabCache(?int $vendorId = null): void
    {
        // 檢查是否啟用快取
        if (!config('sinsheng.cache.enabled', true)) {
            return;
        }

        if (!$vendorId) return;

        $tagPrefix = config('sinsheng.cache.tag_prefix', 'sinsheng');

        // 如果使用 Redis，使用 cache tags 來管理快取
        if (config('cache.default') === 'redis') {
            Cache::tags([
                "{$tagPrefix}_vendor_{$vendorId}",
                "{$tagPrefix}_counts"
            ])->flush();
        } else {
            // 對於其他快取驅動，清除特定模式的快取鍵
            // 注意：這個方法比較粗暴，在生產環境中可能需要更精確的實作
            $patterns = [
                "{$tagPrefix}_count_{$vendorId}_*"
            ];

            foreach ($patterns as $pattern) {
                // 由於 Laravel 沒有內建的 pattern 刪除，這裡提供一個簡化版本
                // 在實際使用中，你可能需要實作更精確的快取清除邏輯
                Cache::flush();
                break; // 只需要執行一次 flush
            }
        }
    }

    /**
     * 清除所有 SinSheng 相關快取
     */
    public static function clearAllSinShengCache(): void
    {
        // 檢查是否啟用快取
        if (!config('sinsheng.cache.enabled', true)) {
            return;
        }

        $tagPrefix = config('sinsheng.cache.tag_prefix', 'sinsheng');

        if (config('cache.default') === 'redis') {
            Cache::tags(["{$tagPrefix}_counts"])->flush();
        } else {
            Cache::flush();
        }
    }
}
