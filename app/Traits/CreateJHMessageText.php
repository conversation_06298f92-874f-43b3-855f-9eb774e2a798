<?php
namespace App\Traits;

use App\Models\Customer;
use App\Models\Location;
use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait CreateJHMessageText
{
    public static function generateMessage($records)
    {
        // dd($records);
        $tmpMessage = '';
        Log::info('文字轉換 ',$records);
        // $tmpride_time = $records["ride_time"];
        $timestamp = strtotime($records["ride_time"]);

        // 使用 date 函數將時間戳格式化為所需的格式
        $formatted_time = date("H:i", $timestamp);
        $records['ride_time'] = $formatted_time;
        if($records['location_type'] == '1'){
            // '接機';
            $tmpStartChinese = static::getAirportChinese($records['start_location']);
            $template = "您己成功預約京鴻租車所提供的接送服務\n請確認預約資料，如錯誤或變更，請與我們聯絡，謝謝！\n預約車型：{{car_model}}\n乘車日期：{{ride_date}}\n預約類型：{{booking_type}}\n乘車時間：{{ride_time}}\n航班資訊：{{flight_no}}\n車資金額：{{fare_amount}}\n行程資訊：\n行程1：\n上車地點:{{start_location}} \n行程2：\n姓名:{{passenger_name}}, 連絡電話:{{passenger_mobile}}\n下車地點: {{end_location}}";
        }else if($records['location_type'] == '2'){
            // '送機';
            $template = "您己成功預約京鴻租車所提供的接送服務\n請確認預約資料，如錯誤或變更，請與我們聯絡，謝謝！\n預約車型：{{car_model}}\n乘車日期：{{ride_date}}\n預約類型：{{booking_type}}\n乘車時間：{{ride_time}}\n航班資訊：{{flight_no}}\n車資金額：{{fare_amount}}\n行程資訊：\n行程1：\n{{passenger_name}}, 連絡電話:{{passenger_mobile}}\n上車地點: {{start_location}}";
        }else {
            $template = "您己成功預約京鴻租車所提供的接送服務\n請確認預約資料，如錯誤或變更，請與我們聯絡，謝謝！\n預約車型：{{car_model}}\n乘車日期：{{ride_date}}\n預約類型：{{booking_type}}\n乘車時間：{{ride_time}}\n航班資訊：{{flight_no}}\n車資金額：{{fare_amount}}\n行程資訊：\n行程1：\n{{passenger_name}}, 連絡電話:{{passenger_mobile}}\n上車地點: {{start_location}}";
        }
        // $record = [ 'car_model' => 'Toyota Camry', 'ride_date' => '2025-01-15', 'booking_type' => '單程', 'ride_time' => '10:00 AM', 'fare_amount' => 'NT$500', 'trip1' => '台北車站到101大樓' ];
        foreach ($records as $key => $value) {
            if ($key != 'otherTrip') {
                // $value = str_replace("\n", "\n- ", $value);
                $template = str_replace('{{' . $key . '}}', $value, $template);
            }
        }
        // 遍歷其他行程資料，並添加至模板
        if (isset($records['otherTrip'])) {
            ($records['location_type'] == '1') ? $tripCounter = 3 : $tripCounter = 2; // 从 trip2 开始
            foreach ($records['otherTrip'] as $otherTrip) {
                $formattedTrip = '姓名：' . $otherTrip['name'] . '，電話：' . $otherTrip['mobile'] . "\n地址：" . $otherTrip['location'];
                $template .= "\n行程" . $tripCounter . "：\n" . $formattedTrip;
                $tripCounter++;
            }
        }
        if($records['location_type'] == '1'){
            $formattedEndTrip = "";
        }else{
            $formattedEndTrip = "地址：" . $records['end_location'];
            $template .= "\n行程" . $tripCounter . "：\n" . $formattedEndTrip;
        }
        return $template;
        // dd($template);
    }
    public function getBookingTypeName($location_type)
    {
        Log::info('搭乘類別: ' .$location_type);
        if($location_type == 1){
            return '接機';
        }else if($location_type == 2){
            return '送機';
        }else if($location_type == 3){
            return '包車';
        }else if($location_type == 4){
            return '短途';
        }else{
            return '單程';
        }
    }
    public static function getAirportChinese($location) {
        if($location == '1'){
            return '松山機場';
        }else if($location == '2'){
            return '桃園機場 T1';
        }else if($location == '3'){
            return '桃園機場 T2';
        }else if($location == '4'){
            return '桃園機場';
        }else if($location == '5'){
            return '台中機場';
        }
    }
}
