<?php
namespace App\Traits;

use App\Models\Customer;
use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait CreateImage
{
    public static function generatePdf($type, $records, $filters)
    {
        // dd($type);
        $data['items'] = $records;
        $data['start_date'] = $filters['start_date']['start_date'] ?? '';
        $data['end_date'] = $filters['start_date']['end_date'] ?? '';
        if($type == 'driver'){
            $data['driver_name'] = Driver::where('id',$filters['driver_id'])->first()->name ?? '';
            $pdf = Pdf::loadView('report.report_driver_v1', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'customer'){
            $data['customer_title'] = Customer::where('id',$filters['customer_id'])->first()->title ?? '';
            $pdf = Pdf::loadView('report.report_customer_v1', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'customer_zs'){
            $data['customer_title'] = Customer::where('id',$filters['customer_id'])->first()->title ?? '';
            $pdf = Pdf::loadView('report.report_customer_zs', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'customer_zs_att'){
            $data['customer_title'] = Customer::where('id',$filters['customer_id'])->first()->title ?? '';
            $pdf = Pdf::loadView('report.report_customer_zs_att', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'customer_ss_att'){
            $data['customer_title'] = Customer::where('id',$filters['customer_id'])->first()->title ?? '';
            $pdf = Pdf::loadView('report.report_customer_ss_att', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'driver_zs'){
            $data['driver_name'] = Driver::where('id',$filters['driver_id'])->first()->name ?? '';
            $pdf = Pdf::loadView('report.report_driver_v1_zs', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'driver_zs_excel'){
            $data['driver_name'] = Driver::where('id',$filters['driver_id'])->first()->name ?? '';
            $pdf = Pdf::loadView('report.report_driver_v1_zs', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'customer_ss'){
            $data['customer_title'] = Customer::where('id',$filters['customer_id'])->first()->title ?? '';
            $pdf = Pdf::loadView('report.report_customer_ss', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }else if($type == 'driver_ss'){
            $data['driver_name'] = Driver::where('id',$filters['driver_id'])->first()->name ?? '';
            $pdf = Pdf::loadView('report.report_driver_v1_ss', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        }
        // dd($data);
        // 生成 PDF
        // 保存 PDF 到本地
        $fileName = 'Report_' .$records[0]->vendor_id .'_'. date('Ymd_His') . '_' . uniqid() . '.pdf';
        Log::info($fileName);
        $filePath = storage_path('app/public/driver_outputs/' . $fileName);
        $pdf->save($filePath);
        return $fileName;
        // dd($records, $filters);
    }
    public function generateImage($type, $record)
    {
        // type
        // 'driver_info' -> 司機資訊圖檔
        // 'dispatch1' -> 派車簽收單圖檔
        // 'dispatch2' -> 出租單圖檔
        // dispatch3 -> 龍興出租單
        // dd($record);
        if($type == 'driver_info'){
            $rs = $this->printDriverInfo($record);
        }else if($type == 'order_info'){
            $rs = $this->printOrderInfo($record);
        }else if($type == 'dispatch1'){
            $rs = $this->printDispatch1($record);
        }else if($type == 'dispatch2'){
            $rs = $this->printDispatch2($record);
        }else if($type == 'dispatch3'){
            $rs = $this->printDispatch3($record);
        }
        return $rs;
    }
    public function printDispatch3($record){
        // dd($record);
        $result = 'error';
        // $mystartdate = date('Y-m-d', strtotime($record['start_date']));
        // $mystarttime = date('H:i', strtotime($record['start_date']));
        $vendor = Vendor::where('id', '=', $record['vendor_id'])->first();
        if(!is_null($record['driver_id'])){
            $driver = Driver::where('id', '=', $record['driver_id'])->first();
            $car = Car::where('driver_id', '=', $record['driver_id'])->first();
        }
        $decodedData = json_decode($vendor->bg_position2, true);
        $background = imagecreatefromjpeg(public_path('images/bg/'.$vendor['bg_image2']));
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $fontPath2 = public_path('fonts/times.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $red = imagecolorallocate($background, 255, 87, 87);
        if($record['carno2'] != null || $record['carno2'] != ''){
            $carLicense = $record['carno2'];
        }else{
            $carLicense = $record['car']['license'];
        }
        if($record['location_type'] == 1){
            $customerAdd = $record['end_location'];
        }else if($record['location_type'] == 2){
            $customerAdd = $record['start_location'];
        }else{
            $customerAdd = $record['start_location'];
        }
        $dateConvert = static::convertToChineseDate($record['start_date'], $record['end_date']);

        $dispatch_id_dest_x = $decodedData['dispatch_id_dest_x'];
        $dispatch_id_dest_y = $decodedData['dispatch_id_dest_y'];
        $passenger_name_dest_x = $decodedData['passenger_name_dest_x'];
        $passenger_name_dest_y = $decodedData['passenger_name_dest_y'];
        $passenger_mobile_dest_x = $decodedData['passenger_mobile_dest_x'];
        $passenger_mobile_dest_y = $decodedData['passenger_mobile_dest_y'];
        $customer_addr_dist_x = $decodedData['customer_addr_dist_x'];
        $customer_addr_dist_y = $decodedData['customer_addr_dist_y'];
        $driver_name_dest_x = $decodedData['driver_name_dest_x'];
        $driver_name_dest_y = $decodedData['driver_name_dest_y'];
        $person_id_dest_x = $decodedData['person_id_dest_x'];
        $person_id_dest_y = $decodedData['person_id_dest_y'];
        $address_dest_x = $decodedData['address_dest_x'];
        $address_dest_y = $decodedData['address_dest_y'];
        $car_license_dest_x = $decodedData['car_license_dest_x'];
        $car_license_dest_y = $decodedData['car_license_dest_y'];
        $car_type_dest_x = $decodedData['car_type_dest_x'];
        $car_type_dest_y = $decodedData['car_type_dest_y'];
        $rental_cost_dest_x = $decodedData['rental_cost_x'];
        $rental_cost_dest_y = $decodedData['rental_cost_y'];
        $date_start_dest_x = $decodedData['date_start_dest_x'];
        $date_start_dest_y = $decodedData['date_start_dest_y'];
        $flight_no_x = $decodedData['flight_no_x'];
        $flight_no_y = $decodedData['flight_no_y'];

        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
        imagettftext($background, 30, 0, $passenger_name_dest_x, $passenger_name_dest_y, $black, $fontPath, $record['passenger_name']);
        imagettftext($background, 30, 0, $passenger_mobile_dest_x, $passenger_mobile_dest_y, $black, $fontPath, $record['passenger_mobile']);
        imagettftext($background, 30, 0, $customer_addr_dist_x, $customer_addr_dist_y, $black, $fontPath, $customerAdd);
        imagettftext($background, 30, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $record['driver_name']);
        imagettftext($background, 28, 0, $person_id_dest_x, $person_id_dest_y, $black, $fontPath, $record['driver']['person_id']);
        imagettftext($background, 30, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $record['rental_cost']);
        imagettftext($background, 28, 0, $date_start_dest_x, $date_start_dest_y, $black, $fontPath, $dateConvert);
        imagettftext($background, 28, 0, $date_start_dest_x, $date_start_dest_y, $black, $fontPath, $dateConvert);
        imagettftext($background, 30, 0, $flight_no_x, $flight_no_y, $black, $fontPath, $record['flight_no']);
        if($record['driver_name'] != ''){
            // dd($record['driver_name']);
            imagettftext($background, 30, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $record['driver_name']);
            imagettftext($background, 28, 0, $person_id_dest_x, $person_id_dest_y, $black, $fontPath, $record['driver']['person_id']);
            if(strlen($record['driver']['address']) > 26){
                $y = $address_dest_y-25;
                $maxWidth = 320;
                $lines = static::wrapText(20, 0, $fontPath, $record['driver']['address'], $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 20, 0, $address_dest_x, $y, $black, $fontPath, $line);
                    $y += 30;
                }
            }else{
                imagettftext($background, 30, 0, $address_dest_x, $address_dest_y, $black, $fontPath, $record['driver']['address']);
            }
            imagettftext($background, 28, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $carLicense);
            imagettftext($background, 30, 0, $car_type_dest_x, $car_type_dest_y, $black, $fontPath, $record['car']['brand']);
        }
        $outputFileName = 'lscar_'. date("YmdHis") . Str::random(4) . '.jpg';
        $outputFilePath = storage_path('app/public/lscar/' . $outputFileName);
        if (file_exists($outputFilePath)) {
            Log::info('檔案已存在: ' . $outputFilePath);
            unlink($outputFilePath);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'orders/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        return $result;
    }
    public function printOrderInfo($record){
        // dd($record);
        $result = 'error';
        $mystartdate = date('Y-m-d', strtotime($record['start_date']));
        $mystarttime = date('H:i', strtotime($record['start_date']));
        $vendor = Vendor::where('id', '=', $record['vendor_id'])->first();
        $driver = Driver::where('id', '=', $record['driver_id'])->first();
        $car = Car::where('driver_id', '=', $record['driver_id'])->first();
        $decodedData = json_decode($vendor->bg_order_position, true);
        // var_dump($car);die;
        $background = imagecreatefromjpeg(public_path('images/bg/'.$vendor['bg_order_template']));
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $fontPath2 = public_path('fonts/times.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $red = imagecolorallocate($background, 255, 87, 87);
        if($record['location_type'] == 1){
            $redBox = imagecreatetruecolor(150, 150);
            $red = imagecolorallocate($redBox, 255, 49,49);
            $white = imagecolorallocate($redBox, 255, 255, 255);
            imagefilledrectangle($redBox, 0, 0, 150, 150, $red);
            imagettftext($redBox, 100, 0, 10, 130, $white, $fontPath, '接');
            imagecopy($background, $redBox, 90, 16, 0, 0, 150, 150);
            imagedestroy($redBox);
        }else if($record['location_type'] == 2){
            $blueBox = imagecreatetruecolor(150, 150);
            $blue = imagecolorallocate($blueBox, 0, 74, 173);
            $white = imagecolorallocate($blueBox, 255, 255, 255);
            imagefilledrectangle($blueBox, 0, 0, 150, 150, $blue);
            imagettftext($blueBox, 100, 0, 10, 130, $white, $fontPath, '送');
            imagecopy($background, $blueBox, 90, 16, 0, 0, 150, 150);
            imagedestroy($blueBox);
        }

        $dispatch_id_dest_x = $decodedData['dispatch_id_dest_x'];
        $dispatch_id_dest_y = $decodedData['dispatch_id_dest_y'];
        $start_date_dest_x = $decodedData['start_date_dest_x'];
        $start_date_dest_y = $decodedData['start_date_dest_y'];
        $start_time_dest_x = $decodedData['start_time_dest_x'];
        $start_time_dest_y = $decodedData['start_time_dest_y'];
        $customer_title_dest_x = $decodedData['customer_title_dest_x'];
        $customer_title_dest_y = $decodedData['customer_title_dest_y'];
        $customer_department_x = $decodedData['customer_department_x'];
        $customer_department_y = $decodedData['customer_department_y'];
        $pay_type_x = $decodedData['pay_type_x'];
        $pay_type_y = $decodedData['pay_type_y'];
        $car_type_x = $decodedData['car_type_x'];
        $car_type_y = $decodedData['car_type_y'];
        $passenger_name_x = $decodedData['passenger_name_x'];
        $passenger_name_y = $decodedData['passenger_name_y'];
        $passenger_mobile_x = $decodedData['passenger_mobile_x'];
        $passenger_mobile_y = $decodedData['passenger_mobile_y'];
        $route_x = $decodedData['route_x'];
        $route_y = $decodedData['route_y'];
        $num_of_people_x = $decodedData['num_of_people_x'];
        $num_of_people_y = $decodedData['num_of_people_y'];
        $num_of_bags_x = $decodedData['num_of_bags_x'];
        $num_of_bags_y = $decodedData['num_of_bags_y'];
        $child_seat_x = $decodedData['child_seat_x'];
        $child_seat_y = $decodedData['child_seat_y'];
        $booster_pad_x = $decodedData['booster_pad_x'];
        $booster_pad_y = $decodedData['booster_pad_y'];
        $flight_no_x = $decodedData['flight_no_x'];
        $flight_no_y = $decodedData['flight_no_y'];
        $rental_cost_x = $decodedData['rental_cost_x'];
        $rental_cost_y = $decodedData['rental_cost_y'];
        $deposit_x = $decodedData['deposit_x'];
        $deposit_y = $decodedData['deposit_y'];
        $note_x = $decodedData['note_x'];
        $note_y = $decodedData['note_y'];
        // name
        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
        imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $red, $fontPath, $mystartdate);
        imagettftext($background, 40, 0, $start_time_dest_x, $start_time_dest_y, $red, $fontPath, $mystarttime);
        imagettftext($background, 40, 0, $customer_title_dest_x, $customer_title_dest_y, $black, $fontPath, $record['customer_title']);
        imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, empty($record['department_name']) ? '' : $record['department_name']);
        imagettftext($background, 40, 0, $pay_type_x, $pay_type_y, $red, $fontPath, empty($record['pay_type_name']) ? '' : $record['pay_type_name']);
        imagettftext($background, 40, 0, $car_type_x, $car_type_y, $black, $fontPath, empty($record['car_type_name']) ? '' : $record['car_type_name']);
        imagettftext($background, 40, 0, $passenger_name_x, $passenger_name_y, $black, $fontPath, empty($record['passenger_name']) ? '' : $record['passenger_name']);
        imagettftext($background, 40, 0, $passenger_mobile_x, $passenger_mobile_y, $black, $fontPath, empty($record['passenger_mobile']) ? '' : $record['passenger_mobile']);
        if(strlen($record['route']) > 36){
            $y = $route_y;
            $maxWidth = 1600;
            $lines = static::wrapText(48, 0, $fontPath, $record['route'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 32, 0, $route_x, $y, $black, $fontPath, $line);
                $y += 60;
            }
        }else{
            imagettftext($background, 48, 0, $route_x, $route_y, $black, $fontPath, empty($record['route']) ? '' : $record['route']);
        }
        // imagettftext($background, 40, 0, $route_x, $route_y, $black, $fontPath, empty($record['route']) ? '' : $record['route']);
        imagettftext($background, 40, 0, $num_of_people_x, $num_of_people_y, $black, $fontPath, empty($record['num_of_people']) ? '' : $record['num_of_people']);
        imagettftext($background, 40, 0, $num_of_bags_x, $num_of_bags_y, $black, $fontPath, empty($record['num_of_bags']) ? '' : $record['num_of_bags']);
        imagettftext($background, 40, 0, $child_seat_x, $child_seat_y, $black, $fontPath, empty($record['child_seat']) ? '' : $record['child_seat']);
        imagettftext($background, 40, 0, $booster_pad_x, $booster_pad_y, $black, $fontPath, empty($record['booster_pad']) ? '' : $record['booster_pad']);
        imagettftext($background, 40, 0, $flight_no_x, $flight_no_y, $red, $fontPath, empty($record['flight_no']) ? '' : $record['flight_no']);
        imagettftext($background, 40, 0, $rental_cost_x, $rental_cost_y, $black, $fontPath, empty($record['rental_cost']) ? '' : $record['rental_cost']);
        imagettftext($background, 40, 0, $deposit_x, $deposit_y, $black, $fontPath, empty($record['deposit']) ? '' : $record['deposit']);
        // imagettftext($background, 40, 0, $deposit_x, $deposit_y, $black, $fontPath, empty($record['note']) ? '' : $record['note']);
        if(strlen($record['note']) > 30){
            // Log::info('> 60');
            $y = $note_y-40;
            $maxWidth = 630;
            $lines = explode("\n", $record['note']);
            foreach ($lines as $line) {
                if(strlen($line) > 30){
                    $wrappedLines  = static::wrapText(30, 0, $fontPath, $line, $maxWidth);
                    foreach ($wrappedLines as $wrappedLine) {
                        imagettftext($background, 30, 0, $note_x, $y, $red, $fontPath, $wrappedLine);
                        $y += 40;
                    }
                }else {
                    // 如果行長度沒超過，直接顯示
                    imagettftext($background, 30, 0, $note_x, $y, $red, $fontPath, $line);
                    $y += 40;
                }
            }
        }else{
            imagettftext($background, 40, 0, $note_x, $note_y, $red, $fontPath, $record['note']);
        }
        $outputFileName = 'order_info_'. date("YmdHis") . Str::random(4) . '.jpg';
        $outputFilePath = storage_path('app/public/orders/' . $outputFileName);
        if (file_exists($outputFilePath)) {
            Log::info('檔案已存在: ' . $outputFilePath);
            unlink($outputFilePath);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'orders/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        return $result;
    }
    public function printDriverInfo($record)
    {
        // dd($record);
        $result = 'error';
        $vendor = Vendor::where('id', '=', $record['vendor_id'])->first();
        $car = Car::where('driver_id', '=', $record['id'])->first();
        $bg_position = $record['bg_position'];
        $photo = $record['driver_image'];
        $bg_driver_image = $vendor->bg_driver_template;
        $decodedData = json_decode($vendor->bg_driver_position, true);
        //create image
        $background = imagecreatefromjpeg(public_path('images/bg/'.$bg_driver_image));
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $fontPath2 = public_path('fonts/times.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $red = imagecolorallocate($background, 255, 87, 87);
        // name
        imagettftext($background, 30, 0, $decodedData['driver_name_x'], $decodedData['driver_name_y'], $black, $fontPath, $record['name']);
        // mobile
        imagettftext($background, 30, 0, $decodedData['driver_mobile_x'], $decodedData['driver_mobile_y'], $black, $fontPath, $record['mobile']);
        // car license
        imagettftext($background, 30, 0, $decodedData['car_licanse_x'], $decodedData['car_licanse_y'], $black, $fontPath, $car['license']);
        // car type
        imagettftext($background, 30, 0, $decodedData['car_type_x'], $decodedData['car_type_y'], $black, $fontPath, $car['brand'] . ' ' . $car['model']);
        imagettftext($background, 30, 0, $decodedData['car_color_x'], $decodedData['car_color_y'], $black, $fontPath, empty($car['color']) ? '' : $car['color']);
        // driver note
        imagettftext($background, 30, 0, $decodedData['driver_note_x'], $decodedData['driver_note_y'], $black, $fontPath, $record['note']);
        // 列印照片
        if(!empty($photo)){
            $photo_bg = storage_path('app/public/' . $photo);
            $photo = imagecreatefromjpeg($photo_bg);
            $target_width = 360;
            $target_height = 540;
            $photo_width = imagesx($photo);
            $photo_height = imagesy($photo);
            $scale = min($target_width / $photo_width, $target_height / $photo_height);
            $new_width = (int)($photo_width * $scale);
            $new_height = (int)($photo_height * $scale);
            $resized_photo = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_photo, false);
            imagesavealpha($resized_photo, false);
            imagecopyresampled(
                $resized_photo,  // 目标图像资源
                $photo,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $photo_width, $photo_height  // 源图像的宽度和高度
            );
            imagecopy($background, $resized_photo, 1150, 190, 0, 0, $new_width,$new_height);
        }
        $outputFileName = 'driver_info_'. $record->id . '.jpg';
        $outputFilePath = storage_path('app/public/drivers/' . $outputFileName);
        if (file_exists($outputFilePath)) {
            Log::info('檔案已存在: ' . $outputFilePath);
            unlink($outputFilePath);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'drivers/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        return $result;
    }
    public static function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = "";
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            }else{
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
    public static function formatChineseDate($datetime) {
        if (empty($datetime)) {
            return '';
        }
        // 创建 DateTime 对象
        $date = new DateTime($datetime);
        // 获取公历年份
        $year = $date->format('Y');
        // 将公历年份转换为民国年份
        $rocYear = $year - 1911;
        // 获取月份和日期
        $month = $date->format('n'); // 'n' 获取没有前导零的月份
        $day = $date->format('j'); // 'j' 获取没有前导零的日期
        $hour = $date->format('G'); // 'G' 获取24小时制的小时，范围是 0-23
        $minute = $date->format('i'); // 'i' 获取分钟，范围是 00-59

        // 构建中文日期时间字符串
        return sprintf(
            "%d 年 %d 月 %d 日 %d 時 %d 分",
            $rocYear,
            $month,
            $day,
            $hour,
            $minute
        );
    }
    public static function convertToChineseDate($start_datetime, $end_datetime) {
        // 定义一个内部函数来处理日期格式化
        // 格式化开始和结束时间
        $startFormatted = static::formatChineseDate($start_datetime);
        $endFormatted = static::formatChineseDate($end_datetime);

        // 如果开始或结束时间为空，返回空白
        if (empty($startFormatted) || empty($endFormatted)) {
            return '';
        }

        // 构建最终的中文时间区间字符串
        return sprintf(
            "自民國 %s 起至 %s止",
            $startFormatted,
            $endFormatted
        );
    }
}
