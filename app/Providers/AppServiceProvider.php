<?php

namespace App\Providers;

// use Filament\Pages\Actions\Action; // This might conflict with Filament\Actions\Action
use Filament\Actions\Action as FilamentAction; // Use the correct Action class with an alias
use Filament\Facades\Filament; // Correct Facade for v3
use Illuminate\Support\Facades\Blade;
use App\Livewire\TopbarDriverStatsButton; // Will be created in next steps
use Illuminate\Support\Facades\URL;
use Filament\Forms\Components\Field;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // if(config('app.env') === 'production') {
        //     $this->app['request']->server->set('HTTPS', true);
        // }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if($this->app->environment('production')) {
            URL::forceScheme('https');
        }
        // Keep existing Field macro if it's used
        // Field::macro("tooltip", function(string $tooltip) { // Temporarily commented out
        //     // Ensure the Action here is Filament\Pages\Actions\Action if that's intended
        //     // For safety, using the full namespace or a different alias if Page Actions are different
        //     return $this->hintAction(
        //         \Filament\Pages\Actions\Action::make('help')
        //             ->icon('heroicon-o-question-mark-circle')
        //             ->extraAttributes(["class" => "text-gray-500"])
        //             ->label("")
        //             ->tooltip($tooltip)
        //     );
        // });

        // Panel Actions are now defined within the TopbarDriverStatsButton Livewire component.
        // No longer need to register them globally here if they are self-contained in that button component.

        // Register Render Hook to display the TopbarDriverStatsButton Livewire component
        Filament::registerRenderHook(
            'panels::global-search.before', // Hook to render before the global search input
            fn (): string => Blade::render('@livewire(\''.TopbarDriverStatsButton::class.'\')')
        );
    }

}
