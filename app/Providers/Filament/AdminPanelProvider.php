<?php

namespace App\Providers\Filament;
use Filament\Facades\Filament;

use Filament\Pages;
use Filament\Panel;
use Filament\Widgets;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Support\Facades\FilamentIcon;
use App\Livewire\DriverMonthlyStatsWidget; // 新增 Livewire 元件的 use 語句
use Filament\Navigation\NavigationItem;    // 新增 NavigationItem 的 use 語句
use Illuminate\Support\Facades\Blade;      // 新增 Blade Facade
use Filament\Actions\Action as FilamentAction; // 新增 Action 的 use 語句並使用別名
// App\Livewire\TopbarDriverStatsButton will be used in AppServiceProvider
// Filament\Support\Facades\Filament as FilamentFacade will be used in AppServiceProvider
use App\Http\Middleware\SecurityHeaders;
use Filament\Http\Middleware\Authenticate;
use Rmsramos\Activitylog\ActivitylogPlugin;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Saade\FilamentFullCalendar\FilamentFullCalendarPlugin;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;

class AdminPanelProvider extends PanelProvider
{
    // boot() method and its contents are moved to AppServiceProvider

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->colors([
                'primary' => Color::Indigo,
            ])
            ->favicon(asset('images/favicon-32x32.png'))
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
                \App\Filament\Widgets\StatsOverview::class,
                \App\Filament\Widgets\CalendarWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                // SecurityHeaders::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                FilamentFullCalendarPlugin::make(),
                ActivitylogPlugin::make()
                    ->navigationSort(13)
                    ->authorize(
                        fn () => Filament::auth()->user()->id === 1
                    ),
            ])
            ->icons([
                'actions::copy' => 'heroicon-o-clipboard',
            ])
            // renderHook logic will be handled by AppServiceProvider and TopbarDriverStatsButton Livewire component
            ->databaseNotifications()
            ->maxContentWidth(MaxWidth::Full)
            ->sidebarFullyCollapsibleOnDesktop()
            ->sidebarWidth('11rem');
    }
}
