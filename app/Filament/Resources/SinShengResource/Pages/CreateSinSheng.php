<?php

namespace App\Filament\Resources\SinShengResource\Pages;

use App\Filament\Resources\SinShengResource;
use App\Models\Car;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Department;
use App\Models\Dispatch;
use App\Models\Driver;
use App\Traits\CreateMessageText;
use Carbon\Carbon;
use Filament\Resources\Pages\CreateRecord;
use Filament\Actions;
use Illuminate\Support\Facades\Log;

class CreateSinSheng extends CreateRecord
{
    use CreateMessageText;

    protected static string $resource = SinShengResource::class;
    protected static ?string $title = '建立派車單';

    public function getBreadcrumbs(): array
    {
        return [
            url('/admin/sin-sheng') => '列表',
        ];
    }

    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }

    // /home/<USER>/car-backend-admin/app/Filament/Resources/SinSengResource/Pages/CreateSinSeng.php
    protected function handleRecordCreation(array $data): Dispatch
    {
        Log::info('[CreateSinSeng - handleRecordCreation] Received data for main dispatch creation:', $data);

        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        /** @var Dispatch $returnRs */
        $returnRs = Dispatch::create($data);  // 主派車單創建
        Log::info('[CreateSinSeng - MainDispatch] Main dispatch created with ID: ' . $returnRs->id);

        // 主派車單的額外處理 (drivername2 - 雖然這裡的賦值不會影響 $dispatch，但保留原始邏輯)
        if (isset($data['drivername2']) && $data['drivername2'] != null) {
            // This part seems to intend to use drivername2, but doesn't assign it to the $dispatch model directly.
            // If $data['drivername2'] (which is an ID) should be used for something on $dispatch,
            // it needs to be explicitly handled, e.g. $dispatch->some_field = $driverNameValue;
            // For now, just logging.
            Log::info('[CreateSinSeng - MainDispatch] drivername2 was present in form data.', ['drivername2_id' => $data['drivername2']]);
        }

        // --- 主派車單 route 和 copy_text_1 計算 ---
        $mainDispatchRouteStartDisplay = $returnRs->start_location;  // May be code or text
        $mainDispatchRouteEndDisplay = $returnRs->end_location;  // May be code or text
        $mainTmpStartLocationForMsg = $returnRs->start_location;  // For message, might be airport name
        $mainTmpEndLocationForMsg = $returnRs->end_location;  // For message, might be airport name

        if ($returnRs->location_type == '1') {  // 主單是「接機」 (start is airport)
            if (is_numeric($returnRs->start_location)) {
                $mainDispatchRouteStartDisplay = $this->getAirportName($returnRs->start_location);
                $mainTmpStartLocationForMsg = $mainDispatchRouteStartDisplay;  // Use name for message
            }
            // $dispatch->end_location should be text
        } elseif ($returnRs->location_type == '2') {  // 主單是「送機」 (end is airport)
            // $dispatch->start_location should be text
            if (is_numeric($returnRs->end_location)) {
                $mainDispatchRouteEndDisplay = $this->getAirportName($returnRs->end_location);
                $mainTmpEndLocationForMsg = $mainDispatchRouteEndDisplay;  // Use name for message
            }
        }
        // For other types, assume both start and end are already text for route and message
        if (!empty($mainDispatchRouteStartDisplay) &&
                $mainDispatchRouteStartDisplay !== '資料錯誤!' &&
                !empty($mainDispatchRouteEndDisplay) &&
                $mainDispatchRouteEndDisplay !== '資料錯誤!') {
            $returnRs->route = $mainDispatchRouteStartDisplay . ' >> ' . $mainDispatchRouteEndDisplay;
        } else {
            $returnRs->route = null;  // Or a placeholder like '路線資訊不完整'
            Log::warning('[CreateSinSeng - MainDispatch] Route for main dispatch ID ' . $returnRs->id . ' is incomplete or has errors.', [
                'start_raw' => $returnRs->start_location, 'end_raw' => $returnRs->end_location,
                'start_display' => $mainDispatchRouteStartDisplay, 'end_display' => $mainDispatchRouteEndDisplay
            ]);
        }
        Log::info('[CreateSinSeng - MainDispatch] Calculated route for main dispatch ID ' . $returnRs->id . ':', ['route' => $returnRs->route]);

        // 主派車單的 msgDispatch 和 copy_text_1
        $mainOtherTripData = $returnRs->pickup_and_dropoff_location ?? [];
        $msgDispatchMain = [
            'car_model' => Cartype::where('id', $returnRs->cartype_id)->first()->short_name ?? '',
            'ride_date' => Carbon::parse($returnRs->start_date)->toDateString(),
            'ride_time' => Carbon::parse($returnRs->start_date)->toTimeString(),  // Ensure correct time format
            'booking_type' => static::getBookingTypeName($returnRs->location_type),
            'start_location' => $mainTmpStartLocationForMsg,  // Use potentially converted name
            'passenger_name' => $returnRs->passenger_name,
            'passenger_mobile' => $returnRs->passenger_mobile,
            'end_location' => $mainTmpEndLocationForMsg,  // Use potentially converted name
            'fare_amount' => ($returnRs->pay_type == 1) ? $returnRs->rental_cost : '--',
            'flight_no' => $returnRs->flight_no,
            'otherTrip' => $mainOtherTripData,
            'location_type' => $returnRs->location_type,
            'vendor_id' => $returnRs->vendor_id,
            'return' => 0  // Main trip is not a return
        ];
        $returnRs->copy_text_1 = static::generateMessage($msgDispatchMain);
        Log::info('[CreateSinSheng - MainDispatch] Generated copy_text_1 for main dispatch ID ' . $returnRs->id);

        // Common temporary properties for image generation
        $cartype = Cartype::find($returnRs->cartype_id);
        $returnRs->car_type_name = $cartype?->name ?? '';

        $returnRs->pay_type_name = $this->getPayTypeName($returnRs->pay_type);

        $customer = Customer::find($returnRs->customer_id);
        $returnRs->customer_title = $customer?->title ?? '';

        if ($returnRs->department_id) {
            $department = Department::find($returnRs->department_id);
            $returnRs->department_name = $department?->name ?? '';
        } else {
            $returnRs->department_name = '';
        }

        $returnRs->tmpPickupLocationStr = !empty($returnRs->pickup_and_dropoff_location) ? $this->transPickupStr($returnRs->pickup_and_dropoff_location) : null;

        // Image generation logic
        if (isset($returnRs->driver_id) && $returnRs->driver_id != null) {
            Log::info('[CreateSinSheng - MainDispatch] Main dispatch ID ' . $returnRs->id . ' has driver, preparing for image generation.');
            $driver = Driver::find($returnRs->driver_id);
            if ($driver) {
                $car = Car::where('driver_id', $returnRs->driver_id)->first();

                // 臨時屬性 for image generation
                $returnRs->driver_name = $driver->name ?? '';
                $returnRs->car_license = $car->license ?? '';
                $returnRs->driver = $driver;
                $returnRs->car = $car;

                if (method_exists(Dispatch::class, 'generateImageV3')) {
                    try {
                        $rs = Dispatch::generateImageV3('dispatch1', $returnRs);
                        $rs2 = Dispatch::generateImageV3('dispatch2', $returnRs);
                        if ($rs && $rs !== 'error')
                            $returnRs->image_path = $rs;
                        else
                            $returnRs->image_path = null;
                        if ($rs2 && $rs2 !== 'error')
                            $returnRs->image2_path = $rs2;
                        else
                            $returnRs->image2_path = null;
                        Log::info('[CreateSinSeng - MainDispatch] Image paths for main dispatch ID ' . $returnRs->id . ':', ['img1' => $returnRs->image_path, 'img2' => $returnRs->image2_path]);
                    } catch (\Exception $e) {
                        Log::error('[CreateSinSeng - MainDispatch] Exception during image generation for main dispatch ID ' . $returnRs->id . ': ' . $e->getMessage());
                        $returnRs->image_path = null;
                        $returnRs->image2_path = null;
                    }
                } else {
                    Log::error('[CreateSinSeng - MainDispatch] generateImageV3 method not found in Dispatch model.');
                }
            } else {
                Log::warning('[CreateSinSeng - MainDispatch] Driver not found for main dispatch ID ' . $returnRs->id, ['driver_id' => $returnRs->driver_id]);
                // Still attempt to generate dispatch1 as if no driver
                $returnRs->driver_name = '';
                $returnRs->car_license = '';
                $returnRs->driver = null;
                $returnRs->car = null;
                if (method_exists(Dispatch::class, 'generateImageV3')) {
                    try {
                        $rs = Dispatch::generateImageV3('dispatch1', $returnRs);
                        $returnRs->image_path = ($rs && $rs !== 'error') ? $rs : null;
                        Log::info('[CreateSinSeng - MainDispatch] Image path (driver not found, fallback) for dispatch ID ' . $returnRs->id . ':', ['img1' => $returnRs->image_path]);
                    } catch (\Exception $e) {
                        Log::error('[CreateSinSeng - MainDispatch] Exception during image generation (driver not found, fallback) for dispatch ID ' . $returnRs->id . ': ' . $e->getMessage());
                        $returnRs->image_path = null;
                    }
                }
                $returnRs->image_path = null;
                $returnRs->image2_path = null;
            }
        } else {
            Log::info('[CreateSinSeng - MainDispatch] No driver for main dispatch ID ' . $returnRs->id . '. Generating dispatch1 only.');
            $returnRs->driver_name = '';
            $returnRs->car_license = '';
            $returnRs->driver = null;
            $returnRs->car = null;
            if (method_exists(Dispatch::class, 'generateImageV3')) {
                try {
                    $rs = Dispatch::generateImageV3('dispatch1', $returnRs);
                    $returnRs->image_path = ($rs && $rs !== 'error') ? $rs : null;
                    Log::info('[CreateSinSeng - MainDispatch] Image path (no driver) for dispatch ID ' . $returnRs->id . ':', ['img1' => $returnRs->image_path]);
                } catch (\Exception $e) {
                    Log::error('[CreateSinSeng - MainDispatch] Exception during image generation (no driver) for dispatch ID ' . $returnRs->id . ': ' . $e->getMessage());
                    $returnRs->image_path = null;
                }
            }
            $returnRs->image2_path = null;
        }
        unset($returnRs->driver_name, $returnRs->car_license, $returnRs->driver, $returnRs->car);
        unset($returnRs->customer_title, $returnRs->department_name, $returnRs->tmpPickupLocationStr);
        unset($returnRs->car_type_name, $returnRs->pay_type_name);
        $returnRs->save();  // 儲存主派車單的所有更新 (route, copy_text_1, image_paths)
        Log::info('[CreateSinSeng - MainDispatch] Main dispatch ID ' . $returnRs->id . ' saved after all processing.');

        // 檢查是否需要建立回程單 (不論是否有司機)
        if (isset($data['return']) && $data['return'] == 1) {
            Log::info('[CreateSinSeng - ReturnTrip] Initiating return trip creation for main dispatch ID: ' . $returnRs->id);
            // Log original dispatch state AFTER it has been fully processed and saved, for accurate reference
            Log::info('[CreateSinSeng - ReturnTrip] Original (main) dispatch data for return reference:', Dispatch::find($returnRs->id)->toArray());
            Log::info('[CreateSinSeng - ReturnTrip] Form data for return reference:', $data);

            $return_location_type = $this->getReturnLocationType($data['location_type']);  // Original type from $data
            $return_rental_cost = $data['return_rental_cost'] ?? null;
            $return_date = $data['return_date'] ?? null;
            $return_flight_no = $data['return_flight_no'] ?? null;
            $return_driver_fee = $data['return_driver_fee'] ?? null;

            if (!$return_date) {  // Removed check for $return_rental_cost === null
                Log::error('[CreateSinSeng - ReturnTrip] Skipped return trip creation for main dispatch ID ' . $returnRs->id . ': Missing return_date. (Note: return_rental_cost is optional).');
            } else {
                $originalPassengerName = $returnRs->passenger_name;
                $originalPassengerMobile = $returnRs->passenger_mobile;

                // Get raw start/end locations from the ALREADY SAVED main dispatch
                $rawOriginalStartLocation = $returnRs->start_location;
                $rawOriginalEndLocation = $returnRs->end_location;
                Log::info('[CreateSinSeng - ReturnTrip] Raw original locations from main dispatch ID ' . $returnRs->id . ':', ['start' => $rawOriginalStartLocation, 'end' => $rawOriginalEndLocation, 'original_location_type' => $returnRs->location_type]);

                $returnPassengerName = $originalPassengerName;
                $returnPassengerMobile = $originalPassengerMobile;

                // Default swap for return trip: original end becomes return start, original start becomes return end.
                // These are RAW values (can be codes or text).
                $returnStartLocation = $rawOriginalEndLocation;
                $returnEndLocation = $rawOriginalStartLocation;
                Log::info('[CreateSinSeng - ReturnTrip] Default swapped RAW locations for return:', ['start' => $returnStartLocation, 'end' => $returnEndLocation]);

                $originalPickupLocations = $returnRs->pickup_and_dropoff_location ?? [];
                $returnPickupLocations = [];  // Initialize for the return trip

                if (!empty($originalPickupLocations) && is_array($originalPickupLocations)) {
                    Log::info('[CreateSinSeng - ReturnTrip] Handling pickup_and_dropoff_location for return trip based on original trip type: ' . $returnRs->location_type);
                    $tempOriginalPickupLocations = $originalPickupLocations;  // Copy for manipulation
                    $lastLocationData = array_pop($tempOriginalPickupLocations);

                    $returnPassengerName = $lastLocationData['name'];
                    $returnPassengerMobile = $lastLocationData['mobile'];
                    $returnPickupLocations = $tempOriginalPickupLocations;  // Remaining stops

                    $originalPassengerLocationForReturnStop = '';

                    if ($returnRs->location_type == '1') {  // Original was PICKUP (接機 Airport -> Text), return is DROPOFF (Text -> Airport)
                        $returnStartLocation = $lastLocationData['location'];  // Return starts from the last multi-stop location (text)
                        $returnEndLocation = $rawOriginalStartLocation;  // Return ends at the original airport (CODE)
                        $originalPassengerLocationForReturnStop = $rawOriginalEndLocation;  // Original main passenger's location (original end, text)
                        Log::info('[CreateSinSeng - ReturnTrip] Original (接機). Return (送機) multi-stop locations:', ['start' => $returnStartLocation, 'end_code' => $returnEndLocation, 'orig_psg_stop' => $originalPassengerLocationForReturnStop]);
                    } else if ($returnRs->location_type == '2') {  // Original was DROPOFF (送機 Text -> Airport), return is PICKUP (Airport -> Text)
                        $returnStartLocation = $rawOriginalEndLocation;  // Return starts from the original airport (CODE)
                        $returnEndLocation = $lastLocationData['location'];  // Return ends at the last multi-stop location (text)
                        $originalPassengerLocationForReturnStop = $rawOriginalStartLocation;  // Original main passenger's location (original start, text)
                        Log::info('[CreateSinSeng - ReturnTrip] Original (送機). Return (接機) multi-stop locations:', ['start_code' => $returnStartLocation, 'end' => $returnEndLocation, 'orig_psg_stop' => $originalPassengerLocationForReturnStop]);
                    } else {  // Other types (e.g., 單程 Text -> Text), return is (Text -> Text)
                        $returnStartLocation = $lastLocationData['location'];
                        $returnEndLocation = $rawOriginalStartLocation;  // Or $rawOriginalStartLocation if that's the text
                        $originalPassengerLocationForReturnStop = $rawOriginalEndLocation;  // Or $rawOriginalEndLocation
                        Log::info('[CreateSinSeng - ReturnTrip] Original (OTHER). Return multi-stop locations:', ['start' => $returnStartLocation, 'end' => $returnEndLocation, 'orig_psg_stop' => $originalPassengerLocationForReturnStop]);
                    }
                    $returnPickupLocations[] = [
                        'name' => $originalPassengerName,
                        'mobile' => $originalPassengerMobile,
                        'location' => $originalPassengerLocationForReturnStop
                    ];
                } else {
                    Log::info('[CreateSinSeng - ReturnTrip] No multi-stop for original trip. Using default swapped RAW locations for return.');
                }

                $returnData = [
                    'start_date' => $return_date,
                    'end_date' => Carbon::parse($return_date)->addHour(),
                    'customer_id' => $returnRs->customer_id,
                    'driver_id' => $returnRs->driver_id,
                    'vendor_id' => $returnRs->vendor_id,
                    'department_id' => $returnRs->department_id,
                    'passenger_name' => $returnPassengerName,
                    'passenger_mobile' => $returnPassengerMobile,
                    'location_type' => $return_location_type,  // This is the type FOR THE RETURN TRIP
                    'start_location' => $returnStartLocation,  // This is the RAW start for return (code or text)
                    'end_location' => $returnEndLocation,  // This is the RAW end for return (code or text)
                    'flight_no' => $return_flight_no,
                    'cartype_id' => $returnRs->cartype_id,
                    'pay_type' => $returnRs->pay_type,
                    'num_of_people' => $returnRs->num_of_people,
                    'num_of_bags' => $returnRs->num_of_bags,
                    'child_seat' => $returnRs->child_seat,
                    'booster_pad' => $returnRs->booster_pad,
                    'status' => $returnRs->driver_id ? 1 : 0,
                    'rental_cost' => (int) $return_rental_cost,
                    'return' => 1,
                    'pickup_and_dropoff_location' => $returnPickupLocations,
                    'note' => $returnRs->note,
                    'driver_note' => $returnRs->driver_note,
                    'row_color' => $returnRs->row_color,
                    'driver_fee' => $return_driver_fee,
                    // route will be calculated after creation
                ];
                Log::info('[CreateSinSeng - ReturnTrip] Data for creating return dispatch (before create):', $returnData);

                /** @var Dispatch $returnRs */
                $returnRs = Dispatch::create($returnData);
                Log::info('[CreateSinSeng - ReturnTrip] Return dispatch created with ID: ' . ($returnRs->id ?? 'null'));

                if ($returnRs) {
                    // --- 為回程單計算 route ---
                    $returnRouteStartDisplay = $returnRs->start_location;  // Raw value from $returnRs
                    $returnRouteEndDisplay = $returnRs->end_location;  // Raw value from $returnRs

                    // Now, use $returnRs->location_type to determine which part is airport for display
                    if ($returnRs->location_type == '1') {  // 回程是「接機」 (returnRs->start_location is airport CODE)
                        if (is_numeric($returnRs->start_location)) {
                            $returnRouteStartDisplay = $this->getAirportName($returnRs->start_location);
                        }
                        // $returnRs->end_location should be text here
                    } elseif ($returnRs->location_type == '2') {  // 回程是「送機」 (returnRs->end_location is airport CODE)
                        // $returnRs->start_location should be text here
                        if (is_numeric($returnRs->end_location)) {
                            $returnRouteEndDisplay = $this->getAirportName($returnRs->end_location);
                        }
                    }
                    // For other types, assume both start and end are already text

                    if (!empty($returnRouteStartDisplay) &&
                            $returnRouteStartDisplay !== '資料錯誤!' &&
                            !empty($returnRouteEndDisplay) &&
                            $returnRouteEndDisplay !== '資料錯誤!') {
                        $returnRs->route = $returnRouteStartDisplay . ' >> ' . $returnRouteEndDisplay;
                    } else {
                        $returnRs->route = null;
                        Log::warning('[CreateSinSeng - ReturnTrip] Route for return dispatch ID ' . $returnRs->id . ' is incomplete or has errors.', [
                            'start_raw' => $returnRs->start_location, 'end_raw' => $returnRs->end_location,
                            'start_display_for_route' => $returnRouteStartDisplay, 'end_display_for_route' => $returnRouteEndDisplay,
                            'return_location_type' => $returnRs->location_type
                        ]);
                    }
                    Log::info('[CreateSinSeng - ReturnTrip] Calculated route for return dispatch ID ' . $returnRs->id . ':', ['route' => $returnRs->route]);

                    // --- 為回程單準備訊息文字 copy_text_1 ---
                    // Use the $returnRouteStartDisplay and $returnRouteEndDisplay that were just prepared for the route
                    $msgDispatchForReturn = [
                        'car_model' => Cartype::where('id', $returnRs->cartype_id)->first()->short_name ?? '',
                        'ride_date' => Carbon::parse($returnRs->start_date)->toDateString(),
                        'ride_time' => Carbon::parse($returnRs->start_date)->format('H:i'),
                        'booking_type' => static::getBookingTypeName($returnRs->location_type),
                        'start_location' => $returnRouteStartDisplay,  // Display name
                        'passenger_name' => $returnRs->passenger_name,
                        'passenger_mobile' => $returnRs->passenger_mobile,
                        'end_location' => $returnRouteEndDisplay,  // Display name
                        'fare_amount' => ($returnRs->pay_type == 1) ? $returnRs->rental_cost : '--',
                        'flight_no' => $returnRs->flight_no,
                        'otherTrip' => $returnRs->pickup_and_dropoff_location ?? [],
                        'location_type' => $returnRs->location_type,
                        'vendor_id' => $returnRs->vendor_id,
                        'return' => 1
                    ];
                    $returnRs->copy_text_1 = static::generateMessage($msgDispatchForReturn);
                    Log::info('[CreateSinSeng - ReturnTrip] Generated copy_text_1 for return dispatch ID ' . $returnRs->id);

                    // --- 產生回程單圖片 (如果需要且有司機) ---
                    if ($returnRs->driver_id) {
                        Log::info('[CreateSinSeng - ReturnTrip] Generating images for return dispatch ID ' . $returnRs->id . ' as driver is assigned.');
                        $returnDriver = Driver::find($returnRs->driver_id);
                        if ($returnDriver) {
                            $returnCar = Car::where('driver_id', $returnRs->driver_id)->first();
                            $returnCustomer = Customer::find($returnRs->customer_id);
                            $returnDepartment = $returnRs->department_id ? Department::find($returnRs->department_id) : null;

                            $returnRs->driver_name = $returnDriver->name ?? '';
                            $returnRs->car_license = $returnCar->license ?? '';
                            $returnRs->driver = $returnDriver;
                            $returnRs->car = $returnCar;
                            $returnRs->customer_title = $returnCustomer->title ?? '';
                            $returnRs->department_name = $returnDepartment?->name ?? '';
                            if (!empty($returnRs->pickup_and_dropoff_location)) {
                                $returnRs->tmpPickupLocationStr = $this->transPickupStr($returnRs->pickup_and_dropoff_location);
                            }

                            if (method_exists(Dispatch::class, 'generateImageV3')) {
                                try {
                                    $returnImg1 = Dispatch::generateImageV3('dispatch1', $returnRs);
                                    $returnImg2 = Dispatch::generateImageV3('dispatch2', $returnRs);
                                    if ($returnImg1 && $returnImg1 !== 'error')
                                        $returnRs->image_path = $returnImg1;
                                    else
                                        $returnRs->image_path = null;
                                    if ($returnImg2 && $returnImg2 !== 'error')
                                        $returnRs->image2_path = $returnImg2;
                                    else
                                        $returnRs->image2_path = null;
                                    Log::info('[CreateSinSeng - ReturnTrip] Image paths for return dispatch ID ' . $returnRs->id . ':', ['img1' => $returnRs->image_path, 'img2' => $returnRs->image2_path]);
                                } catch (\Exception $e) {
                                    Log::error('[CreateSinSeng - ReturnTrip] Exception during image generation for return dispatch ID ' . $returnRs->id . ': ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
                                    $returnRs->image_path = null;
                                    $returnRs->image2_path = null;
                                }
                            } else {
                                Log::error('[CreateSinSeng - ReturnTrip] generateImageV3 method not found in Dispatch model for return trip.');
                            }
                            unset($returnRs->driver_name, $returnRs->car_license, $returnRs->driver, $returnRs->car, $returnRs->customer_title, $returnRs->department_name, $returnRs->tmpPickupLocationStr);
                        } else {
                            
                            Log::error('[CreateSinSeng - ReturnTrip] Driver not found for return dispatch ID ' . $returnRs->id, ['driver_id' => $returnRs->driver_id]);
                            $returnRs->image_path = null;
                            $returnRs->image2_path = null;
                        }
                    } else {
                        $returnCustomer = Customer::find($returnRs->customer_id);
                        $returnDepartment = $returnRs->department_id ? Department::find($returnRs->department_id) : null;
                        $returnRs->customer_title = $returnCustomer->title ?? '';
                        $returnRs->department_name = $returnDepartment?->name ?? '';
                        $returnRs->driver_name = '';
                        $returnRs->car_license = '';
                        $returnRs->driver = null;
                        $returnRs->car = null;
                        if (method_exists(Dispatch::class, 'generateImageV3')) {
                            try {
                                $rs = Dispatch::generateImageV3('dispatch1', $returnRs);
                                $returnRs->image_path = ($rs && $rs !== 'error') ? $rs : null;
                                Log::info('[CreateSinSeng - MainDispatch] Image path (no driver) for dispatch ID ' . $returnRs->id . ':', ['img1' => $returnRs->image_path]);
                            } catch (\Exception $e) {
                                Log::error('[CreateSinSeng - MainDispatch] Exception during image generation (no driver) for dispatch ID ' . $returnRs->id . ': ' . $e->getMessage());
                                $returnRs->image_path = null;
                            }
                        }
                        Log::info('[CreateSinSeng - ReturnTrip] 000 -- No driver assigned for return dispatch ID ' . $returnRs->id . '. Setting images to null.');
                        $returnRs->image2_path = null;
                        unset($returnRs->driver_name, $returnRs->car_license, $returnRs->driver, $returnRs->car, $returnRs->customer_title, $returnRs->department_name, $returnRs->tmpPickupLocationStr);

                    }
                    $returnRs->save();  // 儲存所有對回程單的修改 (route, copy_text_1, image_paths)
                    Log::info('[CreateSinSeng - ReturnTrip] Return dispatch ID ' . $returnRs->id . ' saved with all updates.');
                } else {
                    Log::error('[CreateSinSeng - ReturnTrip] Failed to create return dispatch record for main dispatch ID ' . $returnRs->id);
                }
            }
        }

        return $returnRs;
    }

    public function transPickupStr($data)
    {
        $result = [];
        foreach ($data as $index => $person) {
            $result[] = ($index + 1) . '.姓名:' . $person['name'] . '，電話:' . $person['mobile'] . "\n地址:" . $person['location'] . "\n";
        }
        $output = implode(" \n", $result);
        return $output;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd($data);
        // 自定義邏輯
        return $data;
    }

    public function getAirportName($data)
    {
        if ($data == 1) {
            return '松山機場 TSA';
        } else if ($data == 2) {
            return '桃園機場 T1';
        } else if ($data == 3) {
            return '桃園機場 T2';
        } else if ($data == 4) {
            return '桃園機場';
        } else if ($data == 5) {
            return '台中機場';
        }
        return '資料錯誤!';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getReturnLocationType($data)
    {
        if ($data == 1) {
            return 2;
        } else if ($data == 2) {
            return 1;
        } else if ($data == 3) {
            return 3;
        } else if ($data == 4) {
            return 4;
        } else if ($data == 0) {
            return 0;
        }
    }

    public function getTrip($data)
    {
        // if($data==1){
        //     return '桃園機場 T1';
        // }else if($data==2){
        //     return '桃園機場 T2';
        // }else if($data==3){
        //     return '桃園機場';
        // }else if($data==4){
        //     return '台中機場';
        // }
        return '台北車站';
    }

    public function generatePickupAndDropoffLocation($pickupAndDropoffLocation)
    {
        if ($pickupAndDropoffLocation == null) {
            return null;
        }
        if (is_string($pickupAndDropoffLocation)) {
            $pickupAndDropoffLocation = json_decode($pickupAndDropoffLocation, true);
        }
        // Check if it's an array and reverse it
        if (is_array($pickupAndDropoffLocation)) {
            return array_reverse($pickupAndDropoffLocation);
        }
        // If it's not an array or null, return the original value (or handle it as needed)
        return $pickupAndDropoffLocation;
    }

    public function getPayTypeName($data)
    {
        if ($data == 0) {
            return '月結';
        } else if ($data == 1) {
            return '現金';
        } else if ($data == 2) {
            return '匯款';
        } else if ($data == 3) {
            return '刷卡';
        }
        return '資料錯誤!';
    }
}
