<?php

namespace App\Filament\Resources\SinShengResource\Pages;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use App\Traits\CreateMessageText;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\SinShengResource;

class EditSinSheng extends EditRecord
{
    use CreateMessageText;
    protected static string $resource = SinShengResource::class;
    protected static ?string $title = '編輯派車簽單';
    public ?string $returnQueryString = null;
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function getRedirectUrl(): string
    {
        // This should now correctly use the stored query string
        $query = $this->returnQueryString;
        // Log::info('Redirect Query: ' . ($query ?? 'NULL'));
        return static::getResource()::getUrl('index') . ($query ? ('?' . $query) : '');
    }

    protected function getFormAction(): string
    {
        // 保留 query string
        return request()->url() . (request()->getQueryString() ? '?' . request()->getQueryString() : '');
    }
    public function mount($record): void
    {
        parent::mount($record);
        // This should now correctly capture the query string passed in the URL
        $this->returnQueryString = request()->getQueryString();
        // Log::info('Mount Query String: ' . $this->returnQueryString); // Verify this log now shows the query string
    }
    public function handleRecordUpdate(Model $record, array $data):Dispatch
    {
        // dd(Session::get(key: 'filters'));
        $filters = [];
        $queryParameter = Request::query();
        foreach ($queryParameter as $key => $value) {
            if (strpos($key, 'tableFilters') === 0) {
                $filters[$key] = $value;
            }
        }
        Session::put('filters', $filters);
        // dd(Session::get(key: 'filters'));
        // if(auth()->user()->vendor_id == 4){
        //     $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addMinute();
        // }else{
            $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        // }
        $dispatch = Dispatch::find($record->id);
        $dispatch->update($data);
        $dispatch->save();
        $dispatch->car_type_name = Cartype::where('id', $data['cartype_id'])->first()->name;
        $dispatch->pay_type_name = $this->getPayTypeName($data['pay_type']);

        if(isset($dispatch->driver_id) && $dispatch->driver_id != null){
            // dd($dispatch);
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = $driver?->name;
            $dispatch->driver = $driver;
            $dispatch->car = $car;
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id){
                $department = Department::find($dispatch->department_id);
                $dispatch->department_name = $department?->name ?? '';
            } else {
                $dispatch->department_name = '';
            }
            $otherTrip = [];
            $dispatch->car_license = $car?->license;
            if($data['location_type'] == 1){
                $tmpStartLocation = $this->getAirportName( $dispatch->start_location);
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $this->getAirportName( $dispatch->end_location);
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            if($dispatch->signature_path != null){
                $signature = storage_path('app/public/' .$dispatch->signature_path);
            }
            else{
                $signature = null;
            }
            // 簽單多筆上下車地點
            if($dispatch->pickup_and_dropoff_location != null){
                $otherTrip = $dispatch->pickup_and_dropoff_location;
                $dispatch->tmpPickupLocationStr = $this->transPickupStr($dispatch->pickup_and_dropoff_location);
            }
            $msgDispatch = [
                'car_model' => Cartype::where('id', $dispatch->cartype_id)->first()->short_name, //$dispatch->cartype_id,
                'ride_date' => Carbon::parse($dispatch->start_date)->toDateString(),
                'ride_time' => Carbon::parse($dispatch->start_date)->format('H:i'),
                'booking_type' => static::getBookingTypeName($dispatch->location_type),
                'start_location' => $tmpStartLocation,
                'passenger_name' => $dispatch->passenger_name,
                'passenger_mobile' => $dispatch->passenger_mobile,
                'end_location' => $tmpEndLocation,
                'fare_amount' => ($dispatch->pay_type == 1) ? $dispatch->rental_cost : '--',
                'flight_no' => $dispatch->flight_no,
                'otherTrip' => $otherTrip,
                'location_type' => $dispatch->location_type,
                'vendor_id' => $dispatch->vendor_id,
                'return' => 0
            ];
            $dispatch->copy_text_1 = static::generateMessage($msgDispatch);

            // if($data['printing'] == 1){
                $rs = Dispatch::generateImageV3('dispatch1', $dispatch, $signature);
                $rs2 = Dispatch::generateImageV3('dispatch2', $dispatch);
                // $printOption = $this->generateImage();
                $dispatch->image_path = $rs;
                $dispatch->image2_path = $rs2;
            // }else{
            //     $dispatch->image_path = null;
            //     $dispatch->image2_path = null;
            // }
            // if($data['print_customer'] == 1){
            //     $printCustomer = static::generateImage('order_info', $dispatch);
            //     // dd($printCustomer);
            //     $dispatch->image3_path = $printCustomer;
            // }

            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->car_type_name);
            unset($dispatch->pay_type_name);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->tmpPickupLocationStr);

            $dispatch->save();
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id){
                $department = Department::find($dispatch->department_id);
                $dispatch->department_name = $department?->name ?? '';
            } else {
                $dispatch->department_name = '';
            }
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $tmpStartLocation = $this->getAirportName( $dispatch->start_location);
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $this->getAirportName( $dispatch->end_location);
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $otherTrip = [];
            if($dispatch->pickup_and_dropoff_location != null){
                $otherTrip = $dispatch->pickup_and_dropoff_location;
                $dispatch->tmpPickupLocationStr = $this->transPickupStr($dispatch->pickup_and_dropoff_location);
            }
            $msgDispatch = [
                'car_model' => Cartype::where('id', $dispatch->cartype_id)->first()->short_name, //$dispatch->cartype_id,
                'ride_date' => Carbon::parse($dispatch->start_date)->toDateString(),
                'ride_time' => Carbon::parse($dispatch->start_date)->toTimeString(),
                'booking_type' => static::getBookingTypeName($dispatch->location_type),
                'start_location' => $tmpStartLocation,
                'passenger_name' => $dispatch->passenger_name,
                'passenger_mobile' => $dispatch->passenger_mobile,
                'end_location' => $tmpEndLocation,
                'fare_amount' => ($dispatch->pay_type == 1) ? $dispatch->rental_cost : '--',
                'flight_no' => $dispatch->flight_no,
                'otherTrip' => $otherTrip,
                'location_type' => $dispatch->location_type,
                'vendor_id' => $dispatch->vendor_id,
                'return' => 0
            ];
            $dispatch->copy_text_1 = static::generateMessage($msgDispatch);
            if($dispatch->signature_path != null){
                $signature = storage_path('app/public/' .$dispatch->signature_path);
            }else{
                $signature = null;
            }
            // dd($dispatch);
            // if($data['printing'] == 1){
                $rs = Dispatch::generateImageV3('dispatch1', $dispatch, $signature);
                // $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch, $signature);
                $dispatch->image_path = $rs;
                // $dispatch->image2_path = $rs2;
            // }else{
            //     $dispatch->image_path = null;
            //     $dispatch->image2_path = null;
            // }
            // dd($dispatch->image_path);
            // if($data['print_customer'] == 1){
            //     $printCustomer = static::generateImage('order_info', $dispatch);
            //     // dd($printCustomer);
            //     $dispatch->image3_path = $printCustomer;
            // }
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->car_type_name);
            unset($dispatch->pay_type_name);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->tmpPickupLocationStr);

            $dispatch->save();
        }
        return $record;
    }
    public function getPayTypeName($data)
    {
        if($data==0){
            return '月結';
        }else if($data==1){
            return '現金';
        }else if($data==2){
            return '匯款';
        }else if($data==3){
            return '刷卡';
        }
        return '資料錯誤!';
    }
    public function getAirportName($data)
    {
        if($data==1){
            return 'TSA 松山機場';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '桃園機場';
        }else if($data==5){
            return '台中機場';
        }
        return '資料錯誤!';
    }
    public function transPickupStr($data)
    {
        $result = [];
        foreach ($data as $index => $person) {
            $result[] = ($index + 1) . ".姓名:" . $person["name"] . "，電話:" . $person["mobile"] . "\n地址:" . $person["location"]."\n";
        }
        $output = implode(" \n", $result);
        return $output;
    }
    public function getTrip($data)
    {
        if($data==1){
            return '桃園機場 T1';
        }else if($data==2){
            return '桃園機場 T2';
        }else if($data==3){
            return '桃園機場';
        }else if($data==4){
            return '台中機場';
        }
        // return '台北車站';
    }
}
