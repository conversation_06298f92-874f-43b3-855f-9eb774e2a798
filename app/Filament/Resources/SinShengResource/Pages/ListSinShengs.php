<?php

namespace App\Filament\Resources\SinShengResource\Pages;

use Filament\Actions;
use App\Models\Dispatch;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Cache;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Widgets\PageMarqueeWidget; // Import the new widget
use App\Filament\Resources\SinShengResource;

class ListSinShengs extends ListRecords
{
    protected static string $resource = SinShengResource::class;
    protected static ?string $title = '派車單列表';
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('新增派車單'),
        ];
    }
    // protected function getHeaderWidgets(): array
    // {
    //     return [
    //         PageMarqueeWidget::make(['message' => '這裡是跑馬燈訊息！請注意最新公告！']),
    //     ];
    // }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
    public function getTabs(): array
    {
        return [
            '今日' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->toDateString())
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function ($query) {
                            session(['activeTab' => '今日']);
                            return $query->whereDate('start_date', '=', now()->toDateString())->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '明日' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->addDay()->toDateString())
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '明日']);
                            return $query->whereDate('start_date', '=', now()->addDay()->toDateString())->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '本周' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本周']);
                            return $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '本月' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本月']);
                            return $query->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '未指派' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('deleted_at', null)
                                ->where('status', 0)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '未指派']);
                            return $query->where('status', 0)->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '已完成' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('status', 2)
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '已完成']);
                            return $query->where('status', 2)->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '全部' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '全部']);
                            return $query->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
        ];
    }
    protected function getFilteredCount(Builder $query): int
    {
        return (clone $query)->count();
    }
}
