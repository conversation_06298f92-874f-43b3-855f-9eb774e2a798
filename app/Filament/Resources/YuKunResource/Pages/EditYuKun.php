<?php

namespace App\Filament\Resources\YuKunResource\Pages;

use App\Models\Car;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use App\Traits\CreateImage;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\YuKunResource;

class EditYuKun extends EditRecord
{
    use CreateImage;
    protected static string $resource = YuKunResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function handleRecordUpdate(Model $record, array $data):Dispatch
    {
        // dd($data);
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Dispatch::find($record->id);
        $dispatch->update($data);
        $dispatch->save();
        if(isset($dispatch->driver_id) && $dispatch->driver_id != null){
            // dd($dispatch);
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->car = $car;
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = $car;
            $dispatch->car_license = $car->license;
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            if($dispatch->signature_path != null){
                $signature = storage_path('app/public/' .$dispatch->signature_path);
            }else{
                $signature = null;
            }
            if($data['printing'] == 1){
                $rs = Dispatch::generateImageV2('dispatch1', $dispatch, $signature);
                $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch, $signature);
                $dispatch->image_path = $rs;
                $dispatch->image2_path = $rs2;
                // $printOption = $this->generateImage('dispatch1', $record->id);
            }else{
                $dispatch->image_path = null;
                $dispatch->image2_path = null;
            }

            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);

            $dispatch->save();
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $signature = null;

            if($data['printing'] == 1){
                $rs = Dispatch::generateImageV2('dispatch1', $dispatch, $signature);
                // $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch, $signature);
                $dispatch->image_path = $rs;
                // $dispatch->image2_path = $rs2;
            }else{
                $dispatch->image_path = null;
                $dispatch->image2_path = null;
            }
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);

            $dispatch->save();
        }
        return $dispatch;
    }
    public function getAirportName($data)
    {
        if($data==1){
            return 'TSA 松山機場';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '桃園機場';
        }else if($data==5){
            return '台中機場';
        }
        return '資料錯誤!';
    }
}
