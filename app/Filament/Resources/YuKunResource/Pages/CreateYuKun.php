<?php

namespace App\Filament\Resources\YuKunResource\Pages;

use App\Models\Car;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use App\Filament\Resources\YuKunResource;
use Filament\Resources\Pages\CreateRecord;

class CreateYuKun extends CreateRecord
{
    protected static string $resource = YuKunResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            // url('/admin/dispatches') => '派車單',
            url('/admin/dispatches') => '列表',
        ];
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function handleRecordCreation(array $data): Dispatch
    {
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Dispatch::create($data);
        if(isset($data['driver_id']) && $data['driver_id'] != null){
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->car = $car;
            $dispatch->customer_title = $customer->title;
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $rs = Dispatch::generateImageV2('dispatch1', $dispatch);
            $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch);

            $dispatch->image_path = $rs;
            $dispatch->image2_path = $rs2;
            unset($dispatch->driver_name);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->save();
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->department_name = empty($department->name) ? '' : $department->name;
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $rs = Dispatch::generateImageV2('dispatch1', $dispatch);
            // $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch);
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            // $dispatch->image2_path = $rs2;
            $dispatch->save();
        }
        return $dispatch;
    }
    public function getAirportName($data)
    {
        if($data==1){
            return '松山機場 TSA';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '桃園機場';
        }else if($data==5){
            return '台中機場';
        }
        return '資料錯誤!';
    }
}
