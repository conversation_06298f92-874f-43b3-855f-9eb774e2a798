<?php

namespace App\Filament\Resources\CustomerAccountResource\Pages;

use App\Filament\Resources\CustomerAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCustomerAccount extends EditRecord
{
    protected static string $resource = CustomerAccountResource::class;
    protected static ?string $title = '編輯派車單';

    public function getBreadcrumbs(): array
    {
        return
        [
            // url('/admin/dispatches') => '派車單',
            url('/admin/customer-accounts') => '列表',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
}
