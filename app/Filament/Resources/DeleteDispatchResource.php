<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use App\Models\DeleteDispatch;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DeleteDispatchResource\Pages;
use App\Filament\Resources\DeleteDispatchResource\RelationManagers;

class DeleteDispatchResource extends Resource
{
    protected static ?string $model = Dispatch::class;
    protected static ?string $navigationLabel = '已刪除派車單管理';
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationIcon = 'heroicon-o-trash';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id); 3 is test james
        if(auth()->user()->id==1 || auth()->user()->id == 7){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', auth()->user()->vendor_id)->whereNotNull('deleted_at');
        }
    }

    public static function form(Form $form): Form
    {
        $firstCarTypeId = Cartype::where('vendor_id', auth()->user()->vendor_id)
                        ->orderBy('sequence', 'asc')
                        ->value('id');
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶資料')
                        ->columns([
                            'default' => 2,
                            'sm' => 1, // 這裡定義在小尺寸螢幕時顯示一個 column
                            'md' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                            'lg' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                        ])
                        ->schema([
                            Forms\Components\Hidden::make('dispatch_no')
                                ->default(fn () => Dispatch::generateDispatchNumber())
                                ->reactive(),
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(fn () => auth()->user()->vendor_id)
                                ->reactive(),
                            Forms\Components\Select::make('customer_id')
                                ->relationship('customer', 'title', function ($query) {
                                    return $query->where('vendor_id', auth()->user()->vendor_id)
                                                ->where('status', 1)
                                                ->orderBy('sequence', 'asc');
                                })
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Hidden::make('vendor_id')
                                            ->default(fn () => auth()->user()->vendor_id)
                                            ->reactive(),
                                        Forms\Components\TextInput::make('title')
                                            ->label('客戶名稱')
                                            ->required()
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('address')
                                            ->label('地址'),
                                        Forms\Components\TextInput::make('telephone')
                                            ->label('電話'),
                                        Forms\Components\TextInput::make('contact')
                                            ->label('聯絡人'),
                                        Forms\Components\TextInput::make('contact_mobile')
                                            ->label('聯絡人電話'),
                                    ])->columns(2),
                                ])
                                ->live()
                                ->searchable()
                                ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                                ->preload()
                                ->label('客戶名稱')
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $set('department_id', null);
                                })
                                ->reactive()
                                ->required(),
                            Forms\Components\Select::make('department_id')
                                ->relationship('department', 'name')
                                ->nullable()
                                ->options(
                                    function ($get) {
                                        $customerId = $get('customer_id');
                                        return Department::where('customer_id', $customerId)
                                                            ->where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id');
                                    }
                                )
                                ->label('部門名稱')
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Select::make('customer_id')
                                            ->relationship('customer', 'title')
                                            ->options(Customer::where('vendor_id', auth()->user()->vendor_id)->pluck('title', 'id'))
                                            ->label('公司名稱')
                                            ->required(),
                                        Forms\Components\TextInput::make('name')
                                            ->label('部門名稱')
                                            ->required(),
                                    ])->columns(3),
                                ])
                                ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                                    $data['vendor_id'] = auth()->user()->vendor_id;
                                    return $data;
                                }))
                                ->live()
                                ->reactive(),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('passenger_name')
                                    ->label('乘客名稱')
                                    ->placeholder('請輸入乘客大名'),
                                Forms\Components\TextInput::make('passenger_mobile')
                                    ->label('乘客電話')
                                    ->placeholder('請輸入乘客電話'),
                            ])
                            ->columns(2)
                            ->default(2),
                        ])->columns(2),
                    Section::make('乘車資料')
                        ->columns([
                            'default' => 2,
                            'sm' => 1, // 這裡定義在小尺寸螢幕時顯示一個 column
                            'md' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                            'lg' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                        ])
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_date')
                                ->placeholder('請選擇日期與時間')
                                ->label('派車日期/時間')
                                ->displayFormat('Y-m-d H:i')
                                ->seconds(false)
                                // ->minutesStep(10)
                                ->firstDayOfWeek(7)
                                ->native(false),
                            Forms\Components\DateTimePicker::make('end_date')
                                ->hidden(),
                            Forms\Components\Select::make('cartype_id')
                                ->label('車輛類別')
                                // >request()
                                ->relationship('cartype', 'name')
                                // ->default(0)
                                ->options(Cartype::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('name', 'id'))
                                ->default($firstCarTypeId)
                                ->live()
                                ->reactive(),
                            Forms\Components\Radio::make('location_type')
                                ->label('派車類別')
                                ->options([
                                    '1' => '接機',
                                    '2' => '送機',
                                    '0' => '單程',
                                    '3' => '包車',
                                    '4' => '短途',
                                ])
                                ->default('1')
                                ->inline()
                                ->live()
                                ->columnSpanFull(),
                            Forms\Components\Select::make('start_location')
                                ->label('上車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '桃園機場',
                                    '5' => '台中機場',
                                    '6' => '小港機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '1')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('start_location')
                                ->label('上車地點')
                                ->placeholder('請輸入上車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2' || $get('location_type') == '3' || $get('location_type') == '4')
                                ->reactive(),
                            Forms\Components\TextInput::make('end_location')
                                ->label('下車地點')
                                ->placeholder('請輸入下車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1' || $get('location_type') == '3' || $get('location_type') == '4')
                                ->reactive(),
                            Forms\Components\Select::make('end_location')
                                ->label('下車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '桃園機場',
                                    '5' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '2')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('flight_no')
                                ->label('航班編號')
                                ->placeholder('請輸入航班編號'),
                            Forms\Components\Select::make('pay_type')
                                ->label('付款方式')
                                ->default(0)
                                ->options([
                                    '0' => '月結',
                                    '1' => '現金',
                                    '2' => '匯款',
                                    '3' => '刷卡',
                                ])
                                ->live()
                                ->reactive(),

                            Grid::make()->schema([
                                Forms\Components\TextInput::make('num_of_people')
                                    ->numeric()
                                    ->label('乘車人數')
                                    ->placeholder('請輸入搭乘人數'),
                                Forms\Components\TextInput::make('num_of_bags')
                                    ->numeric()
                                    ->label('行李數')
                                    ->placeholder('請輸入行李數'),
                                Forms\Components\TextInput::make('child_seat')
                                    ->numeric()
                                    ->default(0)
                                    ->label('安全座椅數')
                                    ->placeholder('請輸入安全座椅數'),
                                Forms\Components\TextInput::make('booster_pad')
                                    ->numeric()
                                    ->default(0)
                                    ->label('增高墊數')
                                    ->placeholder('請輸入增高墊數'),
                            ])->columns(4),
                        ])->columns(2),
                    Section::make('派車資料')
                        ->schema([
                            Forms\Components\Select::make('driver_id')
                                ->searchable()
                                ->preload()
                                ->options(Driver::where('vendor_id', auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id'))
                                ->label('司機')
                                ->live()
                                ->disableOptionWhen(fn (string $value): bool => $value === 2)
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if($state){
                                        $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                    }else{
                                        $set('status', 0);
                                    }
                                }),
                            Forms\Components\Select::make('status')
                                ->label('派車狀態')
                                ->default(0)
                                // ->disabled()
                                ->options([
                                    '0' => '派遣中',
                                    '1' => '已指派司機',
                                    '2' => '已完成',
                                    '3' => '已取消',
                                    '4' => '已出發',
                                    '5' => '未出發',
                                ]),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('carno2')
                                    ->label('其他車號')
                                    ->placeholder('請輸入其他車牌號碼'),
                            ])->columns(2),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('rental_cost')
                                    ->label('費用')
                                    ->placeholder('請輸入費用')
                                    ->numeric(),

                                Forms\Components\TextInput::make('driver_fee')
                                    ->label('司機費用')
                                    ->placeholder('請輸入司機費用')
                                    ->numeric(),
                                // Forms\Components\TextInput::make('deposit')
                                //     ->label('已付訂金')
                                //     ->placeholder('請輸入訂金費用')
                                //     ->numeric(),
                            ])->columns(3),
                            // Forms\Components\TextInput::make('return_fee')
                            //     ->label('回金')
                            //     ->visible(fn ($get) => $get('pay_type') == '1'),
                            // Forms\Components\TextInput::make('return_boss')
                            //     ->label('車趟來源')
                            //     ->visible(fn ($get) => $get('pay_type') == '1'),
                            Grid::make()->schema([
                                Forms\Components\Toggle::make('return')
                                    ->hiddenOn('edit')
                                    ->default(0)
                                    ->live()
                                    ->reactive()
                                    ->label('回程'),
                                Forms\Components\DateTimePicker::make('return_date')
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程日期/時間')
                                    ->displayFormat('Y-m-d H:i')
                                    ->seconds(false)
                                    ->firstDayOfWeek(7)
                                    ->native(false),
                                Forms\Components\TextInput::make('return_flight_no')
                                    // ->default(0)
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程航班編號'),
                            ])->columns(3),
                        ])->columns(2),
                ]),
                Group::make([
                    Section::make('其他上/下車資訊')
                        ->schema([
                            Repeater::make('pickup_and_dropoff_location')
                                ->label('其他上/下車資訊')
                                ->defaultItems(0)
                                ->schema([
                                    Grid::make(3)->schema([
                                        TextInput::make('name')
                                            ->label('乘客大名'),
                                        TextInput::make('mobile')
                                            ->label('連絡電話'),
                                        TextInput::make('time')
                                            ->label('時間')
                                            ->placeholder('請輸入4位數字')
                                            ->dehydrateStateUsing(function ($state) {
                                                // 格式化为 HH:mm
                                                if (preg_match('/^\d{4}$/', $state)) {
                                                    $hours = substr($state, 0, 2);
                                                    $minutes = substr($state, 2, 2);
                                                    return "$hours:$minutes";
                                                }
                                                return $state;
                                            })
                                            ->mutateDehydratedStateUsing(function ($state) {
                                                // 如果需要重新格式化输入值时调用
                                                return $state;
                                            })
                                            ->helperText('範例:1330 -> 13:30')
                                    ]),
                                    TextInput::make('location')
                                            ->label('上下車地點')
                                            ->columnSpanFull(),
                                ])
                        ]),
                    Section::make('其他資料')
                        ->schema([
                            Group::make([
                                Forms\Components\Textarea::make('note')
                                    ->rows(5)
                                    ->label('備註'),
                                Forms\Components\Textarea::make('driver_note')
                                    ->rows(5)
                                    ->label('其他備註'),
                            ])->columns(2),
                            Grid::make()->schema([
                                Forms\Components\Placeholder::make('image_path')
                                    ->label('簽單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image_path) . "' onclick=window.open('" . asset('storage/' . $record->image_path) . "?".time()."')>");
                                        }
                                        return new HtmlString('');
                                    }),
                                Forms\Components\Placeholder::make('image2_path')
                                    ->label('出租單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image2_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image2_path) . "' onclick=window.open('" . asset('storage/' . $record->image2_path) . "?".time()."')>");
                                        }
                                        return new HtmlString('');
                                    }),

                            ])->columns(2),
                        ])
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('30s')
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->toggleable()
                    ->badge()
                    ->extraAttributes(function ($record) {
                        if($record->status == 5){
                            return ['style' => 'background-color: red;'];
                        }else if($record->status == 4){
                            return ['style' => 'background-color: green;'];
                        }else{
                            return [];
                        }
                    })
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 4){
                            return '已出發';
                        }else if($record['status'] == 5){
                            return '未出發';
                        }else if($record['status'] == 101){
                            return '異常';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已出發' => 'success',
                        '已取消' => 'warning',
                        '未出發' => 'danger',
                        '異常' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->searchable()
                    ->toggleable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\SelectColumn::make('driver_id')
                    ->label('司機')
                    ->toggleable()
                    ->options(
                        Driver::where('vendor_id', auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id')->toArray()
                    )
                    ->afterStateUpdated(function ($state, $record) {
                        // dd($state);
                        if ($state) {
                            $record->status = 1;
                            $record->save();
                            $driver = Driver::where('id', $record->driver_id)->first();
                            $car = Car::where('driver_id', $record->driver_id)->first();
                            $customer = Customer::where('id', $record->customer_id)->first();
                            if($record->department_id != null){
                                $department = Department::where('id', $record->department_id)->first();
                                $record->department_name = $department->name;
                            }
                            $record->customer_title = $customer->title;
                            $record->driver_name = $driver->name;
                            $record->car_license = $car->license;
                            $record->car = $car;
                            // $rs = Dispatch::generateImage($record);
                            // 產生派車單
                            $rs = Dispatch::generateImageV3('dispatch1',$record);
                            $rs2 = Dispatch::generateImageV3('dispatch2',$record);
                            if(!$rs){
                                return $this->sendError('簽單圖片生成失敗!請與車行洽詢!');
                            }
                            if(!$rs2){
                                return $this->sendError('出租單圖片生成失敗!請與車行洽詢!');
                            }
                            Dispatch::where('id', $record->id)
                                ->update([
                                    // 'signature_file' => 'signatures/' . $fileName,
                                    'image_path' => $rs,
                                    'image2_path' => $rs2,
                                ]);
                        }else{
                            $record->status = 0;
                            $record->save();
                        }
                    })
                    ->extraAttributes([
                        'style' => 'width: 120px;',  // 設定固定寬度為150px
                    ])
                    ,
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->toggleable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    ->toggleable()
                    ->label('車型')
                    ->getStateUsing(function (Model $record) {
                        $rs = Cartype::where('id', $record['cartype_id'])->first();
                        if($rs != null){
                            return $rs->short_name;
                        }
                        return '';
                    }),

                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->toggleable()
                    ->searchable()
                    // ->limit(30)
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if($record['start_location'] == 1 || $record['end_location'] == 1){
                            $tmpdata = 'TSA 松山機場';
                        }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                            $tmpdata = '桃園機場 T1';
                        }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                            $tmpdata = '桃園機場 T2';
                        }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                            $tmpdata = '桃園機場';
                        }else if($record['start_location'] == 5 || $record['end_location'] == 5){
                            $tmpdata = '台中機場';
                        }else{
                            $tmpdata = $record['start_location'];
                        }
                        if($record['location_type'] == 1){
                            return $tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('up_time')
                    ->label('出發/上/下車時間')
                    ->toggleable()
                    ->formatStateUsing(function (Model $record) {
                        $startTime = Carbon::parse($record->car_start_time);
                        $upTime = Carbon::parse($record->up_time);
                        $downTime = Carbon::parse($record->down_time);
                        // $duration = $upTime->diffInMinutes($downTime);
                        // $hours = floor($duration / 60);
                        // $minutes = $duration % 60;
                        return $startTime->format('H:i') . ' <br />' . $upTime->format('H:i') . ' <br />' . $downTime->format('H:i'); // . '<br />計: ' . $hours . ' 時 ' . $minutes . ' 分';
                    })->html(),
                Tables\Columns\TextColumn::make('pay_type')
                    ->toggleable()
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return '匯款';
                            case 3:
                                return '刷卡';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextInputColumn::make('rental_cost')
                    ->toggleable()
                    ->label('月結/現金')
                    ,
                Tables\Columns\TextInputColumn::make('driver_fee')
                    ->toggleable()
                    ->label('司機車資'),

                // Tables\Columns\TextColumn::make('rental_cost')
                //     ->label('金額')
                //     ->numeric()
                //     ->summarize(Sum::make()->label('總計金額')),
                // Tables\Columns\TextColumn::make('driver_fee')
                //     ->label('司機車資')
                //     ->numeric()
                //     ->summarize(Sum::make()->label('總計金額'))
            ])
            ->filters([
                Filter::make('customer_id')
                    ->label('查詢')
                    ->form([
                        Select::make('customer_id')
                            ->label('公司名稱')
                            ->options(Customer::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence')->pluck('title', 'id'))
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('department_id', null);
                            }),
                        Select::make('department_id')
                            ->label('部門')
                            ->options(function ($get) {
                                return Department::where('customer_id', $get('customer_id'))
                                    ->where('vendor_id', auth()->user()->vendor_id)
                                    ->pluck('name', 'id');
                            }),
                            // ->options(Department::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // dd($data);
                        return $query
                            ->when($data['customer_id'], fn ($query, $customer_id) => $query->where('customer_id', $customer_id)->where('vendor_id', auth()->user()->vendor_id))
                            ->when($data['department_id'], fn ($query, $department_id) => $query->where('department_id', $department_id))->where('vendor_id', auth()->user()->vendor_id);
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['customer_id']) {
                            $customer_name = Customer::find($data['customer_id'])->title;
                            $indicators['customer_id'] = '公司: ' . $customer_name;
                        }
                        if ($data['department_id']) {
                            $department_name = Department::find($data['department_id'])->name;
                            $indicators['department_id'] = '部門: ' . $department_name;
                        }
                        return $indicators;
                    })
                    ,
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('start_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('start_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
                SelectFilter::make('status')
                    ->label('狀態')
                    ->searchable()
                    ->options([
                        '0' => '派遣中',
                        '1' => '已指派司機',
                        '2' => '已完成',
                        '3' => '已取消',
                        '4' => '已出發',
                        '5' => '未出發',
                    ]),
            ])
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),

                    Tables\Actions\DeleteAction::make()
                        ->label('回復')
                        ->icon('heroicon-o-trash')
                        ->action(function ($record) {
                            Dispatch::find($record->id)->update(['deleted_at' => null, 'status' => 0]);
                        }),
                ])
                ->label('')
                ->icon('heroicon-o-cog'),
            ])
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\ExportBulkAction::make()
            //             ->exporter(DispatchExporter::class)
            //             ->label('匯出'),
            //         Tables\Actions\DeleteBulkAction::make(),
            //     ]),
            // ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeleteDispatches::route('/'),
            'create' => Pages\CreateDeleteDispatch::route('/create'),
            'edit' => Pages\EditDeleteDispatch::route('/{record}/edit'),
        ];
    }
}
