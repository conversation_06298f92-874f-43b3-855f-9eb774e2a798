<?php

namespace App\Filament\Resources\ZuSanDriverResource\Pages;

use App\Filament\Resources\ZuSanDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditZuSanDriver extends EditRecord
{
    protected static string $resource = ZuSanDriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
