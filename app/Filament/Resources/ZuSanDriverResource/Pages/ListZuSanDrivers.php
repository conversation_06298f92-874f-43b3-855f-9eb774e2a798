<?php

namespace App\Filament\Resources\ZuSanDriverResource\Pages;

use App\Filament\Resources\ZuSanDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListZuSanDrivers extends ListRecords
{
    protected static string $resource = ZuSanDriverResource::class;
    protected static ?string $title = '司機報表';
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
