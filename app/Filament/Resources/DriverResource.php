<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Vendor;
use GuzzleHttp\Client;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Enums\FiltersLayout;
use Illuminate\Database\Eloquent\Builder;
use LINE\Clients\MessagingApi\Configuration;
use App\Filament\Resources\DriverResource\Pages;
use LINE\Clients\MessagingApi\Api\MessagingApiApi;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DriverResource\RelationManagers;

class DriverResource extends Resource
{
    protected static ?string $model = Driver::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '司機管理';
    protected static ?string $navigationGroup = '一般管理';
    protected static ?int $navigationSort = 11;

    public static function getEloquentQuery(): Builder
    {
            if(Filament::auth()->user()->vendor_id==0){
                return parent::getEloquentQuery()
                    // ->where('status', 0)
                    ->where('deleted_at', null)
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);
            }else{
                return parent::getEloquentQuery()
                    // ->where('status', 0)
                    ->where('deleted_at', null)
                    ->where('vendor_id', Filament::auth()->user()->vendor_id)
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);;
            }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('司機基本資料')
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('司機姓名')
                                ->required(),
                            Forms\Components\Radio::make('sex')
                                ->label('性別')
                                ->default('1')
                                ->required()
                                ->inline()
                                ->options([
                                    '1' => '男',
                                    '0' => '女',
                                ]),
                            Forms\Components\TextInput::make('mobile')
                                ->label('手機')
                                ->required(),
                            Forms\Components\TextInput::make('person_id')
                                ->required()
                                ->label('身分證字號'),
                            Forms\Components\TextInput::make('address')
                                ->label('地址'),
                            Forms\Components\TextInput::make('line_id')
                                ->disabled()
                                // ->required()
                                ->hiddenOn('create'),
                            Forms\Components\TextInput::make('nickname')
                                ->disabled()
                                ->hiddenOn('create'),
                            Forms\Components\TextInput::make('avatar')
                                ->disabled()
                                ->hiddenOn('create'),
                            Forms\Components\Select::make('active')
                                ->label('狀態')
                                ->hiddenOn('create')
                                ->placeholder('請選擇')
                                ->options([
                                    '0' => '未啟用/審核未通過',
                                    '1' => '審核通過',
                                ])
                                ->afterStateUpdated(function ($record, Select $component, $state) {
                                    // dd($record);
                                    $user_line_id = $record->line_id;
                                    $channelToken = config('line.channel_access_token');
                                    $config = new Configuration();
                                    $config->setAccessToken($channelToken);
                                    $bot = new MessagingApiApi(new Client(), $config);

                                    if($state==1){
                                        if($record->join_type==0){
                                            Log::info('司機 line id '.json_encode($user_line_id));
                                            $driver = Driver::where('line_id', $user_line_id)->first();
                                            $vendor = Vendor::where('id', $driver->vendor_id)->first();
                                            Log::info('司機 DATA '.json_encode($driver));
                                            // Log::info('司機 USER ID '.json_encode($user_line_id));
                                            // Log::info('司機 RM ID '.json_encode($vendor->rm_id));
                                            // $bot->linkRichMenuIdToUser($user_line_id, 'richmenu-b434072b7e79aa7d8912ad02cd130ff3');
                                            $bot->linkRichMenuIdToUser($user_line_id, $vendor->rm_id);
                                            $message = '審核通過，請開始使用電子派車單!';
                                            static::pushMsg($message, $user_line_id, $channelToken);
                                        }

                                        // Log::info(print_r($response, true));
                                    }else if($state==0 || $state==2){
                                        if($record->join_type==0){
                                            Log::info('司機 解除綁定:: '.$user_line_id);
                                            // $message = '解除綁定，如有疑問，請與車行聯繫';
                                            // static::pushMsg($message, $user_line_id, $channelToken);
                                            $bot->unlinkRichMenuIdFromUser($user_line_id);
                                        }
                                    }
                                }),
                            Forms\Components\FileUpload::make('driver_image')
                                // ->image()
                                ->hiddenOn('create')
                                ->label('司機照片')
                                ->imageEditor()
                                ->disk('public')
                                ->directory('driver_image')
                                ->acceptedFileTypes(['image/jpeg', 'image/jpg', 'image/png'])
                                ->storeFileNamesIn('original_filename'),
                            Forms\Components\Textarea::make('note')
                                ->label('備註')
                                ->columnSpan(2),
                            Forms\Components\Hidden::make('vendor_id')
                                    ->default(fn () => Filament::auth()->user()->vendor_id)

                        ])->columns(2),
                ]),
                Group::make([
                    Forms\Components\Fieldset::make('車輛資訊')
                        ->relationship('car')
                        ->schema([
                            Forms\Components\TextInput::make("brand")
                                // ->disabled()
                                ->required()
                                ->label('廠牌'),
                            Forms\Components\TextInput::make("license")
                                // ->disabled()
                                ->required()
                                ->label('車牌號碼'),
                            Forms\Components\TextInput::make("model")
                                // ->disabled()
                                ->label('型號'),
                            Forms\Components\TextInput::make("color")
                                // ->disabled()
                                ->label('顏色'),

                        ]),
                    Forms\Components\Placeholder::make('driver_info_image_path')
                            ->label('司機資訊')
                            ->hiddenOn('create')
                            ->content(function ($record): HtmlString {
                                if(!empty($record->driver_info_image_path)){
                                    return new HtmlString("<img src= '" . asset('storage/'.$record->driver_info_image_path)  . "?".time(). "' onclick=window.open('" . asset('storage/' . $record->driver_info_image_path) . "?".time()."')>");
                                }
                                return new HtmlString('');
                            }),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->toggleable(),
                Tables\Columns\ImageColumn::make('driver_image')
                    ->circular()
                    ->width(40)
                    ->label(''),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->label('司機姓名'),
                Tables\Columns\TextColumn::make('mobile')
                    ->searchable()
                    ->label('手機'),
                // Tables\Columns\TextColumn::make('address'),
                // Tables\Columns\TextColumn::make('nickname'),
                Tables\Columns\TextColumn::make('car.license')
                    ->searchable()
                    ->label('車牌號碼'),
                Tables\Columns\IconColumn::make('active')
                    ->label('是否啟用')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->searchable()
                    ->label('建立時間'),
            ])
            ->reorderable('sequence')
            ->defaultSort('sequence')
            ->filters([
                Filter::make('join_type')
                    ->visible(fn () => Filament::auth()->user()->user_id == 0 || Filament::auth()->user()->vendor_id == 6)
                    ->query(function ($query, $data) {
                        if (isset($data['join_type']) && $data['join_type'] != 99) {
                            $query->where('join_type', $data['join_type']);
                        }
                    })
                    ->form([
                        Radio::make('join_type')
                            ->default(99)
                            ->inline()
                            ->label('司機加入方式')
                            ->options([
                                99 => '全部',
                                0 => 'LINE',
                                1 => '外派司機',
                            ])
                            ->extraAttributes(['class' => 'inline-radio']),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->label('刪除')
                    ->modalHeading('確認刪除此筆資料!')
                    ->modalSubheading('刪除後無法恢復，請確認是否刪除!')
                    ->modalButton('刪除')
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDrivers::route('/'),
            'create' => Pages\CreateDriver::route('/create'),
            'edit' => Pages\EditDriver::route('/{record}/edit'),
        ];
    }

    public static function pushMsg($message, $uid, $ChannelAccessToken):void
    {
        $Payload = [
            'to' => $uid,
            'messages' => [
                [
                    'type' => 'text',
                    'text' => $message
                ]
            ]
        ];

        // 傳送訊息
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/bot/message/push');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($Payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $ChannelAccessToken
        ]);
        $Result = curl_exec($ch);
        curl_close($ch);
        // return $Result;
    }
}
