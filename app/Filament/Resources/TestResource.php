<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Mytest;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Columns\Summarizers\Sum;
use Illuminate\Database\Eloquent\Collection;
use App\Filament\Resources\TestResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\TestResource\RelationManagers;

class TestResource extends Resource
{
    protected static ?string $model = Mytest::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '測試管理';
    public static $vendorId = 2;
    protected static ?int $navigationSort = 5;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny TEST--'.auth()->user()->id);
        if(auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', static::$vendorId);
        }
    }

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Group::make([
                Section::make('客戶資料')
                    ->schema([
                        Forms\Components\Hidden::make('dispatch_no')
                            ->default(fn () => Dispatch::generateDispatchNumber())
                            ->reactive(),
                        Forms\Components\Hidden::make('vendor_id')
                            ->default(fn () => auth()->user()->vendor_id)
                            ->reactive(),
                        Forms\Components\Select::make('customer_id')
                            ->relationship('customer', 'title', function ($query) {
                                return $query->where('vendor_id', auth()->user()->vendor_id)
                                            ->orderBy('sequence', 'asc');
                            })
                            ->createOptionForm([
                                Grid::make()->schema([
                                    Forms\Components\Hidden::make('vendor_id')
                                        ->default(fn () => auth()->user()->vendor_id)
                                        ->reactive(),
                                    Forms\Components\TextInput::make('title')
                                        ->label('客戶名稱')
                                        ->required()
                                        ->columnSpanFull(),
                                    Forms\Components\TextInput::make('address')
                                        ->label('地址'),
                                    Forms\Components\TextInput::make('telephone')
                                        ->label('電話'),
                                    Forms\Components\TextInput::make('contact')
                                        ->label('聯絡人'),
                                    Forms\Components\TextInput::make('contact_mobile')
                                        ->label('聯絡人電話'),
                                ])->columns(2),
                            ])
                            ->live()
                            ->searchable()
                            ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                            ->preload()
                            ->label('客戶名稱')
                            // ->default('1')
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('department_id', null);
                            })
                            ->reactive()
                            ->required(),
                        Forms\Components\Select::make('department_id')
                            ->relationship('department', 'name')
                            // ->default(0)
                            ->nullable()
                            ->options(
                                function ($get) {
                                    $customerId = $get('customer_id');
                                    return Department::where('customer_id', $customerId)
                                                        ->where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id');
                                }
                            )
                            ->label('部門名稱')
                            ->createOptionForm([
                                Grid::make()->schema([
                                    Forms\Components\Select::make('customer_id')
                                        ->relationship('customer', 'title')
                                        ->options(Customer::all()->pluck('title', 'id'))
                                        ->label('公司名稱')
                                        // ->hidden()
                                        // ->default(fn () => 2)
                                        ->required(),
                                    Forms\Components\TextInput::make('name')
                                        ->label('部門名稱')
                                        ->required(),
                                    // Forms\Components\TextInput::make('vendor_id')
                                    //     ->label('不用輸入')
                                    //     ->default(fn () => auth()->user()->vendor_id)
                                    //     ->hidden()
                                    //     // ->maxWidth('10px')
                                    //     ->required(),
                                ])->columns(3),
                            ])
                            ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                                // set tenant in $data
                                // dd($data);
                                $data['vendor_id'] = auth()->user()->vendor_id;
                                return $data;
                            }))
                            ->live()
                            ->reactive(),
                        Grid::make()->schema([
                            Forms\Components\TextInput::make('passenger_name')
                                ->label('乘客名稱')
                                ->placeholder('請輸入乘客大名'),
                            Forms\Components\TextInput::make('passenger_mobile')
                                ->label('乘客電話')
                                ->placeholder('請輸入乘客電話'),
                        ])
                    ])->columns(2),
                Section::make('乘車資料')
                    ->schema([
                        Forms\Components\DateTimePicker::make('start_date')
                            ->placeholder('請選擇日期與時間')
                            ->label('派車日期/時間')
                            ->displayFormat('Y-m-d H:i')
                            ->seconds(false)
                            // ->minutesStep(10)
                            ->firstDayOfWeek(7)
                            ->native(false),
                        Forms\Components\DateTimePicker::make('end_date')
                            ->hidden(),
                        Forms\Components\Select::make('pay_type')
                            ->label('付款方式')
                            ->default(0)
                            ->options([
                                '0' => '月結',
                                '1' => '現金',
                                '2' => 'LINE PAY',
                                '3' => '匯款',
                            ])
                            ->live()
                            ->reactive(),
                        Forms\Components\Radio::make('location_type')
                            ->label('派車類別')
                            ->options([
                                '1' => '接機',
                                '2' => '送機',
                                '0' => '其他',
                            ])
                            ->default('1')
                            ->inline()
                            ->live()
                            ->columnSpanFull(),
                        Forms\Components\Select::make('start_location')
                            ->label('上車地點')
                            ->options([
                                '1' => 'TSA 松山機場',
                                '2' => '桃園機場 T1',
                                '3' => '桃園機場 T2',
                                '4' => '台中機場',
                            ])
                            ->visible(fn ($get) => $get('location_type') == '1')
                            ->live()
                            ->reactive(),

                        Forms\Components\TextInput::make('start_location')
                            ->label('上車地點')
                            ->placeholder('請輸入上車地點')
                            ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2')
                            ->reactive(),
                        Forms\Components\TextInput::make('end_location')
                            ->label('下車地點')
                            ->placeholder('請輸入下車地點')
                            ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1')
                            ->reactive(),
                        Forms\Components\Select::make('end_location')
                            ->label('下車地點')
                            ->options([
                                '1' => 'TSA 松山機場',
                                '2' => '桃園機場 T1',
                                '3' => '桃園機場 T2',
                                '4' => '台中機場',
                            ])
                            ->visible(fn ($get) => $get('location_type') == '2')
                            ->live()
                            ->reactive(),
                        Forms\Components\TextInput::make('flight_no')
                            ->label('航班編號')
                            ->placeholder('請輸入航班編號'),
                        Forms\Components\Select::make('car_type')
                            ->label('車輛類別')
                            ->default(0)
                            ->options([
                                '0' => 'Benz  小車5人座車',
                                '1' => 'Vito   大車9人座車',
                                '2' => 'GRANVIA (代號G) 大車 6人座車',
                            ])
                            ->live()
                            ->reactive(),

                    ])->columns(2),

            ]),
            Group::make([
                Section::make('派車資料')
                    ->schema([
                        Forms\Components\Select::make('driver_id')
                            ->searchable()
                            ->preload()
                            ->options(Driver::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id'))
                            ->label('司機')
                            ->live()
                            ->disableOptionWhen(fn (string $value): bool => $value === 2)
                            ->afterStateUpdated(function ($state, callable $set) {
                                if($state){
                                    $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                }else{
                                    $set('status', 0);
                                }
                            }),
                        Forms\Components\Select::make('status')
                            ->label('派車狀態')
                            ->default(0)
                            // ->disabled()
                            ->options([
                                '0' => '派遣中',
                                '1' => '已指派司機',
                                '2' => '已完成',
                                '3' => '已取消',
                                '102' => '未審核',
                            ]),
                        Forms\Components\TextInput::make('rental_cost')
                            ->label('費用')
                            ->placeholder('請輸入費用')
                            ->numeric(),
                        Forms\Components\TextInput::make('driver_fee')
                            ->label('司機費用')
                            ->placeholder('請輸入司機費用')
                            ->numeric(),
                        // CopyablePlaceholder::make('Label')
                        //     ->label('簽名檔連結')
                        //     ->content(fn ($get) => 'http://carv2.aerocars.cc/sign/') // . Crypt::encryptString($get('dispatch_no')))
                        //     ->visible(fn ($get) => $get('driver_id') == '11')
                        //     ->iconOnly(),

                    ])->columns(2),
                Section::make('其他資料')
                    ->schema([
                        Group::make([
                            Forms\Components\Textarea::make('note')
                                ->rows(5)
                                ->label('備註'),

                            // Forms\Components\Textarea::make('driver_note')
                            //     ->rows(5)
                            //     ->label('司機備註'),
                        ])->columns(2),
                        Forms\Components\Placeholder::make('image_path')
                            ->label('簽單')
                            ->hiddenOn('create')
                            ->content(function ($record): HtmlString {
                                if(!empty($record->image_path)){
                                    return new HtmlString("<img src= '" . asset('storage/'.$record->image_path) . "' onclick=window.open('" . asset('storage/' . $record->image_path) . "?".time()."')>");
                                }
                                return new HtmlString('');
                            }),
                        // Forms\Components\FileUpload::make('attachment')
                        //     ->label('附件')
                        //     ->hiddenOn('create')
                    ])
            ])
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('10s')
            // ->header(function () {
            //     return view('filament.tables.test-tabs');
            // })
            ->columns([
                Tables\Columns\TextColumn::make('source')
                    ->label('來源'),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 102){
                            return '未審核';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'danger',
                        '未審核' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    // ->copyable()
                    // ->copyableState(function (Dispatch $record){
                    //     if($record->driver_id == 11){
                    //         return "http://carv2-web.chihlistudio.com/sign/" . Crypt::encryptString($record->dispatch_no);
                    //     }
                    // })
                    // ->copyMessage('已複製')
                    // ->copyMessage(function (Dispatch $record) {
                    //     return $record->driver_id == 11 ? '已複製簽名鏈接' : '請選擇外派司機';
                    // })
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\SelectColumn::make('driver_id')
                    ->label('司機')
                    ->options(
                        Driver::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id')->toArray()
                    )
                    ->afterStateUpdated(function ($state, $record) {
                        // dd($state);
                        if ($state) {
                            $record->status = 1;
                            $record->save();
                            $driver = Driver::where('id', $record->driver_id)->first();
                            $car = Car::where('driver_id', $record->driver_id)->first();
                            $customer = Customer::where('id', $record->customer_id)->first();
                            if($record->department_id != null){
                                $department = Department::where('id', $record->department_id)->first();
                                $record->department_name = $department->name;
                            }
                            $record->customer_title = $customer->title;
                            $record->driver_name = $driver->name;
                            $record->car_license = $car->license;
                            $rs = Dispatch::generateImage($record);
                            if(!$rs){
                                return $this->sendError('簽名圖片生成失敗!請與車行洽詢!');
                            }else{
                                Dispatch::where('id', $record->id)
                                    ->update([
                                        // 'signature_file' => 'signatures/' . $fileName,
                                        'image_path' => $rs,
                                    ]);
                            }
                        }else{
                            $record->status = 0;
                            $record->save();
                        }
                    })
                    ->extraAttributes([
                        'style' => 'width: 120px;',  // 設定固定寬度為150px
                    ]),
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    ->label('車型')
                    ->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return 'Benz';
                            case 1:
                                return 'Vito';
                            case 2:
                                return 'GRANVIA';
                            default:
                                return 'Benz';
                        }
                    }),

                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->searchable()
                    // ->limit(30)
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if($record['start_location'] == 1 || $record['end_location'] == 1){
                            $tmpdata = 'TSA 松山機場';
                        }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                            $tmpdata = '桃園機場 T1';
                        }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                            $tmpdata = '桃園機場 T2';
                        }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                            $tmpdata = '台中機場';
                        }else{
                            $tmpdata = $record['start_location'];
                        }
                        if($record['location_type'] == 1){
                            return $tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('up_time')
                    ->label('上/下車時間')
                    ->formatStateUsing(function (Model $record) {
                        $upTime = Carbon::parse($record->up_time);
                        $downTime = Carbon::parse($record->down_time);
                        $duration = $upTime->diffInMinutes($downTime);
                        $hours = floor($duration / 60);
                        $minutes = $duration % 60;
                        return $upTime->format('H:i') . ' <br />' .  $downTime->format('H:i') . '<br />計: ' . $hours . ' 時 ' . $minutes . ' 分';
                    })->html(),
                Tables\Columns\TextColumn::make('paytype_id')
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return 'LINE PAY';
                            case 3:
                                return '匯款';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('月結/現金')
                    ,
                Tables\Columns\TextColumn::make('driver_fee')
                    ->label('司機車資'),
                // Tables\Columns\TextColumn::make('driver_fee2')
                //     ->label('司機車資2'),

                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('金額')
                    ->numeric()
                    // ->summarizeLabel('本頁總計')
                    ->summarize(Sum::make()->label('總計金額')),
                Tables\Columns\TextColumn::make('driver_fee')
                    ->label('司機車資')
                    ->numeric()
                    ->summarize(Sum::make()->label('總計金額'))
            ])
            ->defaultSort('start_date', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('批次修改狀態')
                        ->action(function (Collection $records, array $data): void {
                            $records->each(function ($record) use ($data) {
                                $record->update(['status' => $data['status']]);
                            });
                        })
                        ->form([
                            Select::make('status')
                                ->options([
                                    '0' => '派遣中',
                                    '1' => '已指派司機',
                                    '2' => '已完成',
                                    '3' => '已取消',
                                    '102' => '未審核',
                                ])
                                ->required()
                                ->label('選擇狀態'),
                        ])
                        ->requiresConfirmation()
                        ->color('success'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTests::route('/'),
            'create' => Pages\CreateTest::route('/create'),
            'edit' => Pages\EditTest::route('/{record}/edit'),
        ];
    }
}
