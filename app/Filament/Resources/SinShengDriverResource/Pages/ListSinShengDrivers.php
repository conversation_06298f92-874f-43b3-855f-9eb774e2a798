<?php

namespace App\Filament\Resources\SinShengDriverResource\Pages;

use App\Filament\Resources\SinShengDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSinShengDrivers extends ListRecords
{
    protected static string $resource = SinShengDriverResource::class;
    protected static ?string $title = '司機報表';
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
}
