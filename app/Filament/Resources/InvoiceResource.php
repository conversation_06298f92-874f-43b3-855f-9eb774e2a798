<?php

namespace App\Filament\Resources;

use copyable;
use Filament\Forms;
use Filament\Tables;
use Livewire\Livewire;
use App\Models\Invoice;
use App\Models\Customer;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use App\Traits\CreateInvoiceTrait;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Crypt;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ModalAction;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Actions\Action;
use App\Filament\Resources\InvoiceResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\InvoiceResource\RelationManagers;
use AxonC\FilamentCopyablePlaceholder\Forms\Components\CopyablePlaceholder;

class InvoiceResource extends Resource
{
    use CreateInvoiceTrait;
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '帳務管理';
    protected static ?string $navigationLabel = '發票管理';
    protected static ?int $navigationSort = 6;

    public static function canViewAny(): bool
    {
        // Log::info('vendor id Log--' . auth()->user()->vendor_id);
        if (auth()->user()->id == 1) {
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶資料')
                        ->schema([
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(auth()->user()->vendor_id),
                            Forms\Components\Select::make('customer_id')
                                ->label('客戶簡稱')
                                ->relationship('customer', 'title', function ($query) {
                                    return $query
                                        ->where('vendor_id', auth()->user()->vendor_id)
                                        ->orderBy('sequence', 'asc');
                                })
                                ->reactive()
                                ->required()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $customer = Customer::find($state);
                                    $set('customer_mobile', $customer ? $customer->contact_mobile : '');
                                    $set('company_code', $customer ? $customer->company_id : '');
                                    $set('contact_email', $customer ? $customer->contact_email : '');
                                    $set('company_name', $customer ? $customer->full_title : '');
                                }),
                            Forms\Components\Select::make('department_id')
                                ->relationship('department', 'name')
                                ->nullable()
                                ->options(
                                    function ($get) {
                                        $customerId = $get('customer_id');
                                        return Department::where('customer_id', $customerId)
                                                            ->where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id');
                                    }
                                )
                                ->label('部門名稱'),
                            Forms\Components\TextInput::make('company_name')
                                ->label('客戶名稱')
                                ->disabled()
                                ->dehydrated(true),
                            Forms\Components\TextInput::make('customer_mobile')
                                ->label('客戶電話')
                                ->disabled()
                                ->dehydrated(true),
                            Forms\Components\TextInput::make('contact_email')
                                ->label('電子信箱')
                                ->disabled()
                                ->dehydrated(true),
                            Forms\Components\TextInput::make('company_code')
                                ->label('統一編號')
                                ->afterStateHydrated(function ($state, callable $set, $record) {
                                    if ($record) {
                                        $customer = $record->customer;
                                        $set('company_code', $customer ? $customer->company_id : '');
                                    }
                                })
                                ->disabled()
                                ->dehydrated(true),
                            Forms\Components\DatePicker::make('invoice_date')
                                            ->placeholder('請選擇日期')
                                            ->label('發票日期')
                                            ->default(now())
                                            ->displayFormat('Y-m-d')
                                            ->firstDayOfWeek(7)
                                            ->native(false),
                    ])->columns(2),
                ]),
                Group::make([
                    Section::make('客戶資料')
                        ->schema([
                            Forms\Components\Repeater::make('invoiceDetails')
                                ->relationship('invoiceDetails')
                                ->label('發票明細')
                                ->schema([
                                    Forms\Components\TextInput::make('name')
                                        ->label('品名')
                                        ->default('車資')
                                        ->required(),
                                    Forms\Components\TextInput::make('number')
                                        ->label('數量')
                                        ->required()
                                        ->default(1)
                                        ->numeric()
                                        ->reactive(),
                                    Forms\Components\TextInput::make('money')
                                        ->label('單價')
                                        ->required()
                                        ->numeric()
                                        ->reactive()
                                        ->debounce(800),
                                    ])
                                    ->addActionLabel('新增發票明細')
                                    ->columns(3)
                                    ->afterStateUpdated(function($state, callable $set){
                                        $sales = collect($state)->reduce(function ($total, $item) {
                                            return $total + ($item['number'] * $item['money']);
                                        }, 0);
                                        $sales = round($sales);
                                        $taxAmount = round($sales * 0.05);
                                        $total = $sales + $taxAmount;
                                        $set('sales', $sales);
                                        $set('amount', $taxAmount);
                                        $set('total_fee', $total);
                                    })
                                    ->dehydrated(true),

                        ]),
                    Section::make('')
                        ->label('')
                        ->schema([
                            Forms\Components\TextInput::make('sales')
                                ->label('未稅總金額')
                                ->disabled()
                                ->dehydrated(true),
                            Forms\Components\TextInput::make('amount')
                                ->label('稅金')
                                ->disabled()
                                ->dehydrated(true),
                            Forms\Components\TextInput::make('total_fee')
                                ->label('總計')
                                ->disabled()
                                ->dehydrated(true),
                        ])->columns(3),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('invoice_date')
                    ->label('發票日期')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice_no')
                    ->label('發票號碼')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.full_title')
                    ->label('客戶名稱 / 統編')
                    ->searchable(query:function ($query, $search) {
                        return $query->orWhereHas('customer', function ($query) use ($search) {
                            $query->where('full_title', 'like', "%{$search}%")
                                  ->orWhere('company_id', 'like', "%{$search}%");
                        });
                    })
                    ->formatStateUsing( function (Model $record){
                        $company_id = Customer::where('id', $record->customer_id)
                                                    ->pluck('company_id')->first();
                        return $record->customer->full_title . '<br />' . $company_id;
                    })->html(),
                Tables\Columns\TextColumn::make('department.name')
                    ->label('部門'),
                Tables\Columns\TextColumn::make('sales')
                    ->label('銷售額'),
                Tables\Columns\TextColumn::make('amount')
                    ->label('稅額'),
                Tables\Columns\TextColumn::make('total_fee')
                    ->label('發票總金額'),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '未開立';
                        }else if($record['status'] == 1){
                            return '已開立';
                        }else if($record['status'] == 2){
                            return '已作廢';
                        }else{
                            return '未開立';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '未開立' => 'gray',
                        '已開立' => 'success',
                        '已作廢' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('複製連結')
                    ->label('')
                    ->default('複製連結')
                    ->color('danger')
                    ->copyable()
                    ->copyableState(function (Invoice $record){
                        // dd($record);
                        return "https://www.giveme.com.tw/invoice.do?action=invoicePrint&code=".$record->invoice_no.'&uncode=53029218';
                    })
                    ->copyMessage(function (Invoice $record) {
                        return'已複製發票連結';
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('customButton') // 自定义按钮名称
                    ->button() // 定义为按钮
                    ->label('列印') // 按钮文本
                    ->color('primary') // 按钮颜色
                    ->icon('heroicon-o-printer') // 图标
                    ->visible(fn($record) => $record->status == 1 || $record->status == 2)
                    // ->url(fn($record) => route('invoice.print', ['code' => $record->invoice_no]))
                    ->url(fn($record) => 'https://www.giveme.com.tw/invoice.do?action=invoicePrint&code='.$record->invoice_no.'&uncode=53029218')
                    ->openUrlInNewTab(),
                Tables\Actions\Action::make('createButton') // 自定义按钮名称
                    ->button() // 定义为按钮
                    ->label('開立') // 按钮文本
                    ->color('success') // 按钮颜色
                    ->icon('heroicon-o-pencil') // 图标
                    ->visible(fn($record) => $record->status == 0)
                    ->action(function ($record) {
                        $rsInvoice = static::generateInvoice($record->id);
                        Log::info('開立'. json_encode($rsInvoice));
                        if($rsInvoice['status']) {
                            Notification::make()
                                ->title($rsInvoice['message'])
                                // ->body('自訂動作已成功執行！')
                                ->success()
                                ->send();
                        }else {
                            Notification::make()
                                ->title($rsInvoice['message'])
                                ->danger()
                                ->send();
                        }
                    }), // 在新标签页中打开链接
                Tables\Actions\Action::make('cancelButton') // 自定义按钮名称
                    ->button() // 定义为按钮
                    ->label('作廢') // 按钮文本
                    ->color('danger') // 按钮颜色
                    ->icon('heroicon-o-x-mark') // 图标
                    ->visible(fn($record) => $record->status == 1)
                    ->action(function ($record) {
                        $rsInvoice = static::cancelInvoice($record->id);
                        Log::info('開立'. json_encode($rsInvoice));
                        if($rsInvoice['status']) {
                            Notification::make()
                                ->title($rsInvoice['message'])
                                // ->body('自訂動作已成功執行！')
                                ->success()
                                ->send();
                        }else {
                            Notification::make()
                                ->title($rsInvoice['message'])
                                ->danger()
                                ->send();
                        }
                    }), // 在新标签页中打开链接
                // Tables\Actions\Action::make('customButton') // 按钮名称
                //     ->button()
                //     ->label('列印') // 按钮显示的文本
                //     ->color('primary') // 按钮颜色
                //     ->icon('heroicon-o-printer') // 按钮图标
                //     ->action(ModalAction::make('列印')
                //         ->modalTitle('列印')
                //         ->modalContent(view('invoices/print-view', ['url' => 'https://www.giveme.com.tw/invoice.do?action=invoicePrint&code=FF66600548&uncode=53418005']))
                //         ->modalFooterActions([
                //             // 在模态框底部添加按钮，例如"关闭"按钮
                //         ])
                //         ),
                    // ->action(function ($record) {
                    //     return <<<'SCRIPT'
                    //         <script>
                    //             window.open('https://www.giveme.com.tw/invoice.do?action=invoicePrint&code=FF66600548&uncode=53418005', '_blank');
                    //         </script>
                    //     SCRIPT;
                    // }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            // 'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }
}
