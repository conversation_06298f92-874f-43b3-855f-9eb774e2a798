<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VendorResource\Pages;
use App\Filament\Resources\VendorResource\RelationManagers;
use App\Models\Vendor;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class VendorResource extends Resource
{
    protected static ?string $model = Vendor::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '廠商管理';
    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('基本資料')->schema([
                    Forms\Components\TextInput::make('title')
                        ->required()
                        ->maxLength(64)
                        ->label('廠商名稱'),
                    Forms\Components\TextInput::make('contact')
                        ->required()
                        ->maxLength(20)
                        ->label('聯絡人'),
                    Forms\Components\TextInput::make('tel')
                        ->maxLength(20)
                        ->label('公司電話'),
                    Forms\Components\TextInput::make('fax')
                        ->maxLength(20)
                        ->label('公司傳真'),
                    Forms\Components\TextInput::make('address')
                        ->maxLength(64)
                        ->label('地址'),
                    Forms\Components\TextInput::make('note')
                        ->maxLength(128)
                        ->label('備註'),
                    Forms\Components\Toggle::make('status')
                        ->default(true)
                        ->label('狀態'),
                    Forms\Components\Textarea::make('bg_position')
                        ->label('簽單文字位置'),
                    Forms\Components\Textarea::make('bg_position2')
                        ->label('出租單文字位置'),
                    Forms\Components\Textarea::make('bg_driver_position')
                        ->label('司機資訊文字位置'),
                    Forms\Components\Textarea::make('bg_order_position')
                        ->label('訂車單文字位置'),
                    Forms\Components\Textarea::make('bg_image')
                        ->label('簽單樣板檔名群組'),
                    Forms\Components\Textarea::make('bg_image2')
                        ->label('出租單樣板檔名'),
                    Forms\Components\Textarea::make('bg_driver_template')
                        ->label('司機資訊樣板檔名'),
                    Forms\Components\Textarea::make('bg_order_template')
                        ->label('訂車單樣板檔名'),

                ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->label('廠商名稱'),
                Tables\Columns\TextColumn::make('contact')
                    ->searchable()
                    ->label('聯絡人'),
                Tables\Columns\TextColumn::make('tel')
                    ->searchable()
                    ->label('公司電話'),
                Tables\Columns\IconColumn::make('status')
                    ->boolean()
                    ->label('狀態'),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVendors::route('/'),
            'create' => Pages\CreateVendor::route('/create'),
            'edit' => Pages\EditVendor::route('/{record}/edit'),
        ];
    }
}
