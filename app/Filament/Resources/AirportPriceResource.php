<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\AirportPrice;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Radio;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\AirportPriceResource\Pages;
use App\Filament\Resources\AirportPriceResource\RelationManagers;

class AirportPriceResource extends Resource
{
    protected static ?string $model = AirportPrice::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '價格管理';
    protected static ?string $navigationGroup = '一般管理';
    protected static ?int $navigationSort = 12;
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id); 3 is test james
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id==5){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_type')
                    ->label('類別')
                    ->toggleable()
                    ->sortable()
                    ->searchable()
                    ->getStateUsing( function (AirportPrice $record){ // 修改這裡：Model 改為 AirportPrice
                        if($record->order_type == 0){
                            return '機場接送';
                        }else{
                            return '兩地接送';
                        }
                    })
                    ->extraAttributes(['class' => 'hide-on-mobile']),
                Tables\Columns\TextColumn::make('car_type')
                    ->label('車型')
                    ->sortable()
                    ->searchable()
                    ->getStateUsing( function (AirportPrice $record){ // 修改這裡：Model 改為 AirportPrice
                        if($record->car_type == 5){
                            return '五人座';
                        }else if($record->car_type == 7){
                            return '七人座';
                        }else if($record->car_type == 9){
                            return '九人座';
                        }else{
                            return '五人座';
                        }
                    }),
                Tables\Columns\TextColumn::make('from_district_name')
                    ->label('A 點')
                    ->sortable()
                    ->searchable(['from_city_name', 'from_district_name', 'from_other_name'])
                    ->formatStateUsing(function (AirportPrice $record) { // 修改這裡：Model 改為 AirportPrice
                        $city = $record->from_city_name ?? ''; // 如果為空，默認為空字串
                        $district = $record->from_district_name ?? ''; // 如果為空，默認為空字串
                        // $other_name = $record->from_other_name ?? ''; // 如果為空，默認為空字串
                        return trim("{$city} {$district}");
                    }),
                Tables\Columns\TextColumn::make('to_city_name')
                    ->label('B 點')
                    ->sortable()
                    ->searchable(['to_city_name', 'to_district_name', 'to_other_name'])
                    ->formatStateUsing(function (AirportPrice $record) { // 修改這裡：Model 改為 AirportPrice
                        $city = $record->to_city_name ?? ''; // 如果為空，默認為空字串
                        $district = $record->to_district_name ?? ''; // 如果為空，默認為空字串
                        return trim("{$city} {$district}");
                    }),
                Tables\Columns\TextInputColumn::make('price')
                    ->label('價格')
                    ->width(100)
                    ->sortable()
                    ->searchable(),
            ])
            ->filters([
                Filter::make('order_type')
                    ->label('類別')
                    ->default(0)
                    ->query(function ($query, $data) {
                        if ($data['order_type']) {
                            $query->where('order_type', $data['order_type']);
                        }
                    })
                    ->form([
                        Radio::make('order_type')
                            ->label('車輛類別')
                            ->inline()
                            ->default(0)
                            ->live()
                            ->options([
                                0 => '機場接送',
                                // 1 => '兩地接送',
                            ])
                    ]),
                Filter::make('car_type')
                    ->query(function ($query, $data) {
                        if ($data['car_type']) {
                            $query->where('car_type', $data['car_type']);
                        }
                    })
                    ->form([
                        Radio::make('car_type')
                            ->label('車輛類別')
                            ->inline()
                            ->default(5)
                            ->options([
                                5 => '五人座',
                                7 => '七人座',
                                9 => '九人座',
                            ]),
                    ]),
                SelectFilter::make('from_district_id')
                    ->label('機場 => ')
                    ->default(110903)
                    ->options(
                        [
                                110903 => '桃園機場',
                                110900 => '松山機場',
                                120901 => '台中機場',
                            ]
                    ),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAirportPrices::route('/'),
            'create' => Pages\CreateAirportPrice::route('/create'),
            'edit' => Pages\EditAirportPrice::route('/{record}/edit'),
        ];
    }
}
