<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Crypt;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\SafetripResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\SafetripResource\RelationManagers;

class SafetripResource extends Resource
{
    protected static ?string $model = Dispatch::class;
    protected static ?string $navigationLabel = '派車單管理ys';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id); 3 is test james
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id == 8){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id)->whereNull('deleted_at');
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶資料')
                        ->schema([
                            Forms\Components\Hidden::make('dispatch_no')
                                ->default(fn () => Dispatch::generateDispatchNumber())
                                ->reactive(),
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(fn () => Filament::auth()->user()->vendor_id)
                                ->reactive(),
                            Forms\Components\Select::make('customer_id')
                                ->relationship('customer', 'title', function ($query) {
                                    return $query->where('vendor_id', Filament::auth()->user()->vendor_id)
                                                ->orderBy('sequence', 'asc');
                                })
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Hidden::make('vendor_id')
                                            ->default(fn () => Filament::auth()->user()->vendor_id)
                                            ->reactive(),
                                        Forms\Components\TextInput::make('title')
                                            ->label('客戶名稱')
                                            ->required()
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('address')
                                            ->label('地址'),
                                        Forms\Components\TextInput::make('telephone')
                                            ->label('電話'),
                                        Forms\Components\TextInput::make('contact')
                                            ->label('聯絡人'),
                                        Forms\Components\TextInput::make('contact_mobile')
                                            ->label('聯絡人電話'),
                                    ])->columns(2),
                                ])
                                ->live()
                                ->searchable()
                                ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                                ->preload()
                                ->label('客戶名稱')
                                // ->default('1')
                                ->afterStateUpdated(function ($state, callable $set) {
                                    // --- Existing logic ---
                                    $set('department_id', null);

                                    // --- New logic to populate passenger fields ---
                                    if ($state) { // Check if a customer ID is selected
                                        $customer = Customer::find($state);
                                        if ($customer) { // Check if customer exists
                                            $set('passenger_name', $customer->contact);
                                            $set('passenger_mobile', $customer->contact_mobile);
                                        } else {
                                            // Optional: Clear fields if customer not found
                                            $set('passenger_name', null);
                                            $set('passenger_mobile', null);
                                        }
                                    } else {
                                        // Optional: Clear fields if customer is deselected
                                        $set('passenger_name', null);
                                        $set('passenger_mobile', null);
                                    }
                                    // --- End of new logic ---
                                })
                                ->reactive()
                                ->required(),
                            // Forms\Components\Select::make('department_id')
                            //     ->relationship('department', 'name')
                            //     // ->default(0)
                            //     ->nullable()
                            //     ->options(
                            //         function ($get) {
                            //             $customerId = $get('customer_id');
                            //             return Department::where('customer_id', $customerId)
                            //                                 ->where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id');
                            //         }
                            //     )
                            //     ->label('部門名稱')
                            //     ->createOptionForm([
                            //         Grid::make()->schema([
                            //             Forms\Components\Select::make('customer_id')
                            //                 ->relationship('customer', 'title')
                            //                 ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('title', 'id'))
                            //                 ->label('公司名稱')
                            //                 // ->hidden()
                            //                 // ->default(fn () => 2)
                            //                 ->required(),
                            //             Forms\Components\TextInput::make('name')
                            //                 ->label('部門名稱')
                            //                 ->required(),

                            //         ])->columns(3),
                            //     ])
                            //     ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                            //         $data['vendor_id'] = Filament::auth()->user()->vendor_id;
                            //         return $data;
                            //     }))
                            //     ->live()
                            //     ->reactive(),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('passenger_name')
                                    ->label('乘客名稱')
                                    ->placeholder('請輸入乘客大名'),
                                Forms\Components\TextInput::make('passenger_mobile')
                                    ->label('乘客電話')
                                    ->placeholder('請輸入乘客電話'),
                            ])
                        ])->columns(2),
                    Section::make('乘車資料')
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_date')
                                ->placeholder('請選擇日期與時間')
                                ->label('派車日期/時間')
                                ->displayFormat('Y-m-d H:i')
                                ->seconds(false)
                                // ->minutesStep(10)
                                ->firstDayOfWeek(7)
                                ->native(false),
                            Forms\Components\DateTimePicker::make('end_date')
                                ->hidden(),
                            Forms\Components\Select::make('pay_type')
                                ->label('付款方式')
                                ->default(0)
                                ->options([
                                    '0' => '月結',
                                    '1' => '現金',
                                    '2' => 'LINE PAY',
                                    '3' => '匯款',
                                ])
                                ->live()
                                ->reactive(),
                            Forms\Components\Radio::make('location_type')
                                ->label('派車類別')
                                ->default('0')
                                ->options([
                                    // '1' => '接機',
                                    // '2' => '送機',
                                    '0' => '長照專車',
                                ])
                                ->default('1')
                                ->inline()
                                ->live()
                                ->columnSpanFull(),

                            Forms\Components\TextInput::make('start_location')
                                ->label('上車地點')
                                ->placeholder('請輸入上車地點')
                                // ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2')
                                ->reactive(),
                            Forms\Components\TextInput::make('end_location')
                                ->label('下車地點')
                                ->placeholder('請輸入下車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1')
                                ->reactive(),

                        ])->columns(2),

                ]),
                Group::make([
                    Section::make('派車資料')
                    ->schema([
                        Forms\Components\Select::make('driver_id')
                            ->searchable()
                            ->preload()
                            ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id'))
                            ->label('司機')
                            ->live()
                            ->disableOptionWhen(fn (string $value): bool => $value === 2)
                            ->afterStateUpdated(function ($state, callable $set) {
                                if($state){
                                    $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                }else{
                                    $set('status', 0);
                                }
                            }),
                        Forms\Components\Select::make('status')
                            ->label('派車狀態')
                            ->default(0)
                            // ->disabled()
                            ->options([
                                '0' => '未派遣',
                                '1' => '已指派司機',
                                '2' => '已完成',
                                '3' => '已取消',
                                '101' => '錯誤',
                                '102' => '待審核'
                            ]),
                        Grid::make()->schema([
                            Forms\Components\TextInput::make('rental_cost')
                                ->label('費用')
                                ->placeholder('請輸入費用')
                                ->numeric(),
                            Forms\Components\TextInput::make('driver_fee')
                                ->label('司機費用')
                                ->placeholder('請輸入司機費用')
                                ->numeric(),
                            Forms\Components\TextInput::make('carno2')
                                ->label('其他車號')
                                ->placeholder('請輸入其他車牌號碼'),
                        ])->columns(3),
                    ])->columns(2),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 101){
                            return '異常';
                        }else if($record['status'] == 102){
                            return '待審核';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'warning',
                        '異常' => 'danger',
                        '待審核' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->copyable()
                    ->copyableState(function (Dispatch $record){
                        if($record->driver_id == 11){
                            $timestamp = time();
                            return "http://carv2-web.chihlistudio.com/sign/" . Crypt::encryptString($record->dispatch_no)."?v=".$timestamp;
                        }
                    })
                    // ->copyMessage('已複製')
                    ->copyMessage(function (Dispatch $record) {
                        return $record->driver_id == 11 ? '已複製簽名鏈接' : '請選擇外派司機';
                    })
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSafetrips::route('/'),
            'create' => Pages\CreateSafetrip::route('/create'),
            'edit' => Pages\EditSafetrip::route('/{record}/edit'),
        ];
    }
}
