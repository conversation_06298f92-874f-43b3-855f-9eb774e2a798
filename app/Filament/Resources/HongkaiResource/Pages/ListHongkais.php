<?php

namespace App\Filament\Resources\HongkaiResource\Pages;

use Filament\Actions;
use App\Models\Dispatch;
use Illuminate\Support\Facades\Cache;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\HongkaiResource;
use Filament\Resources\Pages\ListRecords\Tab;

class ListHongkais extends ListRecords
{
    protected static string $resource = HongkaiResource::class;
    protected static ?string $title = '派車單列表';
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增派車單'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
    public function getTabs(): array
    {
        return [
            '今日' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_today_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->toDateString())
                                ->count();
                        }))
                        ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('start_date', '=', now()->toDateString())->orderBy('start_date', 'asc')),
            '明日' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_tomorrow_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->addDay()->toDateString())
                                ->count();
                        }))
                        ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('start_date', '=', now()->addDay()->toDateString())->orderBy('start_date', 'asc')),
            '本周' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_this_week_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->count();
                        }))
                        ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])->orderBy('start_date', 'asc')),
            '本月' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_this_month_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])
                                ->count();
                        }))
                        ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])->orderBy('start_date', 'desc')),

            '已完成' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_completed_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->where('status', 2)
                                ->count();
                        }))
                        ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 2)->orderBy('start_date', 'desc')),
            '全部' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_all_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)->count();
                        })),
        ];
    }
    protected function getFilteredCount(Builder $query): int
    {
        return (clone $query)->count();
    }
}
