<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Support\Html;
use Filament\Tables\Table;
use App\Traits\CreateImage;
use Filament\Facades\Filament;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Crypt;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Exports\DispatchExporter;
use Filament\Notifications\Actions\Action;
use Filament\Tables\Columns\Summarizers\Sum;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\Dispatchv2Resource\Pages;
use App\Filament\Resources\Dispatchv2Resource\RelationManagers;

class Dispatchv2Resource extends Resource
{
    use CreateImage;
    protected static ?string $model = Dispatch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '派車管理';
    protected static ?int $navigationSort = 2;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id); 3 is test james
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id == 3 || Filament::auth()->user()->id == 5 || Filament::auth()->user()->id == 6){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶資料')
                        ->schema([
                            Forms\Components\Hidden::make('dispatch_no')
                                ->default(fn () => Dispatch::generateDispatchNumber())
                                ->reactive(),
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(fn () => Filament::auth()->user()->vendor_id)
                                ->reactive(),
                            Forms\Components\Select::make('customer_id')
                                ->relationship('customer', 'title', function ($query) {
                                    return $query->where('vendor_id', Filament::auth()->user()->vendor_id)
                                                ->orderBy('sequence', 'asc');
                                })
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Hidden::make('vendor_id')
                                            ->default(fn () => Filament::auth()->user()->vendor_id)
                                            ->reactive(),
                                        Forms\Components\TextInput::make('title')
                                            ->label('客戶名稱')
                                            ->required()
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('address')
                                            ->label('地址'),
                                        Forms\Components\TextInput::make('telephone')
                                            ->label('電話'),
                                        Forms\Components\TextInput::make('contact')
                                            ->label('聯絡人'),
                                        Forms\Components\TextInput::make('contact_mobile')
                                            ->label('聯絡人電話'),
                                    ])->columns(2),
                                ])
                                ->live()
                                ->searchable()
                                ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                                ->preload()
                                ->label('客戶名稱')
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $set('department_id', null);
                                })
                                ->reactive()
                                ->required(),
                            Forms\Components\Select::make('department_id')
                                ->relationship('department', 'name')
                                ->nullable()
                                ->options(
                                    function ($get) {
                                        $customerId = $get('customer_id');
                                        return Department::where('customer_id', $customerId)
                                                            ->where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id');
                                    }
                                )
                                ->label('部門名稱')
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Select::make('customer_id')
                                            ->relationship('customer', 'title')
                                            ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('title', 'id'))
                                            ->label('公司名稱')
                                            ->required(),
                                        Forms\Components\TextInput::make('name')
                                            ->label('部門名稱')
                                            ->required(),
                                    ])->columns(3),
                                ])
                                ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                                    $data['vendor_id'] = Filament::auth()->user()->vendor_id;
                                    return $data;
                                }))
                                ->live()
                                ->reactive(),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('passenger_name')
                                    ->label('乘客名稱')
                                    ->placeholder('請輸入乘客大名'),
                                Forms\Components\TextInput::make('passenger_mobile')
                                    ->label('乘客電話')
                                    ->placeholder('請輸入乘客電話'),
                            ])
                        ])->columns(2),
                    Section::make('乘車資料')
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_date')
                                ->placeholder('請選擇日期與時間')
                                ->label('派車日期/時間')
                                ->displayFormat('Y-m-d H:i')
                                ->seconds(false)
                                // ->minutesStep(10)
                                ->firstDayOfWeek(7)
                                ->native(false),
                            Forms\Components\DateTimePicker::make('end_date')
                                ->hidden(),
                            Forms\Components\Select::make('cartype_id')
                                ->label('車輛類別')
                                // >request()
                                ->relationship('cartype', 'name')
                                // ->default(0)
                                ->options(Cartype::where('vendor_id', Filament::auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('name', 'id'))
                                ->live()
                                ->reactive(),
                            Forms\Components\Radio::make('location_type')
                                ->label('派車類別')
                                ->options([
                                    '1' => '接機',
                                    '2' => '送機',
                                    '0' => '其他',
                                    '3' => '包車',
                                ])
                                ->default('1')
                                ->inline()
                                ->live()
                                ->columnSpanFull(),
                            Forms\Components\Select::make('start_location')
                                ->label('上車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '桃園機場',
                                    '5' => '台中機場',
                                    '6' => '小港機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '1')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('start_location')
                                ->label('上車地點')
                                ->placeholder('請輸入上車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2' || $get('location_type') == '3')
                                ->reactive(),
                            Forms\Components\TextInput::make('end_location')
                                ->label('下車地點')
                                ->placeholder('請輸入下車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1' || $get('location_type') == '3')
                                ->reactive(),
                            Forms\Components\Select::make('end_location')
                                ->label('下車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '桃園機場',
                                    '5' => '台中機場',
                                    '6' => '小港機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '2')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('flight_no')
                                ->label('航班編號')
                                ->placeholder('請輸入航班編號'),
                            Forms\Components\Select::make('pay_type')
                                ->label('付款方式')
                                ->default(0)
                                ->options([
                                    '0' => '月結',
                                    '1' => '現金',
                                    '2' => 'LINE PAY',
                                    '3' => '匯款',
                                    '4' => '刷卡',
                                    '5' => '入房帳',
                                ])
                                ->live()
                                ->reactive(),

                            Grid::make()->schema([
                                Forms\Components\TextInput::make('num_of_people')
                                    ->numeric()
                                    ->label('乘車人數')
                                    ->placeholder('請輸入搭乘人數'),
                                Forms\Components\TextInput::make('num_of_bags')
                                    ->numeric()
                                    ->label('行李數')
                                    ->placeholder('請輸入行李數'),
                                Forms\Components\TextInput::make('child_seat')
                                    ->numeric()
                                    ->default(0)
                                    ->label('安全座椅數')
                                    ->placeholder('請輸入安全座椅數'),
                                Forms\Components\TextInput::make('booster_pad')
                                    ->numeric()
                                    ->default(0)
                                    ->label('增高墊數')
                                    ->placeholder('請輸入增高墊數'),
                            ])->columns(4),
                        ])->columns(2),
                    Section::make('派車資料')
                        ->schema([
                            Forms\Components\Select::make('driver_id')
                                ->searchable()
                                ->preload()
                                ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id'))
                                ->label('司機')
                                ->live()
                                ->disableOptionWhen(fn (string $value): bool => $value === 2)
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if($state){
                                        $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                    }else{
                                        $set('status', 0);
                                    }
                                }),
                            Forms\Components\Select::make('status')
                                ->label('派車狀態')
                                ->default(0)
                                // ->disabled()
                                ->options([
                                    '0' => '派遣中',
                                    '1' => '已指派司機',
                                    '2' => '已完成',
                                    '3' => '已取消',
                                ]),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('carno2')
                                    ->label('其他車號')
                                    ->placeholder('請輸入其他車牌號碼'),
                            ])->columns(2),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('rental_cost')
                                    ->label('費用')
                                    ->placeholder('請輸入費用')
                                    ->numeric(),

                                Forms\Components\TextInput::make('driver_fee')
                                    ->label('司機費用')
                                    ->placeholder('請輸入司機費用')
                                    ->numeric(),
                                Forms\Components\TextInput::make('deposit')
                                    ->label('已付訂金')
                                    ->placeholder('請輸入訂金費用')
                                    ->numeric(),
                            ])->columns(3),
                            // Forms\Components\TextInput::make('return_fee')
                            //     ->label('回金')
                            //     ->visible(fn ($get) => $get('pay_type') == '1'),
                            // Forms\Components\TextInput::make('return_boss')
                            //     ->label('車趟來源')
                            //     ->visible(fn ($get) => $get('pay_type') == '1'),
                            Grid::make()->schema([
                                Forms\Components\Toggle::make('return')
                                    ->hiddenOn('edit')
                                    ->default(0)
                                    ->live()
                                    ->reactive()
                                    ->label('回程'),
                                Forms\Components\DateTimePicker::make('return_date')
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程日期/時間')
                                    ->displayFormat('Y-m-d H:i')
                                    ->seconds(false)
                                    ->firstDayOfWeek(7)
                                    ->native(false),
                                Forms\Components\TextInput::make('return_flight_no')
                                    // ->default(0)
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程航班編號'),
                            ])->columns(3),
                            Grid::make()->schema([
                                Forms\Components\Toggle::make('printing')
                                    ->default(1)
                                    ->label('輸出派車單'),
                                Forms\Components\Toggle::make('print_customer')
                                    ->default(1)
                                    ->label('輸出訂車單')
                                    ->visible(function(){
                                        if(Filament::auth()->user()->vendor_id == 4){
                                            return true;
                                        }
                                        return false;
                                    }),

                            ])->columns(3),
                        ])->columns(2),
                ]),
                Group::make([
                    Section::make('其他資料')
                        ->schema([
                            Group::make([
                                Forms\Components\Textarea::make('note')
                                    ->rows(5)
                                    ->label('備註'),
                            ])->columns(2),
                            Grid::make()->schema([
                                Forms\Components\Placeholder::make('image_path')
                                    ->label('簽單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image_path) . "' onclick=window.open('" . asset('storage/' . $record->image_path) . "?".time()."')>");
                                        }
                                        return new HtmlString('');
                                    }),
                                Forms\Components\Placeholder::make('image2_path')
                                    ->label('出租單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image2_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image2_path) . "' onclick=window.open('" . asset('storage/' . $record->image2_path) . "?".time()."')>");
                                        }
                                        return new HtmlString('');
                                    }),
                                Forms\Components\Placeholder::make('image3_path')
                                    ->label('訂車單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image3_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image3_path) . "' onclick=window.open('" . asset('storage/' . $record->image3_path) . "?".time()."')>");
                                        }
                                        return new HtmlString('');
                                    })
                                    ->visible(function(){
                                        if(Filament::auth()->user()->vendor_id == 4){
                                            return true;
                                        }
                                        return false;
                                    }),
                                Forms\Components\Placeholder::make('driver.driver_info_image_path')
                                    ->label('司機服務單')
                                    ->hiddenOn('create')
                                    ->visible(fn ($get) => $get('image3_path') != null && Filament::auth()->user()->vendor_id == 4)
                                    ->content(function ($record): HtmlString {
                                        if($record->driver_id == null){
                                            return new HtmlString('');
                                        }else{
                                            $driver_image = Driver::where('id', $record->driver_id)->first()->driver_info_image_path;
                                            if(!empty($driver_image)){
                                                return new HtmlString("<img src= '" . asset('storage/'.$driver_image) . "' onclick=window.open('" . asset('storage/' . $driver_image) . "?".time()."')>");
                                            }
                                            return new HtmlString('');
                                        }
                                    }),
                            ])->columns(2),
                        ])
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 101){
                            return '異常';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'warning',
                        '異常' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->copyable()
                    ->copyableState(function (Dispatch $record){
                        if($record->driver_id == 76){
                            $timestamp = time();
                            return "http://carv2-web.chihlistudio.com/sign/" . Crypt::encryptString($record->dispatch_no)."?v=".$timestamp;
                        }
                    })
                    // ->copyMessage('已複製')
                    ->copyMessage(function (Dispatch $record) {
                        return $record->driver_id == 76 ? '已複製簽名鏈接' : '請選擇外派司機';
                    })
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\SelectColumn::make('driver_id')
                    ->label('司機')
                    ->options(
                        Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id')->toArray()
                    )
                    ->afterStateUpdated(function ($state, $record) {
                        // dd($state);
                        if ($state) {
                            $record->status = 1;
                            $record->save();
                            $driver = Driver::where('id', $record->driver_id)->first();
                            $car = Car::where('driver_id', $record->driver_id)->first();
                            $customer = Customer::where('id', $record->customer_id)->first();
                            if($record->department_id != null){
                                $department = Department::where('id', $record->department_id)->first();
                                $record->department_name = $department->name;
                            }
                            $record->customer_title = $customer->title;
                            $record->driver_name = $driver->name;
                            $record->car_license = $car->license;
                            $record->car = $car;
                            // $rs = Dispatch::generateImage($record);
                            // 產生派車單
                            $rs = Dispatch::generateImageV2('dispatch1',$record);
                            $rs2 = Dispatch::generateImageV2('dispatch2',$record);
                            if(!$rs){
                                Notification::make()
                                    ->title('簽單圖片生成失敗!請與車行洽詢!')
                                    ->danger()
                                    ->send();
                                    return;
                            }
                            if(!$rs2){
                                Notification::make()
                                    ->title('出租單圖片生成失敗!請與車行洽詢!')
                                    ->danger()
                                    ->send();
                                    return;
                            }
                            Dispatch::where('id', $record->id)
                                ->update([
                                    // 'signature_file' => 'signatures/' . $fileName,
                                    'image_path' => $rs,
                                    'image2_path' => $rs2,
                                ]);
                        }else{
                            $record->status = 0;
                            $record->save();
                        }
                    })
                    ->extraAttributes([
                        'style' => 'width: 120px;',  // 設定固定寬度為150px
                    ])
                    ,
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    ->label('車型')
                    ->getStateUsing(function (Model $record) {
                        $rs = Cartype::where('id', $record['cartype_id'])->first();
                        if($rs != null){
                            return $rs->short_name;
                        }
                        return '';
                    }),

                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->searchable()
                    // ->limit(30)
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if($record['start_location'] == 1 || $record['end_location'] == 1){
                            $tmpdata = 'TSA 松山機場';
                        }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                            $tmpdata = '桃園機場 T1';
                        }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                            $tmpdata = '桃園機場 T2';
                        }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                            $tmpdata = '桃園機場';
                        }else if($record['start_location'] == 5 || $record['end_location'] == 5){
                            $tmpdata = '台中機場';
                        }else if($record['start_location'] == 6 || $record['end_location'] == 6){
                            $tmpdata = '小港機場';
                        }else{
                            $tmpdata = $record['start_location'];
                        }
                        if($record['location_type'] == 1){
                            return $tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('up_time')
                    ->label('上/下車時間')
                    ->formatStateUsing(function (Model $record) {
                        $upTime = Carbon::parse($record->up_time);
                        $downTime = Carbon::parse($record->down_time);
                        $duration = $upTime->diffInMinutes($downTime);
                        $hours = floor($duration / 60);
                        $minutes = $duration % 60;
                        return $upTime->format('H:i') . ' <br />' .  $downTime->format('H:i') . '<br />計: ' . $hours . ' 時 ' . $minutes . ' 分';
                    })->html(),
                Tables\Columns\TextColumn::make('pay_type')
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return 'LINE PAY';
                            case 3:
                                return '匯款';
                            case 4:
                                return '刷卡';
                            case 5:
                                return '入房帳';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('月結/現金')
                    ,
                Tables\Columns\TextColumn::make('driver_fee')
                    ->label('司機車資'),

                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('金額')
                    ->numeric()
                    ->summarize(Sum::make()->label('總計金額')),
                Tables\Columns\TextColumn::make('driver_fee')
                    ->label('司機車資')
                    ->numeric()
                    ->summarize(Sum::make()->label('總計金額'))
            ])
            ->filters([
                Filter::make('customer_id')
                    ->label('查詢')
                    ->form([
                        Select::make('customer_id')
                            ->label('公司名稱')
                            ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->orderBy('sequence')->pluck('title', 'id'))
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('department_id', null);
                            }),
                        Select::make('department_id')
                            ->label('部門')
                            ->options(function ($get) {
                                return Department::where('customer_id', $get('customer_id'))
                                    ->where('vendor_id', Filament::auth()->user()->vendor_id)
                                    ->pluck('name', 'id');
                            }),
                            // ->options(Department::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // dd($data);
                        return $query
                            ->when($data['customer_id'], fn ($query, $customer_id) => $query->where('customer_id', $customer_id)->where('vendor_id', Filament::auth()->user()->vendor_id))
                            ->when($data['department_id'], fn ($query, $department_id) => $query->where('department_id', $department_id))->where('vendor_id', Filament::auth()->user()->vendor_id);
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['customer_id']) {
                            $customer_name = Customer::find($data['customer_id'])->title;
                            $indicators['customer_id'] = '公司: ' . $customer_name;
                        }
                        if ($data['department_id']) {
                            $department_name = Department::find($data['department_id'])->name;
                            $indicators['department_id'] = '部門: ' . $department_name;
                        }
                        return $indicators;
                    })
                    ,
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('start_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('start_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
                SelectFilter::make('status')
                    ->label('狀態')
                    ->searchable()
                    ->options([
                        '0' => '派遣中',
                        '1' => '已指派司機',
                        '2' => '已完成',
                        '3' => '已取消',
                    ]),
            ])
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\Action::make('update-status')
                            ->label('移除客上')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['up_time' => null, 'status' => 1]);
                                // $record->save();
                            }),
                    Tables\Actions\Action::make('update-signature')
                            ->label('移除簽名檔')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['signature_path' => null, 'status' => 1]);
                                // $record->save();
                            }),
                    Tables\Actions\DeleteAction::make(),
                ])
                ->label('')
                ->icon('heroicon-o-cog'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('makeReport')
                        ->label('建立報表')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            // dd($filters);
                            $start_date = $filters['start_date'];
                            $fileName = static::generatePdf('report',$records, $filters, $start_date);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                            Notification::make()
                                ->title('報表已建立')
                                ->body("報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->icon('heroicon-o-document')
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    Tables\Actions\ExportBulkAction::make()
                        ->exporter(DispatchExporter::class)
                        ->label('匯出')
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDispatchv2s::route('/'),
            'create' => Pages\CreateDispatchv2::route('/create'),
            'edit' => Pages\EditDispatchv2::route('/{record}/edit'),
        ];
    }
    // protected static function generatePdf(Collection $records, $customer_data=null, $start_date=null)
    // {
    //     $total = 0;
    //     // dd($customer_data['customer_id']);
    //     $customer = Customer::where('id',$customer_data['customer_id']['customer_id'])->first()->toArray() ?? '客戶名稱';
    //     $department_name = Department::find($customer_data['customer_id']['department_id'])->name ?? '部門名稱';
    //     $sql = "select customer_id, COUNT(id) AS COUNT, SUM(rental_cost) AS sum FROM dispatches WHERE customer_id={$customer_data['customer_id']['customer_id']} AND status=2 AND department_id={$customer_data['customer_id']['department_id']} AND (start_date between '{$start_date["start_date"]} 00:00:00' AND '{$start_date["end_date"]} 23:59:59') GROUP BY customer_id";
    //     $data = DB::select($sql);
    //     foreach ($records as $key => $value) {
    //         if($value->driver_id == null){
    //             $driver_car_license = '';
    //         }else{
    //             $driver_car_license = Car::where('driver_id', '=', $value->driver_id)->pluck('license')->first();

    //         }
    //         $records[$key]->car_license = $driver_car_license;
    //     }
    //     // dd($driver_car_license);
    //     // $customer_title = 'ABC';
    //     $data['customer_title'] = $customer['title'] . ' ' . $department_name;
    //     $data['start_end_date'] = $start_date['start_date'] . ' - ' . $start_date['end_date'];
    //     $data['company_id'] = $customer['company_id'];
    //     $data['items'] = $records;
    //     // dd($data);
    //     // 生成 PDF
    //     $pdf = Pdf::loadView('report.myreport', ['data' => $data])->setPaper('a4', 'portrait');
    //     // 保存 PDF 到本地
    //     $fileName = 'Report_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
    //     Log::info($fileName);
    //     $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
    //     $pdf->save($filePath);
    //     return $fileName;
    // }
}
