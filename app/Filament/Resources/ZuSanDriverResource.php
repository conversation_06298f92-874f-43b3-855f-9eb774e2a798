<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use App\Models\ZuSanDriver;
use App\Traits\CreateImage;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use App\Traits\CreateDriverExcel;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Exports\DispatchExporter;
use Filament\Notifications\Actions\Action;
use Filament\Tables\Columns\Summarizers\Sum;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ZuSanDriverResource\Pages;
use App\Filament\Resources\ZuSanDriverResource\RelationManagers;

class ZuSanDriverResource extends Resource
{
    use CreateImage, CreateDriverExcel;
    protected static ?string $model = Dispatch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '司機報表';
    protected static ?string $navigationGroup = '報表管理';
    protected static ?int $navigationSort = 7;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id); 3 is test james
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id == 7){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id)
                            ->where('status', '!=', 3)
                            ->where('status', '!=', 101);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes(function ($record) {
                        $backgroundColor = ($record->row_color == null) ? 'white' : $record->row_color;
                        return ['style' => "background-color: {$backgroundColor};"];
                    })
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->searchable()
                    ->toggleable()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable()
                    ->toggleable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\TextColumn::make('driver.name')
                    ->label('司機'),
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->toggleable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    // ->toggleable()
                    ->label('車型')
                    ->getStateUsing(function (Model $record) {
                        $rs = Cartype::where('id', $record['cartype_id'])->first();
                        if($rs != null){
                            return $rs->short_name;
                        }
                        return '';
                    }),
                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->searchable()
                    ->toggleable()
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if($record['start_location'] == 1 || $record['end_location'] == 1){
                            $tmpdata = 'TSA 松山機場';
                        }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                            $tmpdata = '桃園機場 T1';
                        }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                            $tmpdata = '桃園機場 T2';
                        }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                            $tmpdata = '桃園機場';
                        }else if($record['start_location'] == 5 || $record['end_location'] == 5){
                            $tmpdata = '台中機場';
                        }else{
                            $tmpdata = $record['start_location'];
                        }
                        $redDot = (!empty($record['pickup_and_dropoff_location']) && $record['pickup_and_dropoff_location'] != '[]')
                        ? '<span style="color: red; margin-left: 4px;">●</span>'
                        : '&nbsp;';
                        if($record['location_type'] == 1){
                            return $redDot.$tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $redDot.$record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $redDot.$record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('pay_type')
                    ->toggleable()
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return 'LINE PAY';
                            case 3:
                                return '匯款';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->toggleable()
                    ->label('月結/現金'),
                Tables\Columns\TextColumn::make('driver_fee')
                    ->toggleable()
                    ->searchable()
                    ->label('司機車資'),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('合計金額')
                    ->numeric()
                    ->toggleable()
                    ->summarize([
                        Sum::make()->label('車資總額'),

                    ]),
                Tables\Columns\TextColumn::make('driver_fee')
                    ->label('司機合計金額')
                    ->numeric()
                    ->toggleable()
                    ->summarize([
                        Sum::make()->label('司機車資總計'),

                    ]),
            ])
            ->defaultSort('start_date', 'asc')
            ->filters([
                SelectFilter::make('driver_id')
                    ->label('司機')
                    ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)
                        ->pluck('name', 'id')),
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->columns(2)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('start_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('start_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('makeReport')
                        ->label('建立報表')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            // dd($filters);
                            $fileName = static::generatePdf('driver_zs',$records, $filters);
                            $downloadUrl = url('storage/driver_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                            Notification::make()
                                ->title('報表已建立')
                                ->body("報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->icon('heroicon-o-document')
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    Tables\Actions\BulkAction::make('makeExcelReport')
                        ->label('建立Excel報表')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            // dd($filters);
                            $fileName = static::generateExcel('driver_zs_excel',$records, $filters);
                            $downloadUrl = url('storage/excel_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                            Notification::make()
                                ->title('報表已建立')
                                ->body("報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('下載')
                                        // ->button()
                                        ->url($downloadUrl,true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->icon('heroicon-o-document')
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    Tables\Actions\ExportBulkAction::make()
                        ->exporter(DispatchExporter::class)
                        ->label('匯出')
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListZuSanDrivers::route('/'),
            // 'create' => Pages\CreateZuSanDriver::route('/create'),
            // 'edit' => Pages\EditZuSanDriver::route('/{record}/edit'),
        ];
    }
}
