<?php

namespace App\Filament\Resources\DepartmentResource\Pages;

use Filament\Actions;
use App\Models\Department;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\DepartmentResource;

class CreateDepartment extends CreateRecord
{
    protected static string $resource = DepartmentResource::class;
    protected static ?string $title = '建立部門';
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd($data);
        $data['vendor_id'] = auth()->user()->vendor_id;
        return $data;
        // Department::create($data);
    }
}
