<?php

namespace App\Filament\Resources\VendorResource\Pages;

use Filament\Actions;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\VendorResource;

class EditVendor extends EditRecord
{
    protected static string $resource = VendorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    // public function handleRecordUpdate(Model $record, array $data):Vendor
    // {
    //     // dd($record);
    //     // $mydata = '{"a": "100", "b": "220"}';
    //     // $data['bg_position'] = json_encode($mydata);
    //     // $vendor = Vendor::where('id', '=', $record->id)->update([
    //     //     'bg_position' => $data['bg_position'],
    //     // ]);
    //     // dd($vendor);
    //     return $record;
    // }
}
