<?php

namespace App\Filament\Resources\VendorResource\Pages;

use App\Filament\Resources\VendorResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListVendors extends ListRecords
{
    protected static string $resource = VendorResource::class;
    protected static ?string $title = '廠商列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('新增廠商'),
        ];
    }
}
