<?php

namespace App\Filament\Resources\TestResource\Pages;

use Filament\Actions;
use App\Models\Mytest;
use Filament\Actions\Action;
use App\Imports\MytestImport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Cache;
use App\Filament\Resources\TestResource;
use Filament\Notifications\Notification;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Filament\Resources\Pages\ListRecords\Tab;

class ListTests extends ListRecords
{
    protected static string $resource = TestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增派車單T'),
            Action::make('importDispatch')
                ->label('匯入派車單')
                ->form([
                    FileUpload::make('attachments'),
                ])
                ->action(function (array $data) {
                    // $data['vendor_id'] = auth()->user()->vendor_id;
                    // $data['status'] = 0;
                    // $data['start_date'] = now();
                    $file = public_path('storage/'.$data['attachments']);
                    $rs = Excel::import(new MytestImport, $file);
                    Notification::make()
                        ->title('上傳完成')
                        ->success()
                        ->send();
                    // dd($rs);
                    return redirect('/admin/tests');
                    // Dispatch::create($data);
                }),
        ];
    }
    public function getTabs(): array
    {
        return [
            '今日' => Tab::make()
                        ->badge(function () {
                            return Mytest::where('vendor_id', auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->toDateString())
                                ->count();
                        })
                        ->modifyQueryUsing(function ($query) {
                            session(['activeTab' => '今日']);
                            return $query->whereDate('start_date', '=', now()->toDateString())->orderBy('start_date', 'asc');
                        }),
            '明日' => Tab::make()
                        ->badge(function () {
                            return Mytest::where('vendor_id', auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->addDay()->toDateString())
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '明日']);
                            return $query->whereDate('start_date', '=', now()->addDay()->toDateString())->orderBy('start_date', 'asc');
                        }),
            '本周' => Tab::make()
                        ->badge(function () {
                            return Mytest::where('vendor_id', auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本周']);
                            return $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])->orderBy('start_date', 'asc');
                        }),
            '異常' => Tab::make()
                        ->badge(function () {
                            return Mytest::where('vendor_id', auth()->user()->vendor_id)
                                ->where('status', 102)
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '異常']);
                            return $query->where('status', 102)->orderBy('start_date', 'asc');
                        }),
            '全部' => Tab::make()
                        ->badge(function () {
                            session(['activeTab' => '全部']);
                            return Mytest::where('vendor_id', auth()->user()->vendor_id)->count();
                        }),
        ];
    }
}
