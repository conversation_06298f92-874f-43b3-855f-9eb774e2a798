<?php

namespace App\Filament\Resources\TestResource\Pages;

use App\Models\Car;
use App\Models\Dispatch;
use App\Models\Mytest;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Department;
use Illuminate\Support\Facades\Log;
use App\Filament\Resources\TestResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTest extends CreateRecord
{
    protected static string $resource = TestResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function handleRecordCreation(array $data): Dispatch
    {
        // dd($data);
        // $rs = $this->mutateFormDataBeforeCreate($data);
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Dispatch::create($data);
        if(isset($data['driver_id']) && $data['driver_id'] != null){
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            dd($driver);
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = $car;
            $dispatch->car_license = $car->license;
            // $dispatch->car_license = $car->license;
            if($data['location_type'] == 1){
                $dispatch->route = static::getLocationName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . static::getLocationName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }

            // $rs = Lscar::generateImage($dispatch);
            // $rs2 = Lscar::generateImageV2('dispatch2', $dispatch);
            $rs = static::generateImage('dispatch3',$dispatch);
            // $rs2 = Lscar::generateImage('dispatch2', $dispatch);
            // $dispatch = Dispatch::find($dispatch->id);
            Log::info('建立 DISPATCH >> '.json_encode($dispatch));
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            // $dispatch->image2_path = $rs2;
            $dispatch->save();
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->department_name = empty($department->name) ? '' : $department->name;
            $dispatch->car = [];
            $dispatch->car_license = '';
            // dd($dispatch);
            if($data['location_type'] == 1){
                $dispatch->route = static::getLocationName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . static::getLocationName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $rs = static::generateImage('dispatch3', $dispatch);
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            $dispatch->save();
        }
        // $data['dispatch_no'] = $rs->dispatch_no;
        return $dispatch;
    }
}
