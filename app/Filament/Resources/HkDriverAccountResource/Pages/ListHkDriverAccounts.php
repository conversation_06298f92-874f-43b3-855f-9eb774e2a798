<?php

namespace App\Filament\Resources\HkDriverAccountResource\Pages;

use App\Filament\Resources\HkDriverAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListHkDriverAccounts extends ListRecords
{
    protected static string $resource = HkDriverAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
