<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Lscar;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Paytype;
use App\Models\Customer;
use App\Models\Location;
use Filament\Forms\Form;
use App\Models\Department;
use App\Models\Statustype;
use Filament\Tables\Table;
use App\Traits\CreateImage;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\LscarResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\LscarResource\RelationManagers;

class LscarResource extends Resource
{
    use CreateImage;
    protected static ?string $model = Lscar::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '派車管理ls';
    protected static ?int $navigationSort = 4;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id);
        if(auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    // Section::make('客戶資料')
                    //     ->schema([
                    Tabs::make('tabs')
                        ->tabs([
                            Tabs\Tab::make('tabs-1')
                                ->label('客戶資料')
                                ->schema([
                                    Forms\Components\Hidden::make('dispatch_no')
                                        ->default(fn () => Lscar::generateDispatchNumber())
                                        ->reactive(),
                                    Forms\Components\Hidden::make('vendor_id')
                                        ->default(fn () => auth()->user()->vendor_id)
                                        ->reactive(),
                                    Grid::make()->schema([
                                        Forms\Components\Select::make('customer_id')
                                            ->relationship('customer', 'title', function ($query) {
                                                return $query->where('vendor_id', auth()->user()->vendor_id)
                                                            ->orderBy('sequence', 'asc');
                                            })
                                            ->createOptionForm([
                                                Grid::make()->schema([
                                                    Forms\Components\Hidden::make('vendor_id')
                                                        ->default(fn () => auth()->user()->vendor_id)
                                                        ->reactive(),
                                                    Forms\Components\TextInput::make('title')
                                                        ->label('客戶名稱')
                                                        ->required()
                                                        ->columnSpanFull(),
                                                    Forms\Components\TextInput::make('address')
                                                        ->label('地址'),
                                                    Forms\Components\TextInput::make('telephone')
                                                        ->label('電話'),
                                                    Forms\Components\TextInput::make('contact')
                                                        ->label('聯絡人'),
                                                    Forms\Components\TextInput::make('contact_mobile')
                                                        ->label('聯絡人電話'),
                                                ])->columns(2),
                                            ])
                                            ->live()
                                            ->searchable()
                                            ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                                            ->preload()
                                            ->label('客戶名稱')
                                            ->afterStateUpdated(function ($state, callable $set) {
                                                $set('department_id', null);
                                            })
                                            ->reactive()
                                            ->required(),
                                        Forms\Components\Select::make('department_id')
                                            ->relationship('department', 'name')
                                            ->nullable()
                                            ->options(
                                                function ($get) {
                                                    $customerId = $get('customer_id');
                                                    return Department::where('customer_id', $customerId)
                                                                        ->where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id');
                                                }
                                            )
                                            ->label('部門名稱')
                                            ->createOptionForm([
                                                Grid::make()->schema([
                                                    Forms\Components\Select::make('customer_id')
                                                        ->relationship('customer', 'title')
                                                        ->options(Customer::where('vendor_id', auth()->user()->vendor_id)->pluck('title', 'id'))
                                                        ->label('公司名稱')
                                                        ->required(),
                                                    Forms\Components\TextInput::make('name')
                                                        ->label('部門名稱')
                                                        ->required(),
                                                ])->columns(3),
                                            ])
                                            ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                                                $data['vendor_id'] = auth()->user()->vendor_id;
                                                return $data;
                                            }))
                                            ->live()
                                            ->reactive(),
                                        ]),
                                    Grid::make()->schema([
                                        Forms\Components\TextInput::make('passenger_name')
                                            ->label('乘客名稱')
                                            ->placeholder('請輸入乘客大名'),
                                        Forms\Components\TextInput::make('passenger_mobile')
                                            ->label('乘客電話')
                                            ->placeholder('請輸入乘客電話'),
                                    ])->columns([
                                        'sm' => 2,  // 手機上每列顯示1個欄位
                                        'md' => 2,  // 平板上每列顯示2個欄位
                                        'lg' => 2,  // 桌面上每列顯示3個欄位
                                    ])
                                ]),
                            Tabs\Tab::make('tabs-2')
                                ->label('乘車資料')
                                ->schema([
                                    Grid::make()->schema([
                                        Forms\Components\DateTimePicker::make('start_date')
                                            ->placeholder('請選擇日期與時間')
                                            ->label('派車日期/時間')
                                            ->displayFormat('Y-m-d H:i')
                                            ->seconds(false)
                                            // ->minutesStep(10)
                                            ->firstDayOfWeek(7)
                                            ->native(false),
                                        Forms\Components\DateTimePicker::make('end_date')
                                            ->hidden(),
                                        Forms\Components\Select::make('cartype_id')
                                            ->label('車輛類別')
                                            // >request()
                                            ->relationship('cartype', 'name')
                                            ->options(Cartype::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('name', 'id'))
                                            ->live()
                                            ->afterStateHydrated(fn ($set, $state) => $state ?? $set('cartype_id', 9))
                                            ->reactive(),
                                        Forms\Components\Select::make('pay_type')
                                            ->label('付款方式')
                                            ->relationship('paytype', 'title')
                                            ->options(options: Paytype::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('title', 'id'))
                                            ->afterStateHydrated(fn ($set, $state) => $state ?? $set('pay_type', 1))
                                            ->live()
                                            ->reactive(),
                                    ])->columns(3),
                                    Forms\Components\Radio::make('location_type')
                                        ->label('派車類別')
                                        ->options([
                                            '1' => '接機',
                                            '2' => '送機',
                                            '3' => '包車',
                                            '4' => '洽公',
                                            '0' => '其他',
                                        ])
                                        ->default('1')
                                        ->inline()
                                        ->live()
                                        ->columnSpanFull(),
                                    Grid::make()->schema([
                                        Forms\Components\Select::make('start_location')
                                            ->label('上車地點')
                                            ->relationship('cartype', 'name')
                                            ->options(options: Location::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('title', 'id'))
                                            ->visible(fn ($get) => $get('location_type') == '1')
                                            ->live()
                                            ->reactive(),
                                        Forms\Components\TextInput::make('start_location')
                                            ->label('上車地點')
                                            ->placeholder('請輸入上車地點')
                                            ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2')
                                            ->reactive(),
                                        Forms\Components\TextInput::make('end_location')
                                            ->label('下車地點')
                                            ->placeholder('請輸入下車地點')
                                            ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1')
                                            ->reactive(),
                                        Forms\Components\Select::make('end_location')
                                            ->label('下車地點')
                                            ->options(options: Location::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('title', 'id'))
                                            ->visible(fn ($get) => $get('location_type') == '2')
                                            ->live()
                                            ->reactive(),
                                        Forms\Components\TextInput::make('flight_no')
                                            ->label('航班編號')
                                            ->placeholder('請輸入航班編號'),
                                    ])->columns(3),

                                    Grid::make()->schema([
                                        Forms\Components\TextInput::make('num_of_people')
                                            ->numeric()
                                            ->label('乘車人數')
                                            ->placeholder('請輸入搭乘人數'),
                                        Forms\Components\TextInput::make('num_of_bags')
                                            ->numeric()
                                            ->label('行李數')
                                            ->placeholder('請輸入行李數'),
                                        Forms\Components\TextInput::make('child_seat')
                                            ->numeric()
                                            ->default(0)
                                            ->label('安全座椅數')
                                            ->placeholder('請輸入安全座椅數'),
                                        Forms\Components\TextInput::make('booster_pad')
                                            ->numeric()
                                            ->default(0)
                                            ->label('增高墊數')
                                            ->placeholder('請輸入增高墊數'),
                                    ])->columns(4),
                                ]),
                        ])
                        ->activeTab(1),

                        // ])->columns([
                        //     'sm' => 2,  // 手機上每列顯示1個欄位
                        //     'md' => 2,  // 平板上每列顯示2個欄位
                        //     'lg' => 2,  // 桌面上每列顯示3個欄位
                        // ]),
                        // Section::make('乘車資料')
                        //     ->schema([

                        //     ])->columns(2),
                    ]),
                Group::make([

                            Tabs::make('Tabs')
                                ->tabs([
                                    Tabs\Tab::make('tab1')
                                        ->label("派車資料")
                                        ->schema([
                                            Grid::make()->schema([
                                                Forms\Components\Select::make('driver_id')
                                                ->searchable()
                                                ->preload()
                                                ->options(Driver::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id'))
                                                ->label('司機')
                                                ->live()
                                                ->disableOptionWhen(fn (string $value): bool => $value === 2)
                                                ->afterStateUpdated(function ($state, callable $set) {
                                                    if($state){
                                                        $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                                    }else{
                                                        $set('status', 0);
                                                    }
                                                }),
                                            Forms\Components\Select::make('status')
                                                ->label('派車狀態')
                                                ->default(0)
                                                ->options(options: Statustype::where('vendor_id', auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('title', 'id')),
                                                Forms\Components\TextInput::make('carno2')
                                                    ->label('其他車號')
                                                    ->placeholder('請輸入其他車牌號碼'),
                                            ])->columns(3),
                                            Grid::make()->schema([
                                                Forms\Components\TextInput::make('rental_cost')
                                                    ->label('費用')
                                                    ->placeholder('請輸入費用')
                                                    ->numeric(),

                                                Forms\Components\TextInput::make('driver_fee')
                                                    ->label('司機費用')
                                                    ->placeholder('請輸入司機費用')
                                                    ->numeric(),
                                                Forms\Components\TextInput::make('deposit')
                                                    ->label('已付訂金')
                                                    ->placeholder('請輸入訂金費用')
                                                    ->numeric(),
                                            ])->columns(3),
                                        ]),
                                    Tabs\Tab::make('tab2')
                                        ->hiddenOn('edit')
                                        ->label("其他時間")
                                        ->schema([
                                            Grid::make()->schema([
                                                Forms\Components\Radio::make('other_schedule')
                                                    ->label('附加時段')
                                                    ->options([
                                                        '0' => '無',
                                                        '1' => '回程',
                                                        '2' => '重複',
                                                        '3' => '自訂',
                                                    ])
                                                    ->default('0')
                                                    ->inline()
                                                    ->live()
                                                    ->columnSpanFull(),
                                                    ]),
                                            Grid::make()
                                                ->schema([
                                                    Forms\Components\DateTimePicker::make('return_date')
                                                        ->hiddenOn('edit')
                                                        ->visible(fn ($get) => $get('other_schedule') == 1)
                                                        ->label('回程日期/時間')
                                                        ->displayFormat('Y-m-d H:i')
                                                        ->seconds(false)
                                                        ->firstDayOfWeek(7)
                                                        ->native(false),
                                                    Forms\Components\TextInput::make('return_flight_no')
                                                        // ->default(0)
                                                        ->hiddenOn('edit')
                                                        ->visible(fn ($get) => $get('other_schedule') == 1)
                                                        ->label('回程航班編號'),
                                                ]),
                                            Grid::make()
                                                ->schema([
                                                    Repeater::make('customize_schedule')
                                                        ->label('')
                                                        ->hiddenOn('edit')
                                                        ->visible(fn ($get) => $get('other_schedule') == 2)
                                                        ->schema([
                                                            Grid::make()->schema([
                                                                Forms\Components\DateTimePicker::make('booking_datetime')
                                                                    ->label('預約日期時間'),
                                                                TextInput::make('note')
                                                                    ->label('備註'),
                                                                ])->columns(3),
                                                        ])
                                                        ->columnSpanFull()
                                                    ]),
                                        ]),
                                    Tabs\Tab::make('tab3')
                                        ->label("其他費用")
                                        ->schema([
                                            Repeater::make('other_fee')
                                                ->label('')
                                                ->schema([
                                                    Grid::make()->schema([
                                                        Forms\Components\Select::make('other_fee')
                                                            ->label('其他費用')
                                                            ->options([
                                                                '1' => '等待',
                                                                '2' => '夜乘',
                                                                '3' => '停車費',
                                                                '4' => '其他',
                                                            ])
                                                            ->live()
                                                            ->reactive(),
                                                        Forms\Components\TextInput::make('fee')
                                                            ->label('費用'),
                                                        Forms\Components\TextInput::make('note')
                                                            ->label('說明'),
                                                        ])->columns(3),
                                                    ])
                                        ]),
                                    Tabs\Tab::make('tab4')
                                        ->label("其他乘車人")
                                        ->schema([
                                            Repeater::make('other_passenger')
                                                ->label('')
                                                ->schema([
                                                    Grid::make()->schema([
                                                        TextInput::make('name')
                                                            ->label('姓名'),
                                                        TextInput::make('tel')
                                                            ->label('電話'),
                                                        TextInput::make('address')
                                                            ->label('地址'),
                                                        ])->columns(3),
                                                ])
                                        ]),
                                ])
                                ->activeTab(1),
                    // Section::make('其他乘車人')
                    //     ->schema([
                    //         Repeater::make('other_passenger')
                    //             ->label('')
                    //             ->schema([
                    //                 Grid::make()->schema([
                    //                     TextInput::make('name')
                    //                         ->label('姓名'),
                    //                     TextInput::make('tel')
                    //                         ->label('電話'),
                    //                     TextInput::make('address')
                    //                         ->label('地址'),
                    //                     ])->columns(3),
                    //             ])
                    //     ])->columnSpanFull(),
                    // Section::make('其他費用')
                    //     ->schema([
                    //         Repeater::make('other_fee')
                    //             ->label('')
                    //             ->schema([
                    //                 Grid::make()->schema([
                    //                     Forms\Components\Select::make('other_fee')
                    //                         ->label('其他費用')
                    //                         ->options([
                    //                             '1' => '等待',
                    //                             '2' => '夜乘',
                    //                             '3' => '停車費',
                    //                             '4' => '其他',
                    //                         ])
                    //                         ->live()
                    //                         ->reactive(),
                    //                     Forms\Components\TextInput::make('fee')
                    //                         ->label('費用'),
                    //                     Forms\Components\TextInput::make('note')
                    //                         ->label('說明'),
                    //                     ])->columns(3),
                    //             ])
                    //     ]),
                    // Section::make('其他時間')
                    //     ->hiddenOn('edit')
                    //     ->schema([
                    //         Grid::make()->schema([
                    //             Forms\Components\Radio::make('other_schedule')
                    //             ->label('附加時段')
                    //             ->options([
                    //                 '0' => '無',
                    //                 '1' => '回程',
                    //                 '2' => '重複',
                    //                 '3' => '自訂',
                    //             ])
                    //             ->default('0')
                    //             ->inline()
                    //             ->live()
                    //             ->columnSpanFull(),
                    //             ]),
                    //         Grid::make()
                    //             ->schema([
                    //                 Forms\Components\DateTimePicker::make('return_date')
                    //                     ->hiddenOn('edit')
                    //                     ->visible(fn ($get) => $get('other_schedule') == 1)
                    //                     ->label('回程日期/時間')
                    //                     ->displayFormat('Y-m-d H:i')
                    //                     ->seconds(false)
                    //                     ->firstDayOfWeek(7)
                    //                     ->native(false),
                    //                 Forms\Components\TextInput::make('return_flight_no')
                    //                     // ->default(0)
                    //                     ->hiddenOn('edit')
                    //                     ->visible(fn ($get) => $get('other_schedule') == 1)
                    //                     ->label('回程航班編號'),
                    //             ]),
                    //         Grid::make()
                    //             ->schema([
                    //                 Repeater::make('customize_schedule')
                    //                 ->label('')
                    //                 ->hiddenOn('edit')
                    //                 ->visible(fn ($get) => $get('other_schedule') == 2)
                    //                 ->schema([
                    //                     Grid::make()->schema([
                    //                         Forms\Components\DateTimePicker::make('booking_datetime')
                    //                             ->label('預約日期時間'),
                    //                         TextInput::make('note')
                    //                             ->label('備註'),
                    //                         ])->columns(3),
                    //                 ])
                    //                 ->columnSpanFull()
                    //             ]),
                    //     ])
                        ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('source')
                    ->label('來源')
                    ->searchable()
                    ->getStateUsing( function (Model $record){
                        if($record['source'] == 0){
                            return '後台';
                        }else if($record['source'] == 1){
                            return 'Excel';
                        }else if($record['source'] == 2){
                            return '司機';
                        }
                    }),
                Tables\Columns\TextColumn::make('location_type')
                    ->label('派車類別')
                    ->searchable()->getStateUsing( function (Model $record){
                        if($record['location_type'] == 0){
                            return '其他';
                        }else if($record['location_type'] == 1){
                            return '接機';
                        }else if($record['location_type'] == 2){
                            return '送機';
                        }else if($record['location_type'] == 3){
                            return '包車';
                        }else if($record['location_type'] == 4){
                            return '其他';
                        }
                    }),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('預約日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 101){
                            return '異常';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'warning',
                        '異常' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\SelectColumn::make('driver_id')
                    ->label('司機')
                    ->options(
                        Driver::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id')->toArray()
                    )
                    ->afterStateUpdated(function ($state, $record) {
                        // dd($state);
                        if ($state) {
                            $record->status = 1;
                            $record->save();
                            $driver = Driver::where('id', $record->driver_id)->first();
                            $car = Car::where('driver_id', $record->driver_id)->first();
                            $customer = Customer::where('id', $record->customer_id)->first();
                            if($record->department_id != null){
                                $department = Department::where('id', $record->department_id)->first();
                                $record->department_name = $department->name;
                            }
                            $record->customer_title = $customer->title;
                            $record->driver_name = $driver->name;
                            $record->car_license = $car->license;
                            // $rs = Dispatch::generateImage($record);
                            // 產生派車單
                            // $rs = Dispatch::generateImageV2('dispatch1',$record);
                            // $rs2 = Dispatch::generateImageV2('dispatch2',$record);
                            // if(!$rs){
                            //     return $this->sendError('簽單圖片生成失敗!請與車行洽詢!');
                            // }
                            // if(!$rs2){
                            //     return $this->sendError('出租單圖片生成失敗!請與車行洽詢!');
                            // }
                            // Dispatch::where('id', $record->id)
                            //     ->update([
                            //         // 'signature_file' => 'signatures/' . $fileName,
                            //         'image_path' => $rs,
                            //         'image2_path' => $rs2,
                            //     ]);
                        }else{
                            $record->status = 0;
                            $record->save();
                        }
                    })
                    // ->extraAttributes([
                    //     'style' => 'width: 120px;',  // 設定固定寬度為150px
                    // ])
                    ,
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    ->label('車型')
                    ->getStateUsing(function (Model $record) {
                        $rs = Cartype::where('id', $record['cartype_id'])->first();
                        if($rs != null){
                            return $rs->short_name;
                        }
                        return '';
                    }),
                Tables\Columns\TextColumn::make('pay_type')
                    ->searchable()
                    ->label('付款方式'),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->searchable()
                    ->label('費用'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLscars::route('/'),
            'create' => Pages\CreateLscar::route('/create'),
            'edit' => Pages\EditLscar::route('/{record}/edit'),
        ];
    }
}
