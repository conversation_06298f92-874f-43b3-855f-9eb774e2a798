<?php

namespace App\Filament\Resources\JinHuangResource\Pages;

use Filament\Actions;
use App\Models\Dispatch;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Cache;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\JinHuangResource;

class ListJinHuangs extends ListRecords
{
    protected static string $resource = JinHuangResource::class;

    protected static ?string $title = '派車單列表';
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增派車單'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
    public function getTabs(): array
    {
        return [
            '今日' => Tab::make()
                        ->badge(function () {
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->toDateString())
                                ->where('deleted_at', null)
                                ->count();
                        })
                        ->modifyQueryUsing(function ($query) {
                            session(['activeTab' => '今日']);
                            return $query->whereDate('start_date', '=', now()->toDateString())->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '明日' => Tab::make()
                        ->badge(function () {
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->addDay()->toDateString())
                                ->where('deleted_at', null)
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '明日']);
                            return $query->whereDate('start_date', '=', now()->addDay()->toDateString())->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '本周' => Tab::make()
                        ->badge( function () {
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->where('deleted_at', null)
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本周']);
                            return $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '本月' => Tab::make()
                        ->badge(function () {
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])
                                ->where('deleted_at', null)
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本月']);
                            return $query->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '未指派' => Tab::make()
                        ->badge(function () {
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('deleted_at', null)
                                ->where('status', 0)
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '未指派']);
                            return $query->where('status', 0)->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '已完成' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_completed_' . Filament::auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('status', 2)
                                ->where('deleted_at', null)
                                ->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '已完成']);
                            return $query->where('status', 2)->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '全部' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_all_' . Filament::auth()->user()->vendor_id, 600, function () {
                            // session(['activeTab' => '全部']); 1123
                            return Dispatch::where('vendor_id', Filament::auth()->user()->vendor_id)->where('deleted_at', null)->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '全部']);
                            return $query->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
        ];
    }
    protected function getFilteredCount(Builder $query): int
    {
        return (clone $query)->count();
    }
}
