<?php

namespace App\Filament\Resources\JinHuangResource\Pages;

use Carbon\Carbon;
use App\Models\Car;
use App\Models\User;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use Filament\Facades\Filament;
use App\Traits\CreateJHMessageText;
use Illuminate\Support\Facades\Log;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\JinHuangResource;

class CreateJinHuang extends CreateRecord
{
    use CreateJHMessageText;
    protected static string $resource = JinHuangResource::class;
    protected static ?string $title = '建立派車單';
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/zu-sans') => '列表',
        ];
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function handleRecordCreation(array $data): Dispatch
    {
        // dd($data);
        // $rs = $this->mutateFormDataBeforeCreate($data);
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Dispatch::create($data);
        if(isset($data['drivername2']) && $data['drivername2'] != null){
            $driverName = Driver::where('id', $data['drivername2'])->value('name');
            $data['drivername2'] = $driverName;
        }
        if(isset($data['driver_id']) && $data['driver_id'] != null){
            // dd($dispatch);
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->customer_title = $customer->title;

            if($dispatch->department_id){
                $department = Department::find($dispatch->department_id);
                $dispatch->department_name = $department?->name ?? '';
            } else {
                $dispatch->department_name = '';
            }
            $dispatch->car = $car;
            $dispatch->car_license = $car->license;
            // $dispatch->car_license = $car->license;
            if($data['location_type'] == 1){
                $tmpStartLocation = $this->getAirportName( $dispatch->start_location);
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $this->getAirportName( $dispatch->end_location);
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $otherTrip = [];
            if($dispatch->pickup_and_dropoff_location != null){
                $dispatch->tmpPickupLocationStr = $this->transPickupStr($dispatch->pickup_and_dropoff_location);
                $otherTrip = $dispatch->pickup_and_dropoff_location;
            }
            $msgDispatch = [
                'car_model' => Cartype::where('id', $dispatch->cartype_id)->first()->short_name, //$dispatch->cartype_id,
                'ride_date' => Carbon::parse($dispatch->start_date)->toDateString(),
                'ride_time' => Carbon::parse($dispatch->start_date)->toTimeString(),
                'booking_type' => static::getBookingTypeName($dispatch->location_type),
                'start_location' => $tmpStartLocation,
                'passenger_name' => $dispatch->passenger_name,
                'passenger_mobile' => $dispatch->passenger_mobile,
                'end_location' => $tmpEndLocation,
                'fare_amount' => ($dispatch->pay_type == 1) ? $dispatch->rental_cost : '--',
                'flight_no' => $dispatch->flight_no,
                'otherTrip' => $otherTrip,
                'location_type' => $dispatch->location_type
            ];
            $dispatch->copy_text_1 = static::generateMessage($msgDispatch);
            // 檢查外派司機
            if($dispatch->driver_zs_id != null){
                $mydriver = Driver::where('id', $dispatch->driver_zs_id)->first();
                $mycar = Car::where('driver_id', $mydriver->id)->first();
                $dispatch->driver_name = $mydriver->name;
                $dispatch->car_license = $mycar->license;
                $dispatch->driver = $mydriver;
                $dispatch->car = $mycar;
            }
            $rs = Dispatch::generateImageV3('dispatch1',$dispatch);
            $rs2 = Dispatch::generateImageV3('dispatch2', $dispatch);
            // $dispatch = Dispatch::find($dispatch->id);
            Log::info('建立 DISPATCH >> '.json_encode($dispatch));
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            unset($dispatch->tmpPickupLocationStr);
            $dispatch->image_path = $rs;
            $dispatch->image2_path = $rs2;
            $dispatch->save();
            if($dispatch->return == 1){
                $returnData = [
                    'id' => $dispatch->id,
                    'start_date' => $data['return_date'],
                    'end_date' => \Carbon\Carbon::parse($data['return_date'])->addHour(),
                    'customer_id' => $data['customer_id'],
                ];
            }
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            $user = User::where('id', Filament::auth()->user()->id)->first();
            $vendor = Vendor::where('id', $user->vendor_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id){
                $department = Department::find($dispatch->department_id);
                $dispatch->department_name = $department?->name ?? '';
            } else {
                $dispatch->department_name = '';
            }
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $tmpStartLocation = $this->getAirportName( $dispatch->start_location);
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
                $return_router = $dispatch->end_location . ' >> ' . $this->getAirportName( $dispatch->start_location);
            }else if($data['location_type'] == 2){
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $this->getAirportName( $dispatch->end_location);
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
                $return_router = $this->getAirportName( $dispatch->end_location) . ' >> ' . $dispatch->start_location;
            }else{
                $tmpStartLocation = $dispatch->start_location;
                $tmpEndLocation = $dispatch->end_location;
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
                $return_router = $dispatch->end_location  . ' >> ' .  $dispatch->start_location;
            }
            $otherTrip = [];
            if($dispatch->pickup_and_dropoff_location != null){
                $otherTrip = $dispatch->pickup_and_dropoff_location;
                $dispatch->tmpPickupLocationStr = $this->transPickupStr($dispatch->pickup_and_dropoff_location);
                // dd($tmpStr);
            }
            $msgDispatch = [
                'car_model' => Cartype::where('id', $dispatch->cartype_id)->first()->short_name, //$dispatch->cartype_id,
                'ride_date' => Carbon::parse($dispatch->start_date)->toDateString(),
                'ride_time' => Carbon::parse($dispatch->start_date)->format('H:i'),
                'booking_type' => static::getBookingTypeName($dispatch->location_type),
                'start_location' => $tmpStartLocation,
                'passenger_name' => $dispatch->passenger_name,
                'passenger_mobile' => $dispatch->passenger_mobile,
                'end_location' => $tmpEndLocation,
                'fare_amount' => ($dispatch->pay_type == 1) ? $dispatch->rental_cost : '--',
                'flight_no' => $dispatch->flight_no,
                'otherTrip' => $otherTrip,
                'location_type' => $dispatch->location_type
            ];
            Log::info("message", $msgDispatch);
            $dispatch->copy_text_1 = static::generateMessage($msgDispatch);
            $rs = Dispatch::generateImageV3('dispatch1',$dispatch);
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->tmpPickupLocationStr);
            $dispatch->image_path = $rs;
            $dispatch->save();
            if($data['return'] == 1){
                $return_location_type = $this->getReturnLocationType($data['location_type']);
                $return_rental_cost = $data['rental_cost'];
                // if($data['location_type']==1)   {
                //     $return_rental_cost = $data['rental_cost']-200;
                // }else if($data['location_type']==2){
                //     $return_rental_cost = $data['rental_cost']+200;
                // }
                $returnData = [
                    // 'id' => $dispatch->id,
                    'start_date' => $data['return_date'],
                    'end_date' => \Carbon\Carbon::parse($data['return_date'])->addHour(),
                    'customer_id' => $data['customer_id'],
                    "vendor_id" => Filament::auth()->user()->vendor_id,
                    "department_id" => empty($data['department_id']) ? null : $data['department_id'],
                    "passenger_name" => empty($data['passenger_name']) ? null : $data['passenger_name'],
                    "passenger_mobile" => empty($data['passenger_mobile']) ? null : $data['passenger_mobile'],
                    "location_type" => $return_location_type,
                    "start_location" => empty($data['end_location']) ? null : $data['end_location'],
                    "end_location" => empty($data['start_location']) ? null : $data['start_location'],
                    "flight_no" => empty($data['return_flight_no']) ? null : $data['return_flight_no'],
                    "cartype_id" => $data['cartype_id'],
                    "pay_type" => $data['pay_type'],
                    "num_of_people" => empty($data['num_of_people']) ? null : $data['num_of_people'],
                    "num_of_bags" => empty($data['num_of_bags']) ? null : $data['num_of_bags'],
                    "child_seat" => empty($data['child_seat']) ? null : $data['child_seat'],
                    "bootster_pad" => empty($data['bootster_pad']) ? null : $data['bootster_pad'],
                    "route" => $return_router,
                    "status" => 0,
                    "rental_cost" => (int)$return_rental_cost,
                    "return" => 2,
                ];
                $returnRs = Dispatch::create($returnData);
                // dd($returnRs);
            }
        }
        // $data['dispatch_no'] = $rs->dispatch_no;
        return $dispatch;
    }
    public function transPickupStr($data)
    {
        $result = [];
        foreach ($data as $index => $person) {
            $result[] = ($index + 1) . ".姓名:" . $person["name"] . "，電話:" . $person["mobile"] . "\n地址:" . $person["location"]."\n";
        }
        $output = implode(" \n", $result);
        return $output;
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd($data);
        // 自定義邏輯
        return $data;
    }
    public function getAirportName($data)
    {
        if($data==1){
            return '松山機場 TSA';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '桃園機場';
        }else if($data==5){
            return '台中機場';
        }
        return '資料錯誤!';
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function getReturnLocationType($data)
    {
        if($data==1){
            return 2;
        }else if($data==2){
            return 1;
        }else if($data==3){
            return 3;
        }
    }
    public function getTrip($data)
    {
        // if($data==1){
        //     return '桃園機場 T1';
        // }else if($data==2){
        //     return '桃園機場 T2';
        // }else if($data==3){
        //     return '桃園機場';
        // }else if($data==4){
        //     return '台中機場';
        // }
        return '台北車站';
    }
}
