<?php

namespace App\Filament\Resources\LscarResource\Pages;

use App\Models\Car;
use App\Models\Lscar;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Department;
use App\Traits\CreateImage;
use App\Traits\CreateDispatch;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\LscarResource;

class EditLscar extends EditRecord
{
    use CreateImage; use CreateDispatch;
    protected static string $resource = LscarResource::class;
    protected static ?string $title = '編輯派車單';

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->modalHeading('刪除此筆訂車單嗎？'),
        ];
    }
    public function handleRecordUpdate(Model $record, array $data):Lscar
    {
        // dd($data);
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Lscar::find($record->id);
        $dispatch->update($data);
        $dispatch->save();
        if(isset($dispatch->driver_id) && $dispatch->driver_id != null){
            // dd($dispatch);
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = $car;
            $dispatch->car_license = $car->license;
            if($data['location_type'] == 1){
                $dispatch->route = static::getLocationName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . static::getLocationName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            if($dispatch->signature_path != null){
                $signature = storage_path('app/public/' .$dispatch->signature_path);
            }else{
                $signature = null;
            }

            // $rs = Dispatch::generateImage($dispatch, $signature);
            $rs = static::generateImage('dispatch3', $dispatch);

            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            $dispatch->image2_path = $rs;
            $dispatch->save();
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = ["person_id" => null];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $dispatch->route = static::getLocationName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . static::getLocationName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $signature = null;

            $rs = static::generateImage('dispatch3', $dispatch);
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            $dispatch->save();
        }
        return $dispatch;
    }
}
