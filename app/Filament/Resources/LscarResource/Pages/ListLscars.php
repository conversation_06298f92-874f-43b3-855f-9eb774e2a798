<?php

namespace App\Filament\Resources\LscarResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use App\Imports\LscarsImport;
use Maatwebsite\Excel\Facades\Excel;
use Filament\Notifications\Notification;
use App\Filament\Resources\LscarResource;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Pages\ListRecords;

class ListLscars extends ListRecords
{
    protected static string $resource = LscarResource::class;
    protected static ?string $title = '派車單列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增派車單'),
            Action::make('importDispatch')
                ->label('匯入派車單')
                ->form([
                    FileUpload::make('attachments'),
                ])
                ->action(function (array $data) {
                    // $data['vendor_id'] = auth()->user()->vendor_id;
                    // $data['status'] = 0;
                    // $data['start_date'] = now();
                    $file = public_path('storage/'.$data['attachments']);
                    $rs = Excel::import(new LscarsImport, $file);
                    Notification::make()
                        ->title('上傳完成')
                        ->success()
                        ->send();
                    // dd($rs);
                    return redirect('/admin/lscar');
                }),
        ];
    }
}
