<?php

namespace App\Filament\Resources\DriverAccountResource\Pages;

use App\Filament\Resources\DriverAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDriverAccount extends EditRecord
{
    protected static string $resource = DriverAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
