<?php

namespace App\Filament\Resources\DriverAccountResource\Pages;

use App\Filament\Resources\DispatchResource\Widgets\DispatchStatsWidget;
use App\Filament\Resources\DriverAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Widgets\StatsOverviewWidget;

class ListDriverAccounts extends ListRecords
{
    protected static string $resource = DriverAccountResource::class;
    protected static ?string $title = '司機帳務列表';
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    // public function getBreadcrumbs(): array
    // {
    //     return
    //     [
    //         url('/admin/driver-accounts') => '司機帳務列表',
    //     ];
    // }
    // public function getFooterWidgets(): array
    // {
    //     return [
    //         DispatchStatsWidget::class,
    //     ];
    // }
}
