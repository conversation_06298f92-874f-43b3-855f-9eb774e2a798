<?php

namespace App\Filament\Resources\ZuSanResource\Pages;

use Filament\Actions;
use App\Models\Dispatch;
use Filament\Facades\Filament;
use App\Traits\ClearsZuSanCache;
use Illuminate\Support\Facades\Cache;
use Filament\Resources\Components\Tab;
use App\Filament\Resources\ZuSanResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListZuSans extends ListRecords
{
    use ClearsZuSanCache;
    protected static string $resource = ZuSanResource::class;
    protected static ?string $title = '派車單列表';
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增派車單'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
    public function getTabs(): array
    {
        return [
            '今日' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->toDateString())
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function ($query) {
                            session(['activeTab' => '今日']);
                            return $query->whereDate('start_date', '=', now()->toDateString())->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '明日' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->addDay()->toDateString())
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '明日']);
                            return $query->whereDate('start_date', '=', now()->addDay()->toDateString())->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '本周' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本周']);
                            return $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '本月' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本月']);
                            return $query->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '未指派' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('deleted_at', null)
                                ->where('status', 0)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '未指派']);
                            return $query->where('status', 0)->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '已完成' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('status', 2)
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '已完成']);
                            return $query->where('status', 2)->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
            '全部' => Tab::make()
                        ->badge($this->getFilteredCount(
                            Dispatch::query()->where('vendor_id', Filament::auth()->user()->vendor_id)
                                ->where('deleted_at', null)
                        ))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '全部']);
                            return $query->where('deleted_at', null)->orderBy('start_date', 'asc');
                        }),
        ];
    }
    protected function getFilteredCount(Builder $query): int
    {
        // 檢查是否啟用快取
        if (!config('zusan.cache.enabled', true)) {
            return (clone $query)->count();
        }

        $vendorId = Filament::auth()->user()->vendor_id;
        $tagPrefix = config('zusan.cache.tag_prefix', 'zusan');
        $cacheTtl = config('zusan.cache.tab_count_ttl', 300);

        // 生成快取鍵，基於查詢條件和用戶 vendor_id
        $cacheKey = $this->generateCacheKey($query, $vendorId);

        // 如果使用 Redis，使用 cache tags 來管理快取
        if (config('cache.default') === 'redis') {
            return Cache::tags(["{$tagPrefix}_vendor_{$vendorId}", "{$tagPrefix}_counts"])
                ->remember($cacheKey, $cacheTtl, fn() => (clone $query)->count());
        }

        // 一般快取
        return Cache::remember($cacheKey, $cacheTtl, fn() => (clone $query)->count());
    }

    /**
     * 生成快取鍵
     */
    private function generateCacheKey(Builder $query, ?int $vendorId = null): string
    {
        $vendorId = $vendorId ?? Filament::auth()->user()->vendor_id;
        $tagPrefix = config('zusan.cache.tag_prefix', 'zusan');
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        // 結合 SQL 和參數生成唯一鍵
        $queryHash = md5($sql . serialize($bindings));

        return "{$tagPrefix}_count_{$vendorId}_{$queryHash}";
    }

    /**
     * 手動清除快取的方法（可以在需要時調用）
     */
    public function clearTabCountCache(?int $vendorId = null): void
    {
        $vendorId = $vendorId ?? Filament::auth()->user()->vendor_id;
        static::clearZuSanTabCache($vendorId);
    }
}
