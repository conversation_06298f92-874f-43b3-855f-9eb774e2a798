<?php

namespace App\Filament\Resources\ZuSanResource\Pages;

use Carbon\Carbon;
use App\Models\Car;
use App\Models\User;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use Filament\Facades\Filament;
use App\Traits\CreateMessageText;
use Illuminate\Support\Facades\Log;
use App\Filament\Resources\ZuSanResource;
use Filament\Resources\Pages\CreateRecord;

class CreateZuSan extends CreateRecord
{
    use CreateMessageText;
    protected static string $resource = ZuSanResource::class;
    protected static ?string $title = '建立派車單';
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/zu-sans') => '列表',
        ];
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    // /home/<USER>/car-backend-admin/app/Filament/Resources/ZuSanResource/Pages/CreateZuSan.php
    protected function handleRecordCreation(array $data): Dispatch
    {
        Log::info('[CreateZuSan - handleRecordCreation] Received data for main dispatch creation:', $data);

        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        /** @var Dispatch $dispatch */
        $dispatch = Dispatch::create($data); // 主派車單創建
        Log::info('[CreateZuSan - MainDispatch] Main dispatch created with ID: ' . $dispatch->id);

        // 主派車單的額外處理 (drivername2 - 雖然這裡的賦值不會影響 $dispatch，但保留原始邏輯)
        if(isset($data['drivername2']) && $data['drivername2'] != null){
            // This part seems to intend to use drivername2, but doesn't assign it to the $dispatch model directly.
            // If $data['drivername2'] (which is an ID) should be used for something on $dispatch,
            // it needs to be explicitly handled, e.g. $dispatch->some_field = $driverNameValue;
            // For now, just logging.
            Log::info('[CreateZuSan - MainDispatch] drivername2 was present in form data.', ['drivername2_id' => $data['drivername2']]);
        }

        // --- 主派車單 route 和 copy_text_1 計算 ---
        $mainDispatchRouteStartDisplay = $dispatch->start_location; // May be code or text
        $mainDispatchRouteEndDisplay = $dispatch->end_location;     // May be code or text
        $mainTmpStartLocationForMsg = $dispatch->start_location; // For message, might be airport name
        $mainTmpEndLocationForMsg = $dispatch->end_location;   // For message, might be airport name


        if ($dispatch->location_type == '1') { // 主單是「接機」 (start is airport)
            if (is_numeric($dispatch->start_location)) {
                $mainDispatchRouteStartDisplay = $this->getAirportName($dispatch->start_location);
                $mainTmpStartLocationForMsg = $mainDispatchRouteStartDisplay; // Use name for message
            }
            // $dispatch->end_location should be text
        } elseif ($dispatch->location_type == '2') { // 主單是「送機」 (end is airport)
            // $dispatch->start_location should be text
            if (is_numeric($dispatch->end_location)) {
                $mainDispatchRouteEndDisplay = $this->getAirportName($dispatch->end_location);
                $mainTmpEndLocationForMsg = $mainDispatchRouteEndDisplay; // Use name for message
            }
        }
        // For other types, assume both start and end are already text for route and message

        if (!empty($mainDispatchRouteStartDisplay) && $mainDispatchRouteStartDisplay !== '資料錯誤!' &&
            !empty($mainDispatchRouteEndDisplay) && $mainDispatchRouteEndDisplay !== '資料錯誤!') {
            $dispatch->route = $mainDispatchRouteStartDisplay . ' >> ' . $mainDispatchRouteEndDisplay;
        } else {
            $dispatch->route = null; // Or a placeholder like '路線資訊不完整'
            Log::warning('[CreateZuSan - MainDispatch] Route for main dispatch ID ' . $dispatch->id . ' is incomplete or has errors.', [
                'start_raw' => $dispatch->start_location, 'end_raw' => $dispatch->end_location,
                'start_display' => $mainDispatchRouteStartDisplay, 'end_display' => $mainDispatchRouteEndDisplay
            ]);
        }
        Log::info('[CreateZuSan - MainDispatch] Calculated route for main dispatch ID ' . $dispatch->id . ':', ['route' => $dispatch->route]);

        // 主派車單的 msgDispatch 和 copy_text_1
        $mainOtherTripData = $dispatch->pickup_and_dropoff_location ?? [];
        $msgDispatchMain = [
            'car_model' => Cartype::where('id', $dispatch->cartype_id)->first()->short_name ?? '',
            'ride_date' => Carbon::parse($dispatch->start_date)->toDateString(),
            'ride_time' => Carbon::parse($dispatch->start_date)->toTimeString(), // Ensure correct time format
            'booking_type' => static::getBookingTypeName($dispatch->location_type),
            'start_location' => $mainTmpStartLocationForMsg, // Use potentially converted name
            'passenger_name' => $dispatch->passenger_name,
            'passenger_mobile' => $dispatch->passenger_mobile,
            'end_location' => $mainTmpEndLocationForMsg,     // Use potentially converted name
            'fare_amount' => ($dispatch->pay_type == 1) ? $dispatch->rental_cost : '--',
            'flight_no' => $dispatch->flight_no,
            'otherTrip' => $mainOtherTripData,
            'location_type' => $dispatch->location_type,
            'vendor_id' => $dispatch->vendor_id,
            'return' => 0 // Main trip is not a return
        ];
        $dispatch->copy_text_1 = static::generateMessage($msgDispatchMain);
        Log::info('[CreateZuSan - MainDispatch] Generated copy_text_1 for main dispatch ID ' . $dispatch->id);

        // 主派車單圖片生成 (如果需要且有司機)
        if(isset($dispatch->driver_id) && $dispatch->driver_id != null){
            Log::info('[CreateZuSan - MainDispatch] Main dispatch ID ' . $dispatch->id . ' has driver, preparing for image generation.');
            $driver = Driver::find($dispatch->driver_id);
            if ($driver) {
                $car = Car::where('driver_id', $dispatch->driver_id)->first();
                $customer = Customer::find($dispatch->customer_id);
                $department = $dispatch->department_id ? Department::find($dispatch->department_id) : null;

                // 臨時屬性 for image generation
                $dispatch->driver_name = $driver->name ?? '';
                $dispatch->car_license = $car->license ?? '';
                $dispatch->driver = $driver;
                $dispatch->car = $car;
                $dispatch->customer_title = $customer->title ?? '';
                $dispatch->department_name = $department?->name ?? '';
                if (!empty($dispatch->pickup_and_dropoff_location)) {
                    $dispatch->tmpPickupLocationStr = $this->transPickupStr($dispatch->pickup_and_dropoff_location);
                }

                if (method_exists(Dispatch::class, 'generateImageV3')) {
                    try {
                        $rs = Dispatch::generateImageV3('dispatch1', $dispatch);
                        $rs2 = Dispatch::generateImageV3('dispatch2', $dispatch);
                        if ($rs && $rs !== 'error') $dispatch->image_path = $rs; else $dispatch->image_path = null;
                        if ($rs2 && $rs2 !== 'error') $dispatch->image2_path = $rs2; else $dispatch->image2_path = null;
                        Log::info('[CreateZuSan - MainDispatch] Image paths for main dispatch ID ' . $dispatch->id . ':', ['img1' => $dispatch->image_path, 'img2' => $dispatch->image2_path]);
                    } catch (\Exception $e) {
                        Log::error('[CreateZuSan - MainDispatch] Exception during image generation for main dispatch ID ' . $dispatch->id . ': ' . $e->getMessage());
                        $dispatch->image_path = null;
                        $dispatch->image2_path = null;
                    }
                } else {
                    Log::error('[CreateZuSan - MainDispatch] generateImageV3 method not found in Dispatch model.');
                }
                // Unset 臨時屬性
                unset($dispatch->driver_name, $dispatch->car_license, $dispatch->driver, $dispatch->car, $dispatch->customer_title, $dispatch->department_name, $dispatch->tmpPickupLocationStr);
            } else {
                Log::warning('[CreateZuSan - MainDispatch] Driver not found for main dispatch ID ' . $dispatch->id, ['driver_id' => $dispatch->driver_id]);
                $dispatch->image_path = null;
                $dispatch->image2_path = null;
            }
        } else {
            Log::info('[CreateZuSan - MainDispatch] No driver for main dispatch ID ' . $dispatch->id . '. Setting images to null.');
            $dispatch->image_path = null;
            $dispatch->image2_path = null;
        }
        $dispatch->save(); // 儲存主派車單的所有更新 (route, copy_text_1, image_paths)
        Log::info('[CreateZuSan - MainDispatch] Main dispatch ID ' . $dispatch->id . ' saved after all processing.');


        // 檢查是否需要建立回程單 (不論是否有司機)
        if(isset($data['return']) && $data['return'] == 1){
            Log::info('[CreateZuSan - ReturnTrip] Initiating return trip creation for main dispatch ID: ' . $dispatch->id);
            // Log original dispatch state AFTER it has been fully processed and saved, for accurate reference
            Log::info('[CreateZuSan - ReturnTrip] Original (main) dispatch data for return reference:', Dispatch::find($dispatch->id)->toArray());
            Log::info('[CreateZuSan - ReturnTrip] Form data for return reference:', $data);

            $return_location_type = $this->getReturnLocationType($data['location_type']); // Original type from $data
            $return_rental_cost = $data['return_rental_cost'] ?? null;
            $return_date = $data['return_date'] ?? null;
            $return_flight_no = $data['return_flight_no'] ?? null;
            $return_driver_fee = $data['return_driver_fee'] ?? null;

            if (!$return_date) { // Removed check for $return_rental_cost === null
                Log::error('[CreateZuSan - ReturnTrip] Skipped return trip creation for main dispatch ID ' . $dispatch->id . ': Missing return_date. (Note: return_rental_cost is optional).');
            } else {
                $originalPassengerName = $dispatch->passenger_name;
                $originalPassengerMobile = $dispatch->passenger_mobile;

                // Get raw start/end locations from the ALREADY SAVED main dispatch
                $rawOriginalStartLocation = $dispatch->start_location;
                $rawOriginalEndLocation = $dispatch->end_location;
                Log::info('[CreateZuSan - ReturnTrip] Raw original locations from main dispatch ID ' . $dispatch->id . ':', ['start' => $rawOriginalStartLocation, 'end' => $rawOriginalEndLocation, 'original_location_type' => $dispatch->location_type]);

                $returnPassengerName = $originalPassengerName;
                $returnPassengerMobile = $originalPassengerMobile;

                // Default swap for return trip: original end becomes return start, original start becomes return end.
                // These are RAW values (can be codes or text).
                $returnStartLocation = $rawOriginalEndLocation;
                $returnEndLocation = $rawOriginalStartLocation;
                Log::info('[CreateZuSan - ReturnTrip] Default swapped RAW locations for return:', ['start' => $returnStartLocation, 'end' => $returnEndLocation]);

                $originalPickupLocations = $dispatch->pickup_and_dropoff_location ?? [];
                $returnPickupLocations = []; // Initialize for the return trip

                if (!empty($originalPickupLocations) && is_array($originalPickupLocations)) {
                    Log::info('[CreateZuSan - ReturnTrip] Handling pickup_and_dropoff_location for return trip based on original trip type: ' . $dispatch->location_type);
                    $tempOriginalPickupLocations = $originalPickupLocations; // Copy for manipulation
                    $lastLocationData = array_pop($tempOriginalPickupLocations);

                    $returnPassengerName = $lastLocationData['name'];
                    $returnPassengerMobile = $lastLocationData['mobile'];
                    $returnPickupLocations = $tempOriginalPickupLocations; // Remaining stops

                    $originalPassengerLocationForReturnStop = '';

                    if ($dispatch->location_type == '1') { // Original was PICKUP (接機 Airport -> Text), return is DROPOFF (Text -> Airport)
                        $returnStartLocation = $lastLocationData['location'];      // Return starts from the last multi-stop location (text)
                        $returnEndLocation = $rawOriginalStartLocation;         // Return ends at the original airport (CODE)
                        $originalPassengerLocationForReturnStop = $rawOriginalEndLocation; // Original main passenger's location (original end, text)
                        Log::info('[CreateZuSan - ReturnTrip] Original (接機). Return (送機) multi-stop locations:', ['start' => $returnStartLocation, 'end_code' => $returnEndLocation, 'orig_psg_stop' => $originalPassengerLocationForReturnStop]);
                    } else if ($dispatch->location_type == '2') { // Original was DROPOFF (送機 Text -> Airport), return is PICKUP (Airport -> Text)
                        $returnStartLocation = $rawOriginalEndLocation;         // Return starts from the original airport (CODE)
                        $returnEndLocation = $lastLocationData['location'];      // Return ends at the last multi-stop location (text)
                        $originalPassengerLocationForReturnStop = $rawOriginalStartLocation; // Original main passenger's location (original start, text)
                        Log::info('[CreateZuSan - ReturnTrip] Original (送機). Return (接機) multi-stop locations:', ['start_code' => $returnStartLocation, 'end' => $returnEndLocation, 'orig_psg_stop' => $originalPassengerLocationForReturnStop]);
                    } else { // Other types (e.g., 單程 Text -> Text), return is (Text -> Text)
                        $returnStartLocation = $lastLocationData['location'];
                        $returnEndLocation = $rawOriginalStartLocation; // Or $rawOriginalStartLocation if that's the text
                        $originalPassengerLocationForReturnStop = $rawOriginalEndLocation; // Or $rawOriginalEndLocation
                        Log::info('[CreateZuSan - ReturnTrip] Original (OTHER). Return multi-stop locations:', ['start' => $returnStartLocation, 'end' => $returnEndLocation, 'orig_psg_stop' => $originalPassengerLocationForReturnStop]);
                    }
                    $returnPickupLocations[] = [
                        'name' => $originalPassengerName,
                        'mobile' => $originalPassengerMobile,
                        'location' => $originalPassengerLocationForReturnStop
                    ];
                } else {
                    Log::info('[CreateZuSan - ReturnTrip] No multi-stop for original trip. Using default swapped RAW locations for return.');
                }

                $returnData = [
                    'start_date' => $return_date,
                    'end_date' => Carbon::parse($return_date)->addHour(),
                    'customer_id' => $dispatch->customer_id,
                    "driver_id" => $dispatch->driver_id,
                    "vendor_id" => $dispatch->vendor_id,
                    "department_id" => $dispatch->department_id,
                    "passenger_name" => $returnPassengerName,
                    "passenger_mobile" => $returnPassengerMobile,
                    "location_type" => $return_location_type, // This is the type FOR THE RETURN TRIP
                    "start_location" => $returnStartLocation, // This is the RAW start for return (code or text)
                    "end_location" => $returnEndLocation,     // This is the RAW end for return (code or text)
                    "flight_no" => $return_flight_no,
                    "cartype_id" => $dispatch->cartype_id,
                    "pay_type" => $dispatch->pay_type,
                    "num_of_people" => $dispatch->num_of_people,
                    "num_of_bags" => $dispatch->num_of_bags,
                    "child_seat" => $dispatch->child_seat,
                    "booster_pad" => $dispatch->booster_pad,
                    "status" => $dispatch->driver_id ? 1 : 0,
                    "rental_cost" => (int)$return_rental_cost,
                    "return" => 1,
                    "pickup_and_dropoff_location" => $returnPickupLocations,
                    "note" => $dispatch->note,
                    "driver_note" => $dispatch->driver_note,
                    "row_color" => $dispatch->row_color,
                    "driver_fee" => $return_driver_fee,
                    // route will be calculated after creation
                ];
                Log::info('[CreateZuSan - ReturnTrip] Data for creating return dispatch (before create):', $returnData);

                /** @var Dispatch $returnRs */
                $returnRs = Dispatch::create($returnData);
                Log::info('[CreateZuSan - ReturnTrip] Return dispatch created with ID: ' . ($returnRs->id ?? 'null'));

                if ($returnRs) {
                    // --- 為回程單計算 route ---
                    $returnRouteStartDisplay = $returnRs->start_location; // Raw value from $returnRs
                    $returnRouteEndDisplay = $returnRs->end_location;     // Raw value from $returnRs

                    // Now, use $returnRs->location_type to determine which part is airport for display
                    if ($returnRs->location_type == '1') { // 回程是「接機」 (returnRs->start_location is airport CODE)
                        if (is_numeric($returnRs->start_location)) {
                            $returnRouteStartDisplay = $this->getAirportName($returnRs->start_location);
                        }
                        // $returnRs->end_location should be text here
                    } elseif ($returnRs->location_type == '2') { // 回程是「送機」 (returnRs->end_location is airport CODE)
                        // $returnRs->start_location should be text here
                        if (is_numeric($returnRs->end_location)) {
                            $returnRouteEndDisplay = $this->getAirportName($returnRs->end_location);
                        }
                    }
                    // For other types, assume both start and end are already text

                    if (!empty($returnRouteStartDisplay) && $returnRouteStartDisplay !== '資料錯誤!' &&
                        !empty($returnRouteEndDisplay) && $returnRouteEndDisplay !== '資料錯誤!') {
                        $returnRs->route = $returnRouteStartDisplay . ' >> ' . $returnRouteEndDisplay;
                    } else {
                        $returnRs->route = null;
                        Log::warning('[CreateZuSan - ReturnTrip] Route for return dispatch ID ' . $returnRs->id . ' is incomplete or has errors.', [
                            'start_raw' => $returnRs->start_location, 'end_raw' => $returnRs->end_location,
                            'start_display_for_route' => $returnRouteStartDisplay, 'end_display_for_route' => $returnRouteEndDisplay,
                            'return_location_type' => $returnRs->location_type
                        ]);
                    }
                    Log::info('[CreateZuSan - ReturnTrip] Calculated route for return dispatch ID ' . $returnRs->id . ':', ['route' => $returnRs->route]);

                    // --- 為回程單準備訊息文字 copy_text_1 ---
                    // Use the $returnRouteStartDisplay and $returnRouteEndDisplay that were just prepared for the route
                    $msgDispatchForReturn = [
                        'car_model' => Cartype::where('id', $returnRs->cartype_id)->first()->short_name ?? '',
                        'ride_date' => Carbon::parse($returnRs->start_date)->toDateString(),
                        'ride_time' => Carbon::parse($returnRs->start_date)->format('H:i'),
                        'booking_type' => static::getBookingTypeName($returnRs->location_type),
                        'start_location' => $returnRouteStartDisplay, // Display name
                        'passenger_name' => $returnRs->passenger_name,
                        'passenger_mobile' => $returnRs->passenger_mobile,
                        'end_location' => $returnRouteEndDisplay,     // Display name
                        'fare_amount' => ($returnRs->pay_type == 1) ? $returnRs->rental_cost : '--',
                        'flight_no' => $returnRs->flight_no,
                        'otherTrip' => $returnRs->pickup_and_dropoff_location ?? [],
                        'location_type' => $returnRs->location_type,
                        'vendor_id' => $dispatch->vendor_id,
                        'return' => 1
                    ];
                    $returnRs->copy_text_1 = static::generateMessage($msgDispatchForReturn);
                    Log::info('[CreateZuSan - ReturnTrip] Generated copy_text_1 for return dispatch ID ' . $returnRs->id);

                    // --- 產生回程單圖片 (如果需要且有司機) ---
                    if ($returnRs->driver_id) {
                        Log::info('[CreateZuSan - ReturnTrip] Generating images for return dispatch ID ' . $returnRs->id . ' as driver is assigned.');
                        $returnDriver = Driver::find($returnRs->driver_id);
                        if ($returnDriver) {
                            $returnCar = Car::where('driver_id', $returnRs->driver_id)->first();
                            $returnCustomer = Customer::find($returnRs->customer_id);
                            $returnDepartment = $returnRs->department_id ? Department::find($returnRs->department_id) : null;

                            $returnRs->driver_name = $returnDriver->name ?? '';
                            $returnRs->car_license = $returnCar->license ?? '';
                            $returnRs->driver = $returnDriver;
                            $returnRs->car = $returnCar;
                            $returnRs->customer_title = $returnCustomer->title ?? '';
                            $returnRs->department_name = $returnDepartment?->name ?? '';
                            if (!empty($returnRs->pickup_and_dropoff_location)) {
                                $returnRs->tmpPickupLocationStr = $this->transPickupStr($returnRs->pickup_and_dropoff_location);
                            }

                            if (method_exists(Dispatch::class, 'generateImageV3')) {
                                try {
                                    $returnImg1 = Dispatch::generateImageV3('dispatch1', $returnRs);
                                    $returnImg2 = Dispatch::generateImageV3('dispatch2', $returnRs);
                                    if ($returnImg1 && $returnImg1 !== 'error') $returnRs->image_path = $returnImg1; else $returnRs->image_path = null;
                                    if ($returnImg2 && $returnImg2 !== 'error') $returnRs->image2_path = $returnImg2; else $returnRs->image2_path = null;
                                    Log::info('[CreateZuSan - ReturnTrip] Image paths for return dispatch ID ' . $returnRs->id . ':', ['img1' => $returnRs->image_path, 'img2' => $returnRs->image2_path]);
                                } catch (\Exception $e) {
                                    Log::error('[CreateZuSan - ReturnTrip] Exception during image generation for return dispatch ID ' . $returnRs->id . ': ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
                                    $returnRs->image_path = null;
                                    $returnRs->image2_path = null;
                                }
                            }  else {
                                Log::error('[CreateZuSan - ReturnTrip] generateImageV3 method not found in Dispatch model for return trip.');
                            }
                            unset($returnRs->driver_name, $returnRs->car_license, $returnRs->driver, $returnRs->car, $returnRs->customer_title, $returnRs->department_name, $returnRs->tmpPickupLocationStr);
                        } else {
                            Log::error('[CreateZuSan - ReturnTrip] Driver not found for return dispatch ID ' . $returnRs->id, ['driver_id' => $returnRs->driver_id]);
                            $returnRs->image_path = null;
                            $returnRs->image2_path = null;
                        }
                    } else {
                        Log::info('[CreateZuSan - ReturnTrip] No driver assigned for return dispatch ID ' . $returnRs->id . '. Setting images to null.');
                        $returnRs->image_path = null;
                        $returnRs->image2_path = null;
                    }
                    $returnRs->save(); // 儲存所有對回程單的修改 (route, copy_text_1, image_paths)
                    Log::info('[CreateZuSan - ReturnTrip] Return dispatch ID ' . $returnRs->id . ' saved with all updates.');
                } else {
                    Log::error('[CreateZuSan - ReturnTrip] Failed to create return dispatch record for main dispatch ID ' . $dispatch->id);
                }
            }
        }
        return $dispatch;
    }
    public function transPickupStr($data)
    {
        $result = [];
        foreach ($data as $index => $person) {
            $result[] = ($index + 1) . ".姓名:" . $person["name"] . "，電話:" . $person["mobile"] . "\n地址:" . $person["location"]."\n";
        }
        $output = implode(" \n", $result);
        return $output;
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd($data);
        // 自定義邏輯
        return $data;
    }
    public function getAirportName($data)
    {
        if($data==1){
            return '松山機場 TSA';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '桃園機場';
        }else if($data==5){
            return '台中機場';
        }
        return '資料錯誤!';
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function getReturnLocationType($data)
    {
        if($data==1){
            return 2;
        }else if($data==2){
            return 1;
        }else if($data==3){
            return 3;
        }else if($data==4){
            return 4;
        }else if($data==0){
            return 0;
        }
    }
    public function getTrip($data)
    {
        // if($data==1){
        //     return '桃園機場 T1';
        // }else if($data==2){
        //     return '桃園機場 T2';
        // }else if($data==3){
        //     return '桃園機場';
        // }else if($data==4){
        //     return '台中機場';
        // }
        return '台北車站';
    }
    public function generatePickupAndDropoffLocation($pickupAndDropoffLocation)
    {
        if($pickupAndDropoffLocation == null){
            return null;
        }
        if (is_string($pickupAndDropoffLocation)) {
            $pickupAndDropoffLocation = json_decode($pickupAndDropoffLocation, true);
        }
        // Check if it's an array and reverse it
        if (is_array($pickupAndDropoffLocation)) {
            return array_reverse($pickupAndDropoffLocation);
        }
        // If it's not an array or null, return the original value (or handle it as needed)
        return $pickupAndDropoffLocation;

    }
}
