<?php

namespace App\Filament\Resources\ZuSanReportResource\Pages;

use App\Filament\Resources\ZuSanReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListZuSanReports extends ListRecords
{
    protected static string $resource = ZuSanReportResource::class;
    protected static ?string $title = '客戶報表';

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
