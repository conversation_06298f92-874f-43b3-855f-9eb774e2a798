<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use App\Traits\CreateImage;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Exports\DispatchExporter;
use Filament\Notifications\Actions\Action;
use Filament\Tables\Columns\Summarizers\Sum;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DriverAccountResource\Pages;
use App\Filament\Resources\DriverAccountResource\RelationManagers;


class DriverAccountResource extends Resource
{
    use CreateImage;
    protected static ?string $model = Dispatch::class;
    protected static ?string $navigationLabel = '司機報表';
    protected static ?string $navigationGroup = '報表管理';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 7;
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id);
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id != 7 || Filament::auth()->user()->id != 9){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶資料')
                        ->schema([
                            Forms\Components\Hidden::make('dispatch_no')
                                ->default(fn () => Dispatch::generateDispatchNumber())
                                ->reactive(),
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(fn () => Filament::auth()->user()->vendor_id)
                                ->reactive(),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('passenger_name')
                                    ->label('乘客名稱')
                                    ->placeholder('請輸入乘客大名'),
                                Forms\Components\TextInput::make('passenger_mobile')
                                    ->label('乘客電話')
                                    ->placeholder('請輸入乘客電話'),
                            ])
                        ])->columns(2),
                    Section::make('乘車資料')
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_date')
                                ->placeholder('請選擇日期與時間')
                                ->label('派車日期/時間')
                                ->displayFormat('Y-m-d H:i')
                                ->seconds(false)
                                // ->minutesStep(10)
                                ->firstDayOfWeek(7)
                                ->native(false),
                            Forms\Components\DateTimePicker::make('end_date')
                                ->hidden(),
                            Forms\Components\Select::make('pay_type')
                                ->label('付款方式')
                                ->default(0)
                                ->options([
                                    '0' => '月結',
                                    '1' => '現金',
                                    '2' => 'LINE PAY',
                                    '3' => '匯款',
                                    '4' => '刷卡',
                                    '5' => '入房帳',
                                ])
                                ->live()
                                ->reactive(),
                            Forms\Components\Radio::make('location_type')
                                ->label('派車類別')
                                ->options([
                                    '1' => '接機',
                                    '2' => '送機',
                                    '0' => '其他',
                                ])
                                ->default('1')
                                ->inline()
                                ->live()
                                ->columnSpanFull(),
                            Forms\Components\Select::make('start_location')
                                ->label('上車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '1')
                                ->live()
                                ->reactive(),

                            Forms\Components\TextInput::make('start_location')
                                ->label('上車地點')
                                ->placeholder('請輸入上車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2')
                                ->reactive(),
                            Forms\Components\TextInput::make('end_location')
                                ->label('下車地點')
                                ->placeholder('請輸入下車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1')
                                ->reactive(),
                            Forms\Components\Select::make('end_location')
                                ->label('下車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '2')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('flight_no')
                                ->label('航班編號')
                                ->placeholder('請輸入航班編號'),
                            // Forms\Components\Select::make('cartype_id')
                            //     ->label('車輛類別')
                            //     ->default(0)
                            //     ->options([
                            //         '0' => 'Benz  小車5人座車',
                            //         '1' => 'Vito   大車9人座車',
                            //         '2' => 'GRANVIA (代號G) 大車 6人座車',
                            //     ])
                            //     ->live()
                            //     ->reactive(),

                        ])->columns(2)
                        ->visible(fn ($get) => Filament::auth()->user()->vendor_id == 1),
                    Section::make('派車資料')
                        ->schema([
                            Forms\Components\Select::make('driver_id')
                                ->searchable()
                                ->preload()
                                ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id'))
                                ->label('司機')
                                ->live()
                                ->disableOptionWhen(fn (string $value): bool => $value === 2)
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if($state){
                                        $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                    }else{
                                        $set('status', 0);
                                    }
                                }),
                            Forms\Components\Select::make('status')
                                ->label('派車狀態')
                                ->default(0)
                                // ->disabled()
                                ->options([
                                    '0' => '派遣中',
                                    '1' => '已指派司機',
                                    '2' => '已完成',
                                    '3' => '已取消',
                                    '101' => '錯誤'
                                ]),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('rental_cost')
                                    ->label('費用')
                                    ->placeholder('請輸入費用')
                                    ->numeric(),
                                Forms\Components\TextInput::make('driver_fee')
                                    ->label('司機費用')
                                    ->placeholder('請輸入司機費用')
                                    ->numeric(),
                                Forms\Components\TextInput::make('carno2')
                                    ->label('其他車號')
                                    ->placeholder('請輸入其他車牌號碼'),
                            ])->columns(3),
                            Grid::make()->schema([
                                Forms\Components\Toggle::make('return')
                                    ->hiddenOn('edit')
                                    ->default(0)
                                    ->live()
                                    ->reactive()
                                    ->label('回程'),
                                Forms\Components\DateTimePicker::make('return_date')
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程日期/時間')
                                    ->displayFormat('Y-m-d H:i')
                                    ->seconds(false)
                                    ->firstDayOfWeek(7)
                                    ->native(false),
                                Forms\Components\TextInput::make('return_flight_no')
                                    // ->default(0)
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程航班編號'),
                            ])->columns(3),
                        ])->columns(2)
                        ->visible(fn ($get) => Filament::auth()->user()->vendor_id == 1),
                ]),
                Group::make([
                    Section::make('其他資料')
                        ->schema([
                            Group::make([
                                Forms\Components\Textarea::make('note')
                                    ->rows(5)
                                    ->label('備註'),
                            ])->columns(2),

                        ])
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->searchable()
                    ->toggleable()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable()
                    ->toggleable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\TextColumn::make('driver.name')
                    ->label('司機'),
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->toggleable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    // ->toggleable()
                    ->label('車型')
                    ->getStateUsing(function (Model $record) {
                        $rs = Cartype::where('id', $record['cartype_id'])->first();
                        if($rs != null){
                            return $rs->short_name;
                        }
                        return '';
                    }),
                Tables\Columns\TextColumn::make('car.license')
                    ->searchable()
                    ->toggleable()
                    ->label('車號')
                    ->getStateUsing(function (Model $record) {
                        // if($record['carno2'] != null){
                        //     return $record['carno2'];
                        // }else{
                            $rs = Car::where('driver_id', $record['driver_id'])->first();
                            if($rs != null){
                                return $rs->license;
                            }
                        // }
                    }),
                Tables\Columns\TextColumn::make('carno2')
                    ->searchable()
                    ->toggleable()
                    ->label('其他車號'),
                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->searchable()
                    ->toggleable()
                    ->limit(30)
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if(Filament::auth()->user()->vendor_id==1){
                            if($record['start_location'] == 1 || $record['end_location'] == 1){
                                $tmpdata = 'TSA 松山機場';
                            }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                                $tmpdata = '桃園機場 T1';
                            }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                                $tmpdata = '桃園機場 T2';
                            }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                                $tmpdata = '台中機場';
                            }else{
                                $tmpdata = $record['start_location'];
                            }
                        }else{
                            if($record['start_location'] == 1 || $record['end_location'] == 1){
                                $tmpdata = 'TSA 松山機場';
                            }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                                $tmpdata = '桃園機場 T1';
                            }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                                $tmpdata = '桃園機場 T2';
                            }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                                $tmpdata = '桃園機場';
                            }else if($record['start_location'] == 5 || $record['end_location'] == 5){
                                $tmpdata = '台中機場';
                            }else{
                                $tmpdata = $record['start_location'];
                            }
                        }

                        if($record['location_type'] == 1){
                            return $tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('pay_type')
                    ->toggleable()
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return 'LINE PAY';
                            case 3:
                                return '匯款';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->toggleable()
                    ->label('月結/現金'),
                Tables\Columns\TextColumn::make('driver_fee')
                    ->toggleable()
                    ->searchable()
                    ->label('司機車資'),
                Tables\Columns\TextColumn::make('return_fee')
                    ->toggleable()
                    ->searchable()
                    ->label('回金'),
                Tables\Columns\TextColumn::make('cash')
                    ->toggleable()
                    ->label('司機收現金'),
                // Tables\Columns\TextColumn::make('return_boss')
                //     ->searchable()
                //     ->toggleable()
                //     ->label('車趟來源'),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('合計金額')
                    ->numeric()
                    ->toggleable()
                    ->summarize([
                        Sum::make()->label('車資總額'),
                        // Sum::make()
                        //     ->label('現金總計')
                        //     ->query(fn($query) => $query->where('pay_type', 1)),
                        // Sum::make()
                        //     ->label('月結總計')
                        //     ->query(fn($query) => $query->where('pay_type', 0)),
                        // Sum::make()
                        //     ->label('匯款總計')
                        //     ->query(fn($query) => $query->where('pay_type', '!=' , null)),

                    ]),
                Tables\Columns\TextColumn::make('driver_fee')
                    ->label('司機合計金額')
                    ->numeric()
                    ->toggleable()
                    ->summarize([
                        Sum::make()->label('司機車資總計'),
                        // Sum::make()
                        //     ->label('現金總計')
                        //     ->query(fn($query) => $query->where('pay_type', 1)),
                        // Sum::make()
                        //     ->label('月結總計')
                        //     ->query(fn($query) => $query->where('pay_type', 0)),
                        // Sum::make()
                        //     ->label('匯款總計')
                        //     ->query(fn($query) => $query->where('pay_type', '!=' , null)),

                    ]),
                // Tables\Columns\TextColumn::make('aa')
                //     ->label('司機車資+收現金')
                //     ->numeric()
                //     ->summarize(Sum::make()->label('司機車資+收現金')),
            ])
            ->defaultSort('start_date', 'asc')
            ->filters([
                SelectFilter::make('driver_id')
                    ->label('司機')
                    ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)
                        ->pluck('name', 'id')),
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->columns(2)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('start_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('start_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('makeReport')
                        ->label('建立報表')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            // dd($filters);
                            $fileName = static::generatePdf('driver',$records, $filters);
                            $downloadUrl = url('storage/driver_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                            Notification::make()
                                ->title('報表已建立')
                                ->body("報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->icon('heroicon-o-document')
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    Tables\Actions\ExportBulkAction::make()
                        ->exporter(DispatchExporter::class)
                        ->label('匯出')
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDriverAccounts::route('/'),
            'create' => Pages\CreateDriverAccount::route('/create'),
            'edit' => Pages\EditDriverAccount::route('/{record}/edit'),
        ];
    }
}
