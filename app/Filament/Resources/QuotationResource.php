<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Quotation;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Filament\Actions\Action;
use Illuminate\Http\Response;
use App\Traits\CreateDispatch;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
// use Filament\Actions\Modal\Actions\Action;
use App\Filament\Resources\QuotationResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\QuotationResource\RelationManagers;

class QuotationResource extends Resource
{
    use CreateDispatch;
    protected static ?string $model = Quotation::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = '詢價/預約管理';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('ID')
                   ->sortable(),
                TextColumn::make('status')->label('狀態')
                    ->badge()
                    ->getStateUsing(function ($record) {
                        if($record->status==0){
                            return trim("詢價");
                        }else{
                            return trim(string: "預約");
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        "詢價" => 'success',
                        "預約" => 'danger',
                    }),
                TextColumn::make('order_type')->label('類別')
                    ->getStateUsing( function (Model $record){
                        if($record['order_type'] == 0){
                            return '接機';
                        }else if($record['order_type'] == 1){
                            return '送機';
                        }else if($record['order_type'] == 2){
                            return '包車';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '接機' => 'success',
                        '送機' => 'primary',
                        '包車' => 'danger',
                    }),
                TextColumn::make( 'car_type')->label('車型')
                    ->getStateUsing( function (Model $record){
                            if($record['car_type'] == 5){
                                return '五人轎車';
                            }else if($record['car_type'] == 51){
                                return '五人休旅車';
                            }else if($record['car_type'] == 9){
                                return '九人座';
                            }else{
                                return '五人轎車';
                            }
                        }),
                TextColumn::make('location_from_name')->label('上車地點')
                    ->formatStateUsing(function ($record) {
                        if($record->order_type == 0){
                            $city = $record->location_from_name ?? '';
                            return trim("{$city}");
                        }else if($record->order_type == 1 || $record->order_type == 2){
                            $city = $record->location_city_name ?? '';
                            $district = $record->location_district_name ?? '';
                            return trim("{$city} {$district}");
                        }
                    }),
                TextColumn::make('location_district_name')->label('下車地點')
                    ->formatStateUsing(function ($record) {
                        if($record->order_type == 0){
                            $city = $record->location_city_name ?? '';
                            $district = $record->location_district_name ?? '';
                            return trim("{$city} {$district}");
                        }else if($record->order_type == 1){
                            $city = $record->location_from_name ?? '';
                            // $district = $record->location_district_name ?? '';
                            return trim("{$city}");
                        }
                    }),
                TextColumn::make('passenger_name')->label('聯絡人')
                    ->searchable(),
                TextColumn::make('passenger_mobile')->label('聯絡電話')
                    ->searchable(),
                TextColumn::make('appointment_date')->label('預約日期時間')
                    ->sortable(),
                TextColumn::make('num_of_people')->label('乘車人數'),
                TextColumn::make('num_of_bags')->label('行李數'),
                TextColumn::make('total')->label('金額')
                    ->searchable(),
            ])
            ->defaultSort('id', 'desc')
            ->striped()
            ->filters([
                // Tables\Filters\TrashedFilter::make(),
                Filter::make('status')
                    ->label('狀態')
                    ->default(0)
                    ->query(function ($query, $data) {
                        if ($data['status']==0 || $data['status']==1 || $data['status']==2) {
                            // dd($data['status']);
                            $query->where('status', $data['status']);
                        }
                    })
                    ->form([
                        Radio::make('status')
                            ->label('狀態')
                            ->inline()
                            ->default(0)
                            ->live()
                            ->options([
                                0 => '詢價',
                                1 => '已預約',
                                2 => '已建派車單',
                            ])
                        ]),
                Filter::make('order_type')
                    ->label('類別')
                    ->default(0)
                    ->query(function ($query, $data) {
                        if ($data['order_type']==0 || $data['order_type']==1 || $data['order_type']==2) {
                            // dd($data['status']);
                            $query->where('order_type', $data['order_type']);
                        }
                    })
                    ->form([
                        Radio::make('order_type')
                            ->label('類別')
                            ->inline()
                            ->default(0)
                            ->live()
                            ->options([
                                0 => '接機',
                                1 => '送機',
                                2 => '包車',
                            ])
                        ]),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                \Filament\Tables\Actions\Action::make('created_dispatch')
                    ->label('建立派車單')
                    ->visible(function (Quotation $record){
                        return $record->status==1;
                    })
                    ->form(function (Quotation $record){
                        return [
                            Grid::make(3) // 分 3 列
                                ->schema([
                                    Hidden::make('quotation_id')
                                        ->default($record->id),
                                    Select::make('driver_id')
                                        ->label('司機')
                                        ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id')),
                                    TextInput::make('passenger_name')
                                        ->default($record->passenger_name)
                                        ->label('聯絡人'),
                                    TextInput::make('passenger_mobile')
                                        ->default($record->passenger_mobile)
                                        ->label('聯絡電話'),
                                    TextInput::make('passenger_address')
                                        ->default($record->passenger_address)
                                        ->label('地址'),
                                    TextInput::make('total')
                                        ->default($record->total)
                                        ->label('金額'),
                                    TextInput::make('id')
                                        ->default($record->id)
                                        ->label('編號'),
                                ]),
                        ];
                    })
                    ->action(function (array $data, Quotation $record) {
                        $validator = Validator::make($data, [
                            'driver_id' => 'required',
                            'passenger_name' => 'required',
                            'passenger_mobile' => 'required',
                            'passenger_address' => 'required',
                            'total' => 'required',
                        ]);
                        if ($validator->fails()) {

                            Notification::make()
                                ->title('表單驗證錯誤')
                                ->body('請檢查所有必填欄位並重新提交。')
                                ->danger()
                                ->send();
                            return;
                        }
                        $validatedData = $validator->validated();
                        // dd($validatedData);
                        $validatedData['status'] = 2;
                        $record->update($validatedData);
                        $quotation_id = $record->id;
                        $driver_id = $validatedData['driver_id'];
                        // $driver = Driver::find($driver_id);
                        $rs = Dispatch::create([
                            'dispatch_id' => 'DP_' . date('YmdHis') . Str::random(3),
                            'driver_id' => $driver_id,
                            // 'driver_name' => $driver->name,
                            // 'driver_mobile' => $driver->mobile,
                            // 'driver_address' => $driver->address,
                            'customer_name' => $validatedData['passenger_name'],
                            'customer_mobile' => $validatedData['passenger_mobile'],
                            'customer_address' => $validatedData['passenger_address'],
                            // 'total' => $validatedData['total'],
                            'start_time' => $record->appointment_date,
                            'end_time' => $record->appointment_date,
                            'route' => $record->location_from_name.'-'.$validatedData['passenger_address'],
                            'rental_cost' => $record->order_type,
                            'flight_no' => $record->flightno,
                            'vendor_id' => Filament::auth()->user()->vendor_id,
                            'quotation_id' => $quotation_id
                        ]);
                        $rsimage = static::createDispatchImage($rs->id);
                        // dd($rsimage);
                        if($rsimage){
                            $dispatch = Dispatch::find($rs->id);
                            $dispatch->update(['image_path' => $rsimage]);
                        }
                        Notification::make()
                            ->title('派車單建立成功')
                            ->body('新的派車單已成功建立！')
                            ->success() // 設定通知類型為成功
                            ->send();
                        return;
                        // $action->success();
                    }),
                Tables\Actions\DeleteAction::make()
                    ->label('刪除')
                    ->modalHeading('確認刪除此筆資料!')
                    ->modalSubheading('刪除後無法恢復，請確認是否刪除!')
                    ->modalButton('刪除')
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuotations::route('/'),
            // 'create' => Pages\CreateQuotation::route('/create'),
            // 'edit' => Pages\EditQuotation::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // return parent::getEloquentQuery()
        //     ->withoutGlobalScopes([
        //         SoftDeletingScope::class,
        //     ]);
            if(Filament::auth()->user()->vendor_id==0){
                return parent::getEloquentQuery()
                    // ->where('status', 0)
                    ->where('deleted_at', null)
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);
            }else{
                return parent::getEloquentQuery()
                    // ->where('status', 0)
                    ->where('deleted_at', null)
                    ->where('vendor_id', Filament::auth()->user()->vendor_id)
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);;
            }
    }
}
