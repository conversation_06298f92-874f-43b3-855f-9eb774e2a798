<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use App\Actions\Tables\CopyIdAction;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Image;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Crypt;
use Filament\Forms\Components\Section;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification as FilamentNotification;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Exports\DispatchExporter;
use Filament\Notifications\Actions\Action;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextInputColumn;
use Illuminate\Database\Eloquent\Collection;
use App\Filament\Resources\DispatchResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DispatchResource\RelationManagers;
use AxonC\FilamentCopyablePlaceholder\Forms\Components\CopyablePlaceholder;
// use Filament\Tables\Columns\Sum;

class DispatchResource extends Resource
{
    protected static ?string $model = Dispatch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '派車單管理';
    protected static ?int $navigationSort = 1;

    public static $vendorId = 1;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id==2){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        $query = parent::getEloquentQuery()
            ->with([
                'driver',
                'car',
                'customer',
                'department',
            ]);
        if (Filament::auth()->user()->vendor_id == 0) {
            return $query;
        } else {
            return $query->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    // public static function mount(): void
    // {
    //     dd(request()->all());
    //     // 預設接收來自 URL 的資料並填充表單
    //     static::form->fill(request()->only('passager_name', 'passager_mobile', 'rental_cost'));
    // }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('return_query_string')
                    ->default(fn () => request()->getQueryString())
                    ->dehydrated(true),
                Group::make([
                    Section::make('客戶資料')
                        ->schema([
                            Forms\Components\Hidden::make('dispatch_no')
                                ->default(fn () => Dispatch::generateDispatchNumber())
                                ->reactive(),
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(fn () => Filament::auth()->user()->vendor_id)
                                ->reactive(),
                            Forms\Components\Select::make('customer_id')
                                ->relationship('customer', 'title', function ($query) {
                                    return $query->where('vendor_id', Filament::auth()->user()->vendor_id)
                                                ->orderBy('sequence', 'asc');
                                })
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Hidden::make('vendor_id')
                                            ->default(fn () => Filament::auth()->user()->vendor_id)
                                            ->reactive(),
                                        Forms\Components\TextInput::make('title')
                                            ->label('客戶名稱')
                                            ->required()
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('address')
                                            ->label('地址'),
                                        Forms\Components\TextInput::make('telephone')
                                            ->label('電話'),
                                        Forms\Components\TextInput::make('contact')
                                            ->label('聯絡人'),
                                        Forms\Components\TextInput::make('contact_mobile')
                                            ->label('聯絡人電話'),
                                    ])->columns(2),
                                ])
                                ->live()
                                ->searchable()
                                ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                                ->preload()
                                ->label('客戶名稱')
                                // ->default('1')
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $set('department_id', null);
                                })
                                ->reactive()
                                ->required(),
                            Forms\Components\Select::make('department_id')
                                ->relationship('department', 'name')
                                // ->default(0)
                                ->nullable()
                                ->options(
                                    function ($get) {
                                        $customerId = $get('customer_id');
                                        return Department::where('customer_id', $customerId)
                                                            ->where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id');
                                    }
                                )
                                ->label('部門名稱')
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Select::make('customer_id')
                                            ->relationship('customer', 'title')
                                            ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('title', 'id'))
                                            ->label('公司名稱')
                                            // ->hidden()
                                            // ->default(fn () => 2)
                                            ->required(),
                                        Forms\Components\TextInput::make('name')
                                            ->label('部門名稱')
                                            ->required(),
                                        // Forms\Components\TextInput::make('vendor_id')
                                        //     ->label('不用輸入')
                                        //     ->default(fn () => auth()->user()->vendor_id)
                                        //     ->hidden()
                                        //     // ->maxWidth('10px')
                                        //     ->required(),
                                    ])->columns(3),
                                ])
                                ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                                    $data['vendor_id'] = Filament::auth()->user()->vendor_id;
                                    return $data;
                                }))
                                ->live()
                                ->reactive(),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('passenger_name')
                                    ->label('乘客名稱')
                                    ->placeholder('請輸入乘客大名'),
                                Forms\Components\TextInput::make('passenger_mobile')
                                    ->label('乘客電話')
                                    ->placeholder('請輸入乘客電話'),
                            ])
                        ])->columns(2),
                    Section::make('乘車資料')
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_date')
                                ->placeholder('請選擇日期與時間')
                                ->label('派車日期/時間')
                                ->displayFormat('Y-m-d H:i')
                                ->seconds(false)
                                // ->minutesStep(10)
                                ->firstDayOfWeek(7)
                                ->native(false),
                            Forms\Components\DateTimePicker::make('end_date')
                                ->hidden(),
                            Forms\Components\Select::make('pay_type')
                                ->label('付款方式')
                                ->default(0)
                                ->options([
                                    '0' => '月結',
                                    '1' => '現金',
                                    '2' => 'LINE PAY',
                                    '3' => '匯款',
                                ])
                                ->live()
                                ->reactive(),
                            Forms\Components\Radio::make('location_type')
                                ->label('派車類別')
                                ->options([
                                    '1' => '接機',
                                    '2' => '送機',
                                    '0' => '其他',
                                ])
                                ->default('1')
                                ->inline()
                                ->live()
                                ->columnSpanFull(),
                            Forms\Components\Select::make('start_location')
                                ->label('上車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '1')
                                ->live()
                                ->reactive(),

                            Forms\Components\TextInput::make('start_location')
                                ->label('上車地點')
                                ->placeholder('請輸入上車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2')
                                ->reactive(),
                            Forms\Components\TextInput::make('end_location')
                                ->label('下車地點')
                                ->placeholder('請輸入下車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1')
                                ->reactive(),
                            Forms\Components\Select::make('end_location')
                                ->label('下車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '2')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('flight_no')
                                ->label('航班編號')
                                ->placeholder('請輸入航班編號'),
                            Forms\Components\Select::make('cartype_id')
                                ->label('車輛類別')
                                ->default(0)
                                ->options([
                                    '0' => '轎車 5人座車',
                                    '1' => 'Vito 大車9人座車',
                                    '2' => 'GRANVIA 大車 6人座車',
                                ])
                                ->live()
                                ->reactive(),
                        ])->columns(2),
                    Section::make('派車資料')
                        ->schema([
                            Forms\Components\Select::make('driver_id')
                                ->searchable()
                                ->preload()
                                ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id'))
                                ->label('司機')
                                ->live()
                                ->disableOptionWhen(fn (string $value): bool => $value === 2)
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if($state){
                                        $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                    }else{
                                        $set('status', 0);
                                    }
                                }),
                            Forms\Components\Select::make('status')
                                ->label('派車狀態')
                                ->default(0)
                                // ->disabled()
                                ->options([
                                    '0' => '派遣中',
                                    '1' => '已指派司機',
                                    '2' => '已完成',
                                    '3' => '已取消',
                                    '101' => '錯誤',
                                    '102' => '待審核'
                                ]),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('rental_cost')
                                    ->label('費用')
                                    ->placeholder('請輸入費用')
                                    ->numeric(),
                                Forms\Components\TextInput::make('driver_fee')
                                    ->label('司機費用')
                                    ->placeholder('請輸入司機費用')
                                    ->numeric(),
                                Forms\Components\TextInput::make('carno2')
                                    ->label('其他車號')
                                    ->placeholder('請輸入其他車牌號碼'),
                            ])->columns(3),

                            // CopyablePlaceholder::make('Label')
                            //     ->label('簽名檔連結')
                            //     ->content(fn ($get) => 'http://carv2.aerocars.cc/sign/') // . Crypt::encryptString($get('dispatch_no')))
                            //     ->visible(fn ($get) => $get('driver_id') == '11')
                            //     ->iconOnly(),
                            Grid::make()->schema([
                                Forms\Components\Toggle::make('return')
                                    ->hiddenOn('edit')
                                    ->default(0)
                                    ->live()
                                    ->reactive()
                                    ->label('回程'),
                                Forms\Components\DateTimePicker::make('return_date')
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程日期/時間')
                                    ->displayFormat('Y-m-d H:i')
                                    ->seconds(false)
                                    ->firstDayOfWeek(7)
                                    ->native(false),
                                Forms\Components\TextInput::make('return_flight_no')
                                    // ->default(0)
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程航班編號'),
                            ])->columns(3),
                        ])->columns(2),
                ]),
                Group::make([

                    Section::make('其他資料')
                        ->schema([
                            Group::make([
                                Grid::make()->schema([
                                    Forms\Components\CheckboxList::make('night')
                                        ->label('')
                                        ->options([
                                            '200' => '夜間加成',
                                        ]),
                                    Forms\Components\CheckboxList::make('raise_a_placard')
                                        ->label('')
                                        ->options([
                                            '200' => '舉牌',
                                        ]),
                                    Forms\Components\CheckboxList::make('across_city')
                                        ->label('')
                                        ->options([
                                            '200' => '跨縣市',
                                        ])
                                ])->columns(3)
                            ]),
                            Group::make([
                                Forms\Components\Textarea::make('note')
                                    ->rows(5)
                                    ->label('備註'),

                                // Forms\Components\Textarea::make('driver_note')
                                //     ->rows(5)
                                //     ->label('司機備註'),
                            ])->columns(2),
                            Forms\Components\Placeholder::make('image_path')
                                ->label('簽單')
                                ->hiddenOn('create')
                                ->content(function ($record): HtmlString {
                                    if(!empty($record->image_path)){
                                        return new HtmlString("<img src= '" . asset('storage/'.$record->image_path)  . "?".time() . "' onclick=window.open('" . asset('storage/' . $record->image_path) . "?".time()."')>");
                                    }
                                    return new HtmlString('');
                                }),
                            Forms\Components\Placeholder::make('image2_path')
                                ->label('司機服務資訊')
                                ->hiddenOn('create')
                                ->content(function ($record): HtmlString {
                                    if(!empty($record->image2_path)){
                                        return new HtmlString("<img src= '" . asset('storage/'.$record->image2_path)  . "?".time() . "' onclick=window.open('" . asset('storage/' . $record->image2_path) . "?".time()."')>");
                                    }
                                    return new HtmlString('');
                                }),
                            // Forms\Components\FileUpload::make('attachment')
                            //     ->label('附件')
                            //     ->hiddenOn('create')
                        ])
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 101){
                            return '異常';
                        }else if($record['status'] == 102){
                            return '待審核';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已取消' => 'warning',
                        '異常' => 'danger',
                        '待審核' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->copyable()
                    ->copyableState(function (Dispatch $record){
                        if($record->driver_id == 11){
                            $timestamp = time();
                            return "http://carv2-web.chihlistudio.com/sign/" . Crypt::encryptString($record->dispatch_no)."?v=".$timestamp;
                        }
                    })
                    // ->copyMessage('已複製')
                    ->copyMessage(function (Dispatch $record) {
                        return $record->driver_id == 11 ? '已複製簽名鏈接' : '請選擇外派司機';
                    })
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('passenger_name', 'like', "%{$search}%")
                                    ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->toggleable()
                    ->label('乘客姓名/電話')
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        return $record->passenger_name . '<br />' . $record->passenger_mobile;
                    })->html(),
                Tables\Columns\SelectColumn::make('driver_id')
                    ->label('司機')
                    ->toggleable()
                    ->options(
                        Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id')->toArray()
                    )
                    ->afterStateUpdated(function ($state, $record) {
                        // dd($state);
                        if ($state) {
                            $record->status = 1;
                            $record->save();
                            $driver = Driver::where('id', $record->driver_id)->first();
                            $car = Car::where('driver_id', $record->driver_id)->first();
                            $customer = Customer::where('id', $record->customer_id)->first();
                            if($record->department_id != null){
                                $department = Department::where('id', $record->department_id)->first();
                                $record->department_name = $department->name;
                            }
                            $record->customer_title = $customer->title;
                            $record->driver_name = $driver->name;
                            $record->car = $car;
                            $record->car_license = $car->license;
                            if($record->location_type == 1){
                                $record->route = static::getAirportName( $record->start_location) . ' >> ' . $record->end_location;
                            }else if($record->location_type == 2){
                                $record->route = $record->start_location . ' >> ' . static::getAirportName( $record->end_location);
                            }else{
                                $record->route = $record->start_location . ' >> ' . $record->end_location;
                            }
                            if($record->signature_path != null){
                                $signature = storage_path('app/public/' .$record->signature_path);
                            }else{
                                $signature = null;
                            }
                            $rs = Dispatch::generateImage($record);
                            $rs2 = Dispatch::generateImageV2('dispatch2', $record);
                            if(!$rs){
                                FilamentNotification::make()
                                    ->title('簽名圖片生成失敗!請與車行洽詢!')
                                    ->danger()
                                    ->send();
                                // Log::error('簽名圖片生成失敗 for dispatch ID: ' . $record->id); // 可選的日誌記錄
                                // return; // 如果希望在此處停止執行
                            }else{
                                Dispatch::where('id', $record->id)
                                    ->update([
                                        // 'signature_file' => 'signatures/' . $fileName,
                                        'image_path' => $rs,
                                    ]);
                            }
                            if(!$rs2){
                                FilamentNotification::make()
                                    ->title('出租單生成失敗!請與車行洽詢!')
                                    ->danger()
                                    ->send();
                                // Log::error('出租單生成失敗 for dispatch ID: ' . $record->id); // 可選的日誌記錄
                                // return; // 如果希望在此處停止執行
                            }else{
                                Dispatch::where('id', $record->id)
                                    ->update([
                                        // 'signature_file' => 'signatures/' . $fileName,
                                        'image2_path' => $rs2,
                                    ]);
                            }
                        }else{
                            $record->status = 0;
                            $record->save();
                        }
                    })
                    ->extraAttributes([
                        'style' => 'width: 120px;',  // 設定固定寬度為150px
                    ])
                    ,
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->toggleable()
                    ->label('航班'),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    ->toggleable()
                    ->label('車型')
                    ->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '轎車';
                            case 1:
                                return 'Vito';
                            case 2:
                                return 'GRANVIA';
                            default:
                                return '轎車';
                        }
                    }),

                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->searchable()
                    ->limit(30)
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if($record['start_location'] == 1 || $record['end_location'] == 1){
                            $tmpdata = 'TSA 松山機場';
                        }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                            $tmpdata = '桃園機場 T1';
                        }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                            $tmpdata = '桃園機場 T2';
                        }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                            $tmpdata = '台中機場';
                        }else{
                            $tmpdata = $record['start_location'];
                        }
                        if($record['location_type'] == 1){
                            return $tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                // Tables\Columns\TextColumn::make('up_time')
                //     ->label('上/下車時間')
                //     ->formatStateUsing(function (Model $record) {
                //         $upTime = Carbon::parse($record->up_time);
                //         $downTime = Carbon::parse($record->down_time);
                //         $duration = $upTime->diffInMinutes($downTime);
                //         $hours = floor($duration / 60);
                //         $minutes = $duration % 60;
                //         return $upTime->format('H:i') . ' <br />' .  $downTime->format('H:i') . '<br />計: ' . $hours . ' 時 ' . $minutes . ' 分';
                //     })->html(),
                Tables\Columns\TextColumn::make('pay_type')
                    ->toggleable()
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return 'LINE PAY';
                            case 3:
                                return '匯款';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextInputColumn::make('rental_cost')
                    ->label('月結/現金')
                    ->afterStateUpdated(function ($state, Model $record) {
                        // 當值更新後，儲存變更
                        $record->update(['rental_cost' => $state]);
                    })
                    ->extraInputAttributes(['oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'])  // 僅允許數字輸入
                    // ->confirmationDialog('確定修改車資嗎？') // 加上確認對話框
                    ->action(function ($state, Model $record) {
                        $record->rental_cost = $state;
                        $record->save();
                    })
                    ->extraAttributes([
                        'style' => 'width: 5rem !important; min-width: 5rem !important; max-width: 6rem !important;',
                    ])
                    ,
                Tables\Columns\TextInputColumn::make('driver_fee')
                    ->label('司機車資')
                    ->extraAttributes([
                        'style' => 'width: 5rem !important; min-width: 5rem !important; max-width: 6rem !important;',
                    ]),
                // Tables\Columns\TextColumn::make('driver_fee2')
                //     ->label('司機車資2'),
                // Tables\Columns\TextColumn::make('rental_cost')
                //     ->label('金額')
                //     ->numeric()
                //     // ->summarizeLabel('本頁總計')
                //     ->summarize(Sum::make()->label('總計金額')),
                // Tables\Columns\TextColumn::make('driver_fee')
                //     ->label('司機車資')
                //     ->numeric()
                //     ->summarize(Sum::make()->label('總計金額'))
            ])
            ->striped()
            ->defaultPaginationPageOption(25)
            // ->deferLoading()
            ->defaultSort('start_date', 'asc')
            ->persistSearchInSession(true)
            ->persistFiltersInSession(true)
            ->recordUrl(function (Model $record, Table $table): string {
                $baseUrl = static::getUrl('edit', ['record' => $record]);
                $livewire = $table->getLivewire();
                $queryParams = [];
                // 只取目前列表頁的 activeTab
                if (property_exists($livewire, 'activeTab') && !is_null($livewire->activeTab)) {
                    $queryParams['activeTab'] = $livewire->activeTab;
                }
                if (property_exists($livewire, 'tableSearch') && !empty($livewire->tableSearch)) {
                    $queryParams['tableSearch'] = $livewire->tableSearch;
                }
                if (property_exists($livewire, 'tableColumnSearchQueries') && is_array($livewire->tableColumnSearchQueries) && !empty($livewire->tableColumnSearchQueries)) {
                    $queryParams['tableColumnSearchQueries'] = $livewire->tableColumnSearchQueries;
                }
                if (property_exists($livewire, 'tableFilters') && is_array($livewire->tableFilters)) {
                    $activeFilters = array_filter($livewire->tableFilters, function ($value) {
                        return !is_null($value) && $value !== '';
                    });
                    if (!empty($activeFilters)) {
                        $queryParams['tableFilters'] = $activeFilters;
                    }
                }
                if (property_exists($livewire, 'tableSortColumn') && $livewire->tableSortColumn) {
                    $queryParams['tableSortColumn'] = $livewire->tableSortColumn;
                }
                if (property_exists($livewire, 'tableSortDirection') && $livewire->tableSortDirection) {
                    $queryParams['tableSortDirection'] = $livewire->tableSortDirection;
                }
                if (
                    property_exists($livewire, 'tableRecordsPerPage') &&
                    method_exists($livewire, 'getTableRecordsPerPage') && // 確保主要方法存在
                    $livewire->getTableRecordsPerPage() !== null // 如果已設定且非 null，則加入參數
                ) {
                    $queryParams['tableRecordsPerPage'] = $livewire->getTableRecordsPerPage();
                }
                if (property_exists($livewire, 'paginators') && isset($livewire->paginators['page']) && $livewire->paginators['page'] > 1) {
                    $queryParams['page'] = $livewire->paginators['page'];
                }
                $queryString = \Illuminate\Support\Arr::query($queryParams);
                return $queryString ? ($baseUrl . '?' . $queryString) : $baseUrl;
            })
            ->filters([
                Filter::make('customer_id')
                    ->label('查詢')
                    ->form([
                        Select::make('customer_id')
                            ->label('公司名稱')
                            ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->orderBy('sequence')->pluck('title', 'id'))
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('department_id', null);
                            }),
                        Select::make('department_id')
                            ->label('部門')
                            ->options(function ($get) {
                                return Department::where('customer_id', $get('customer_id'))
                                    ->where('vendor_id', Filament::auth()->user()->vendor_id)
                                    ->pluck('name', 'id');
                            }),
                            // ->options(Department::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // dd($data);
                        return $query
                            ->when($data['customer_id'], fn ($query, $customer_id) => $query->where('customer_id', $customer_id)->where('vendor_id', Filament::auth()->user()->vendor_id))
                            ->when($data['department_id'], fn ($query, $department_id) => $query->where('department_id', $department_id))->where('vendor_id', Filament::auth()->user()->vendor_id);
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['customer_id']) {
                            $customer_name = Customer::find($data['customer_id'])->title;
                            $indicators['customer_id'] = '公司: ' . $customer_name;
                        }
                        if ($data['department_id']) {
                            $department_name = Department::find($data['department_id'])->name;
                            $indicators['department_id'] = '部門: ' . $department_name;
                        }
                        return $indicators;
                    })
                    ,
                    SelectFilter::make('driver_id')
                    ->label('司機')
                    ->options(
                        Driver::where('vendor_id', Filament::auth()->user()->vendor_id)
                            ->where('active', 1)
                            ->get()
                            ->mapWithKeys(function ($driver) {
                                $displayName = $driver->join_type == 1 ? "{$driver->name}-[外]" : $driver->name;
                                return [$driver->id => $displayName];
                            })->toArray()
                    )
                    ->searchable()
                    ->preload(),
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('start_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('start_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
                SelectFilter::make('status')
                    ->label('狀態')
                    ->searchable()
                    ->options([
                        '0' => '派遣中',
                        '1' => '已指派司機',
                        '2' => '已完成',
                        '3' => '已取消',
                        '101' => '錯誤',
                        '102' => '待審核'
                    ]),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                //Access to filter data
                $request = Request::instance();
                $filters = $request->query();

                //You can now access the filter values directly.
                //Example
                $startDate = $filters['tableFilters']['start_date']['start_date'] ?? null;
                $endDate = $filters['tableFilters']['start_date']['end_date'] ?? null;
                $customerId = $filters['tableFilters']['customer_id']['customer_id'] ?? null;
                $departmentId = $filters['tableFilters']['customer_id']['department_id'] ?? null;

                //You can also store it in Session manually if needed.
                Session::put('my_custom_filters', $filters);
                //or get it in Session
                $myFilters = Session::get('my_custom_filters');
                foreach ($filters as $key => $value) {
                    if (strpos($key, 'tableFilters') === 0) {
                        $query->when($value, function ($query, $value) use ($key) {
                            switch ($key) {
                                case 'tableFilters.status.0':
                                    $query->where('status', $value);
                                    break;
                                case 'tableFilters.location_type.0':
                                    $query->where('location_type', $value);
                                    break;
                                case 'tableFilters.driver_id.0':
                                    $query->where('driver_id', $value);
                                    break;
                                case 'tableFilters.customer_id.0':
                                    $query->where('customer_id', $value);
                                    break;
                                    //date range
                                case 'tableFilters.start_date.start_date':
                                    $query->whereDate('start_date', '>=', $value);
                                    break;
                                case 'tableFilters.start_date.end_date':
                                    $query->whereDate('start_date', '<=', $value);
                                    break;
                                case 'tableFilters.customer_id.customer_id':
                                    $query->where('customer_id', $value);
                                    break;
                                case 'tableFilters.customer_id.department_id':
                                    $query->where('department_id', $value);
                                    break;
                            }
                        });
                    }
                }
            })
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),
                    \App\Actions\Tables\CopyIdAction::make('copy'),
                    Tables\Actions\Action::make('update-status')
                            ->label('移除簽名檔')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['signature_path' => null, 'status' => 1]);
                                // $record->save();
                            }),
                    Tables\Actions\Action::make('copy-driver-info')
                            ->label('複製司機資訊')
                            ->icon('heroicon-o-clipboard-document')
                            ->action(function ($record, $livewire) {
                                // 獲取司機資訊
                                $driver = $record->driver;
                                if (!$driver) {
                                    FilamentNotification::make()
                                        ->title('無法複製')
                                        ->body('此派車單沒有指定司機')
                                        ->warning()
                                        ->send();
                                    return;
                                }

                                // 獲取車輛資訊
                                $car = Car::where('driver_id', $driver->id)->first();
                                $carLicense = $car ? $car->license : '無車牌資訊';
                                $carBrand = $car ? $car->brand : '無車型資訊'; // 新增：獲取車輛品牌

                                // 組合要複製的文字
                                $copyText = "司機姓名：{$driver->name}\n";
                                $copyText .= "司機手機：{$driver->mobile}\n";
                                $copyText .= "車牌號碼：{$carLicense}\n";
                                $copyText .= "車型：{$carBrand}";

                                // 使用 $livewire->js() 方法執行 JavaScript
                                $livewire->js(
                                    'window.navigator.clipboard.writeText(' . json_encode($copyText) . ');
                                    $tooltip(' . json_encode(__('已複製司機資訊到剪貼簿中!')) . ', { timeout: 1500 });'
                                );

                                // 顯示通知
                                FilamentNotification::make()
                                    ->title('已複製司機資訊')
                                    ->body('司機姓名、手機、車牌號碼和車型已複製到剪貼簿') // 更新通知內容
                                    ->success()
                                    ->send();
                            }),
                    Tables\Actions\DeleteAction::make(),
                ])
                ->label('操作')
                ->icon('heroicon-o-cog'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('makeReport')
                        ->label('建立報表')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            $start_date = $filters['start_date'];
                            // dd($filters);
                            // $customer_title = Customer::find($filters['customer_id']['customer_id'])->title ?? '客戶名稱';
                            // $department_name = Department::find($filters['customer_id']['department_id'])->name ?? '部門名稱';

                            // $modalSubheading = static::getModalSubheading($filters);
                            // $search = $livewire->tableSearch;
                            if(empty($filters['customer_id']['customer_id'])){
                                FilamentNotification::make()
                                        ->title('錯誤!請先選擇客戶名稱及部門名稱')
                                        ->danger()
                                        ->send();
                                    return;
                            }
                            if(empty($filters['start_date']['start_date']) || empty($filters['start_date']['end_date'])){
                                FilamentNotification::make()
                                        ->title('錯誤!請先選擇日期範圍')
                                        ->danger()
                                        ->send();
                                    return;
                            }
                            if($filters['status']['value']!=2){
                                FilamentNotification::make()
                                        ->title('錯誤!請先選擇已完成之派車單')
                                        ->danger()
                                        ->send();
                                    return;
                            }
                            $fileName = self::generatePdf($records, $filters, $start_date);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Filament::auth()->user();

                            FilamentNotification::make()
                                ->title('報表已建立')
                                ->body("報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        // ->visible(function () {
                        //     return Filament::auth()->user()->role == 'admin';
                        // })
                        ->icon('heroicon-o-document')
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        // ->modalContent(function ($record) {
                        //     return view('filament.actions.make-report', ['record' => $record]);
                        // })
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    Tables\Actions\BulkAction::make('sendLineMsg')
                        ->label('傳送訊息')
                        ->icon('heroicon-o-paper-airplane')
                        ->action(function ($records, $data, $livewire): void {
                            // dd($records);
                            $driver = Driver::find($records[0]->driver_id);
                            $user_line_id = $driver->line_id; //'U91beb5b6a562f58c57ee1b52b1e7ada0'; //$record->line_id;
                            $channelToken = config('line.channel_access_token');
                            $message = '派車單資訊! <br />';
                            foreach ($records as $key => $record) {
                                $message .= '=========='.'<br />';
                                $message .= '派車日期：'.$record->start_date.'<br />';
                                $message .= '派車時間：'.$record->start_time.'<br />';
                                $message .= '上下車地點：'.$record->route.'<br />';
                                $message .= '航班編號：'.$record->flight_no.'<br />';
                                $message .= '=========='.'<br />';
                            }
                            // static::pushMsg($message, $user_line_id, $channelToken);
                            $user = Filament::auth()->user();
                            FilamentNotification::make()
                                ->title('訊息已發送')
                                ->body("派車單訊息已發送到司機: ({$driver->name})\n {$message}")
                                ->success()
                                ->sendToDatabase($user);
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認發送訊息')
                        ->modalSubheading(function($records){
                            $message = '派車單資訊!<br/>';
                            foreach ($records as $key => $record) {
                                $message .= '==========<br>';
                                $message .= '派車日期：'.$record->start_date.'<br />';
                                $message .= '派車時間：'.$record->start_time.'<br />';
                                $message .= '上下車地點：'.$record->route.'<br />';
                                $message .= '航班編號：'.$record->flight_no.'<br />';
                                $message .= '=========='.'<br />';
                            }
                            return new HtmlString($message);
                        })
                        ->modalButton('發送訊息'),
                    Tables\Actions\ExportBulkAction::make()
                        ->exporter(DispatchExporter::class)
                ]),
            ]);
    }
    protected static function processDynamicInput($input)
    {
        // dd($input);
        $singleLineString = preg_replace('/\s+/', ' ', $input);
        // 使用正則表達式或其他方式解析輸入字符串
        // $pattern = '/名稱: (?<passager_name>[^,]+),\s*手機:\s*(?<passager_mobile>\d+),\s*價格:\s*(?<rental_cost>\d+)/';
        $pattern = '/名稱:(.*)\s*手機:(.*)\s*價格:\s*(\d+)/';
        if (preg_match($pattern, $singleLineString, $matches)) {
            dd($matches);
            return [
                'passager_name' => $matches[1],
                'passager_mobile' => $matches[2],
                'rental_cost' => $matches[3],
            ];
        } else {
            dd($matches);
            // $this->notify('error', '輸入格式不正確，請重新嘗試。');
            return [];
        }
    }
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDispatches::route('/'),
            'create' => Pages\CreateDispatch::route('/create'),
            'edit' => Pages\EditDispatch::route('/{record}/edit'),
        ];
    }

    protected static function generatePdf(Collection $records, $customer_data=null, $start_date=null)
    {
        $total = 0;
        // dd($records);
        $customer = Customer::where('id',$customer_data['customer_id']['customer_id'])->first()->toArray() ?? '客戶名稱';
        $department_name = Department::find($customer_data['customer_id']['department_id'])->name ?? '部門名稱';
        // $sql = "select customer_id, COUNT(id) AS COUNT, SUM(rental_cost) AS sum FROM dispatches WHERE customer_id={$customer_data['customer_id']['customer_id']} AND status=2 AND department_id={$customer_data['customer_id']['department_id']} AND (start_date between '{$start_date["start_date"]} 00:00:00' AND '{$start_date["end_date"]} 23:59:59') GROUP BY customer_id";
        // $data = DB::select($sql);
        foreach ($records as $key => $value) {
            if($value->driver_id == null){
                $driver_car_license = '';
            }else{
                $driver_car_license = Car::where('driver_id', '=', $value->driver_id)->pluck('license')->first();

            }
            $records[$key]->car_license = $driver_car_license;
        }
        // dd($driver_car_license);
        // $customer_title = 'ABC';
        $data['customer_title'] = $customer['title'] . ' ' . $department_name;
        $data['start_end_date'] = $start_date['start_date'] . ' - ' . $start_date['end_date'];
        $data['company_id'] = $customer['company_id'];
        $data['items'] = $records;
        $data['count'] = count($records);
        // dd($data);
        // 生成 PDF
        $pdf = Pdf::loadView('report.myreport', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        // 保存 PDF 到本地
        $fileName = 'Report_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
        Log::info($fileName);
        $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
        $pdf->save($filePath);
        return $fileName;
    }
    // public static function getModalSubheading($data):string
    // {
    //     // dd($data);
    //     $customerTitle = Customer::find($data['customer_id']['customer_id'])->title ?? '客戶名稱';
    //     $departmentName = Department::find($data['customer_id']['department_id'])->name ?? '部門名稱';

    //     return "選取派車單，建立報表! {$customerTitle} - {$departmentName}";
    // }
    public static function pushMsg($message, $uid, $ChannelAccessToken):void
    {
        $Payload = [
            'to' => $uid,
            'messages' => [
                [
                    'type' => 'text',
                    'text' => $message
                ]
            ]
        ];
        // 傳送訊息
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/bot/message/push');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($Payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $ChannelAccessToken
        ]);
        $Result = curl_exec($ch);
        curl_close($ch);
        // return $Result;
    }
    public static function getAirportName($data)
    {
        if($data==1){
            return 'TSA 松山機場';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '台中機場';
        }
        return '資料錯誤!';
    }
}

