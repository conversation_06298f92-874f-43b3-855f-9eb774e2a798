<?php

namespace App\Filament\Resources\DispatchResource\Widgets;

use App\Models\Dispatch;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Filament\Widgets\ChartWidget;

class DispatchCaseChart extends ChartWidget
{
    protected static ?string $heading = '一周派車單數';
    protected static string $color = 'info';
    protected static ?int $sort = 1;

    protected function getData(): array
    {
        $startDate = now()->addDays(-6)->startOfDay();
        $endDate = now()->endOfDay();
        if(auth()->user()->vendor_id==0){
            $data = Trend::model(Dispatch::class)
                ->between(
                    start: $startDate,
                    end: $endDate,
                )
                ->perDay()
                ->dateColumn('start_date')
                ->count();
        }else{



            // 獲取基於 start_date 的統計數據
            $data = Trend::query(
                    Dispatch::query()
                        ->where('vendor_id', '=', auth()->user()->vendor_id)
                        ->whereBetween('start_date', [$startDate, $endDate]) // 確保在此範圍內
                )
                ->between(
                    start: $startDate,
                    end: $endDate,
                )
                ->perDay()
                ->dateColumn('start_date') // 使用 start_date 欄位
                ->count();
        }

        // dd($data);die;
        return [
            'labels' => $data->map(fn (TrendValue $item) => $item->date),
            'datasets' => [
                [
                    'label' => '最後7天派車數使用數',
                    'data' => $data->map(fn (TrendValue $item) => $item->aggregate),
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1,
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
