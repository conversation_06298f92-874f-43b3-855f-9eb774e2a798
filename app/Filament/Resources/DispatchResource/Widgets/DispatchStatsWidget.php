<?php

namespace App\Filament\Resources\DispatchResource\Widgets;

use App\Models\Dispatch;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class DispatchStatsWidget extends BaseWidget
{
    public $totalRentalCost; // 宣告一個 public property

    public function mount(): void // 在元件掛載時執行
    {
        $this->updateTotal(); // 初始計算總計
    }

    public function updatedTableFilters(): void // 當表格過濾器更新時執行
    {
        $this->updateTotal(); // 重新計算總計
    }

    private function updateTotal(): void // 獨立的更新總計方法
    {
        $query = Dispatch::query();
        $table = static::getTable(); // Get the table instance
        $activeFilters = $table->getFilters();
        $filterData = [];
        dd($activeFilters);
        $activeFilters = $this->getTable()->getFilters();
        foreach ($activeFilters as $filter) {
            if ($filter instanceof Filter) {
                $query->where(function (Builder $query) use ($filter) {
                    $data = $filter->getState();
                    $filter->apply($query, $data);
                });
            }
        }

        $this->totalRentalCost = $query->sum('rental_cost') ?? 0; // 使用 null coalescing operator 避免 null 值

    }
    protected function getStats(): array
    {
        dd($this->filters);
        $dispatch = Dispatch::where('vendor_id', auth()->user()->vendor_id)->first();
        return [
            Stat::make('總車資', 1200),
            Stat::make('總收現金', 600),
            Stat::make('總車資 + 總收現金', 1800)
        ];
    }
}
