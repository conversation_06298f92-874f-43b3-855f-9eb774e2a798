<?php

namespace App\Filament\Resources\DispatchResource\Widgets;

use App\Models\Dispatch;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Filament\Widgets\ChartWidget;

class DispatchTotalChart extends ChartWidget
{
    protected static ?string $heading = '一周車資總計';
    protected static string $color = 'success';
    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $startDate = now()->addDays(-6)->startOfDay();
        $endDate = now()->endOfDay();
        if(auth()->user()->vendor_id==0){
            $data = Trend::model(Dispatch::class)
                ->between(
                    start: $startDate,
                    end: $endDate,
                )
                ->perDay()
                ->dateColumn('start_date')
                ->sum('rental_cost');
        }else{

            $data = Trend::query(
                    Dispatch::query()
                        ->where('vendor_id', '=', auth()->user()->vendor_id)
                )
                ->between(
                    start: $startDate,
                    end: $endDate,
                )
                ->perDay()
                ->dateColumn('start_date')
                ->sum('rental_cost');
        }

        return [
            'labels' => $data->map(fn (TrendValue $item) => $item->date),
            'datasets' => [
                [
                    'label' => '最後7天車資總計',
                    'data' => $data->map(fn (TrendValue $item) => $item->aggregate),
                    // 'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    // 'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1,
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
