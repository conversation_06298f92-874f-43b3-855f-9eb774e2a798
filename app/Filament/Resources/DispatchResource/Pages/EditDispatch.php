<?php

namespace App\Filament\Resources\DispatchResource\Pages;

use App\Models\Car;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\DispatchResource;

class EditDispatch extends EditRecord
{
    protected static string $resource = DispatchResource::class;
    protected static ?string $title = '編輯派車單';

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->modalHeading('刪除此筆訂車單嗎？'),
        ];
    }
    protected function getRedirectUrl(): string
    {
        $queryString = session('dispatch_return_query_string', '');
        // 清掉 session，避免下次編輯還殘留
        session()->forget('dispatch_return_query_string');
        $url = $this->getResource()::getUrl('index');
        return $queryString ? ($url . '?' . $queryString) : $url;
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            // url('/admin/dispatches') => '派車單',
            url('/admin/dispatches') => '列表',
        ];
    }
    public function handleRecordUpdate(Model $record, array $data):Dispatch
    {
        // dd($data);
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Dispatch::find($record->id);
        $dispatch->update($data);
        $dispatch->save();
        if(isset($dispatch->driver_id) && $dispatch->driver_id != null){
            // dd($dispatch);
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = $car;
            $dispatch->car_license = $car->license;
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            if($dispatch->signature_path != null){
                $signature = storage_path('app/public/' .$dispatch->signature_path);
            }else{
                $signature = null;
            }

            $rs = Dispatch::generateImage($dispatch, $signature);
            $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch);

            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            $dispatch->image_path = $rs;
            $dispatch->image2_path = $rs2;
            $dispatch->save();
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $signature = null;

            $rs = Dispatch::generateImage($dispatch, $signature);
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            $dispatch->save();
        }
        return $dispatch;
    }
    public function getAirportName($data)
    {
        if($data==1){
            return 'TSA 松山機場';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '台中機場';
        }
        return '資料錯誤!';
    }

    public function mount(string|int $record): void
    {
        parent::mount($record);

        // 只在第一次進入編輯頁時存 query string 和 activeTab
        if (!session()->has('dispatch_return_query_string')) {
            session(['dispatch_return_query_string' => request()->getQueryString()]);
            $activeTab = request()->query('activeTab');
            if ($activeTab) {
                session(['activeTab' => $activeTab]);
            }
        }

        // 這裡不要再覆蓋 $this->activeTab，讓回去時還原正確
    }
}
