<?php

namespace App\Filament\Resources\DispatchResource\Pages;

use App\Models\Car;
use App\Models\User;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use Illuminate\Support\Facades\Log;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\DispatchResource;

class CreateDispatch extends CreateRecord
{
    protected static string $resource = DispatchResource::class;
    protected static ?string $title = '建立派車單';
    public function getBreadcrumbs(): array
    {
        return
        [
            // url('/admin/dispatches') => '派車單',
            url('/admin/dispatches') => '列表',
        ];
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function handleRecordCreation(array $data): Dispatch
    {
        // dd($data);
        // $rs = $this->mutateFormDataBeforeCreate($data);
        $data['end_date'] = \Carbon\Carbon::parse($data['start_date'])->addHour();
        $dispatch = Dispatch::create($data);
        if(isset($data['driver_id']) && $data['driver_id'] != null){
            // dd($dispatch);
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            $dispatch->driver_name = $driver->name;
            $dispatch->driver = $driver;
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->car = $car;
            $dispatch->car_license = $car->license;
            // $dispatch->car_license = $car->license;
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
            }
            $rs = Dispatch::generateImage($dispatch);
            $rs2 = Dispatch::generateImageV2('dispatch2', $dispatch);
            // $dispatch = Dispatch::find($dispatch->id);
            Log::info('建立 DISPATCH >> '.json_encode($dispatch));
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            $dispatch->image2_path = $rs2;
            $dispatch->save();
            if($dispatch->return == 1){
                $returnData = [
                    'id' => $dispatch->id,
                    'start_date' => $data['return_date'],
                    'end_date' => \Carbon\Carbon::parse($data['return_date'])->addHour(),
                    'customer_id' => $data['customer_id'],
                ];
            }
        }else{
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            $user = User::where('id', auth()->user()->id)->first();
            $vendor = Vendor::where('id', $user->vendor_id)->first();
            // $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->driver_name = '';
            $dispatch->driver = [];
            $dispatch->customer_title = $customer->title;
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->department_name = empty($department->name) ? '' : $department->name;
            $dispatch->car = [];
            $dispatch->car_license = '';
            if($data['location_type'] == 1){
                $dispatch->route = $this->getAirportName( $dispatch->start_location) . ' >> ' . $dispatch->end_location;
                $return_router = $dispatch->end_location . ' >> ' . $this->getAirportName( $dispatch->start_location);
            }else if($data['location_type'] == 2){
                $dispatch->route = $dispatch->start_location . ' >> ' . $this->getAirportName( $dispatch->end_location);
                $return_router = $this->getAirportName( $dispatch->end_location) . ' >> ' . $dispatch->start_location;
            }else{
                $dispatch->route = $dispatch->start_location . ' >> ' . $dispatch->end_location;
                $return_router = $dispatch->end_location  . ' >> ' .  $dispatch->start_location;
            }
            $rs = Dispatch::generateImage($dispatch);
            unset($dispatch->driver_name);
            unset($dispatch->car_license);
            unset($dispatch->driver);
            unset($dispatch->car);
            unset($dispatch->customer_title);
            unset($dispatch->department_name);
            unset($dispatch->car_license);
            $dispatch->image_path = $rs;
            $dispatch->save();
            if($data['return'] == 1){
                $return_location_type = $this->getReturnLocationType($data['location_type']);
                $return_rental_cost = $data['rental_cost'];
                if($data['location_type']==1)   {
                    $return_rental_cost = $data['rental_cost']-200;
                }else if($data['location_type']==2){
                    $return_rental_cost = $data['rental_cost']+200;
                }
                $returnData = [
                    // 'id' => $dispatch->id,
                    'start_date' => $data['return_date'],
                    'end_date' => \Carbon\Carbon::parse($data['return_date'])->addHour(),
                    'customer_id' => $data['customer_id'],
                    "vendor_id" => auth()->user()->vendor_id,
                    "department_id" => empty($data['department_id']) ? null : $data['department_id'],
                    "passenger_name" => empty($data['passenger_name']) ? null : $data['passenger_name'],
                    "passenger_mobile" => empty($data['passenger_mobile']) ? null : $data['passenger_mobile'],
                    "location_type" => $return_location_type,
                    "start_location" => empty($data['end_location']) ? null : $data['end_location'],
                    "end_location" => empty($data['start_location']) ? null : $data['start_location'],
                    "flight_no" => empty($data['return_flight_no']) ? null : $data['return_flight_no'],
                    "cartype_id" => $data['cartype_id'],
                    "pay_type" => $data['pay_type'],
                    "num_of_people" => empty($data['num_of_people']) ? null : $data['num_of_people'],
                    "num_of_bags" => empty($data['num_of_bags']) ? null : $data['num_of_bags'],
                    "child_seat" => empty($data['child_seat']) ? null : $data['child_seat'],
                    "bootster_pad" => empty($data['bootster_pad']) ? null : $data['bootster_pad'],
                    "route" => $return_router,
                    "status" => 0,
                    "rental_cost" => (int)$return_rental_cost,
                    "return" => 2,
                ];
                $returnRs = Dispatch::create($returnData);
                // dd($returnRs);
            }
        }
        // $data['dispatch_no'] = $rs->dispatch_no;
        return $dispatch;
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd($data);
        // 自定義邏輯
        return $data;
    }
    public function getAirportName($data)
    {
        if($data==1){
            return '松山機場 TSA';
        }else if($data==2){
            return '桃園機場 T1';
        }else if($data==3){
            return '桃園機場 T2';
        }else if($data==4){
            return '桃園機場';
        }else if($data==5){
            return '台中機場';
        }
        return '資料錯誤!';
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function getReturnLocationType($data)
    {
        if($data==1){
            return 2;
        }else if($data==2){
            return 1;
        }else if($data==3){
            return 3;
        }
    }
}
