<?php

namespace App\Filament\Resources\DispatchResource\Pages;

use Filament\Actions;
use App\Models\Dispatch;
use Filament\Actions\Action;
use App\Imports\DispatchImport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Cache;
use Filament\Resources\Components\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\DispatchResource;

class ListDispatches extends ListRecords
{
    protected static string $resource = DispatchResource::class;
    protected static ?string $title = '派車單列表';
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增派車單')
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
    public function getTabs(): array
    {
        return [
            '今日' => Tab::make()
                        ->badge(function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->toDateString())
                                ->count();
                        })
                        ->modifyQueryUsing(function ($query) {
                            session(['activeTab' => '今日']);
                            return $query->whereDate('start_date', '=', now()->toDateString())->orderBy('start_date', 'asc');
                        }),
            '明日' => Tab::make()
                        ->badge(function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereDate('start_date', '=', now()->addDay()->toDateString())
                                ->count();
                        })
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '明日']);
                            return $query->whereDate('start_date', '=', now()->addDay()->toDateString())->orderBy('start_date', 'asc');
                        }),
            '本周' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_this_week_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])
                                ->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本周']);
                            return $query->whereBetween('start_date', [now()->startOfWeek(), now()->endOfWeek()])->orderBy('start_date', 'asc');
                        }),
            '本月' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_this_month_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])
                                ->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '本月']);
                            return $query->whereBetween('start_date', [now()->startOfMonth(), now()->endOfMonth()])->orderBy('start_date', 'asc');
                        }),
            '未指派' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_unassigned_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->where('status', 0)
                                ->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '未指派']);
                            return $query->where('status', 0)->orderBy('start_date', 'asc');
                        }),
            '異常' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_assigned_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->where('status', 101)
                                ->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '異常']);
                            return $query->where('status', 101)->orderBy('start_date', 'asc');
                        }),
            '已完成' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_completed_' . auth()->user()->vendor_id, 600, function () {
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)
                                ->where('status', 2)
                                ->count();
                        }))
                        ->modifyQueryUsing(function (Builder $query) {
                            session(['activeTab' => '已完成']);
                            return $query->where('status', 2)->orderBy('start_date', 'asc');
                        }),
            '全部' => Tab::make()
                        ->badge(fn () => Cache::remember('dispatch_all_' . auth()->user()->vendor_id, 600, function () {
                            session(['activeTab' => '全部']);
                            return Dispatch::where('vendor_id', auth()->user()->vendor_id)->count();
                        })),
        ];
    }
    protected function getFilteredCount(Builder $query): int
    {
        return (clone $query)->count();
    }
    public function parseText($dispatchText): array
    {
        $lines = explode("\n", $dispatchText);

        // 初始化空數組
        $items = array();

        foreach ($lines as $line) {
            // 確保行不是空的
            if (!empty(trim($line))) {
                // 將行拆分為鍵和值
                list($key, $value) = explode(":", $line, 2);
                $items[trim($key)] = trim($value);
            }
        }
        // 2. 初始化結果陣列
        $resultArray = [];

        // 3. 定義中文鍵名與英文鍵名的對應關係 (方便後續轉換)
        $keyMap = [
            '"類別' => 'location_type',
            '出發日期' => 'start_date',
            '結束日期' => 'end_date',
            '客戶名稱' => 'customer_name',
            '乘客資訊' => 'passenger_name',
            '路程' => 'router',
            '司機資訊' => 'driver',
            '車輛類別' => 'cartype_id',
            // '派車類別' => 'location_type', // '1' => '接機','2' => '送機','0' => '其他','3' => '包車',
            '上車地點' => 'start_location',
            '下車地點' => 'end_location',
            '付款方式' => 'pay_type',
            '車資' => 'rental_cost',
            '航班編號' => 'flight_no',
            '廠商編號' => 'vendor_id',
            '乘車人數' => 'num_of_people',
        ];

                // if ($keyChinese === '司機') {
                //     // 使用正則表達式判斷值是否為 "數字-" 開頭
                //     if (preg_match('/^(\d+)-/', $value, $matches)) {
                //         // 如果符合，則提取數字部分 ($matches[1] 包含捕獲的數字)
                //         $value = $matches[1];
                //     }
                // }
        // 4. 迴圈處理分割後的每個元素
        foreach ($items as $chineseKey => $value) {
            if (isset($keyMap[$chineseKey])) {
                $englishKey = $keyMap[$chineseKey];
                $currentValue = $value; // 先將原始值賦值給一個變數，方便後續修改
                // 針對 'location_type' 進行特殊處理，提取數字
                if ($englishKey === 'location_type') {
                    if (preg_match('/^(\d+)-/', $currentValue, $matches)) {
                        $currentValue = $matches[1]; // 如果符合 "數字-" 開頭，則提取數字部分
                    }
                    // 如果不符合 "數字-" 開頭的格式，則 $currentValue 維持原值
                }
                if ($englishKey === 'driver') {
                    if (preg_match('/^(\d+)-/', $currentValue, $matches)) {
                        $currentValue = $matches[1]; // 如果符合 "數字-" 開頭，則提取數字部分
                    }
                    // 如果不符合 "數字-" 開頭的格式，則 $currentValue 維持原值
                }
                $resultArray[$englishKey] = $currentValue; // 使用可能修改過的值
            } else {
                // 未知鍵名處理 (可根據需求調整)
                // error_log("Warning: No English key found for Chinese key: " . $chineseKey);
            }
        }
        dd($resultArray);
        return $resultArray;
    }
    public function getNumber($string)
    {
        $parts = explode("-", $string); // 將字符串拆分為陣列
        $number = $parts[0]; // 取出陣列中的第一個元素（即 "-" 之前的數字）
        echo $number;
    }


}
