<?php

namespace App\Filament\Resources\SinShengReportResource\Pages;

use App\Filament\Resources\SinShengReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSinShengReports extends ListRecords
{
    protected static string $resource = SinShengReportResource::class;
    protected static ?string $title = '客戶報表';
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            url('/admin/dispatches') => '列表',
        ];
    }
}
