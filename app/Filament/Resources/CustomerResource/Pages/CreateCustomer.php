<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Filament\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCustomer extends CreateRecord
{
    protected static string $resource = CustomerResource::class;
    protected static ?string $title = '建立客戶';
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    // protected function handleRecordCreation(array $data): Dispatch
    // {
    //     return parent::handleRecordCreation($data);
    // }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd(auth()->user()->vandor_id);
        $data['vendor_id'] = auth()->user()->vendor_id;
        // dd($data);

        return $data;
    }
}
