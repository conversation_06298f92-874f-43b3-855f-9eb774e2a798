<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use App\Filament\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListInvoices extends ListRecords
{
    protected static string $resource = InvoiceResource::class;
    protected static ?string $title = '發票列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增發票'),
        ];
    }
    protected function getActions(): array
    {
        return [
            // 移除 create 操作以隱藏新增按鈕
        ];
    }
}
