<?php

namespace App\Filament\Resources\InvoiceResource\Pages;

use Log;
use Actions\Action;
use Filament\Actions;
use App\Models\Invoice;
use App\Traits\CreateInvoiceTrait;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\InvoiceResource;

class CreateInvoice extends CreateRecord
{
    use CreateInvoiceTrait;
    protected static string $resource = InvoiceResource::class;
    protected static ?string $title = '開立電子發票';

    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getFormActions(): array
    {
        return [
            // 移除預設的 CreateAction
            // Actions\CreateAction::make(),
            Actions\Action::make('customAction')
                ->label('儲存並開立發票')
                ->action(function(){
                    $this->executeCustomFunction();
                    return redirect('/admin/invoices');
                })
                ->color('success')
                ->size('md'),
            Actions\Action::make('cancel')
                ->label('取消')
                ->url($this->getResource()::getUrl('index'))
                ->color('danger')
                ->size('md'),
        ];
    }
    protected function getActions(): array {
        return [
            // Actions\CreateAction::make(),
            // Actions\Action::make('customAction')
            //     ->label('儲存並開立發票')
            //     ->action(function(){
            //         $this->executeCustomFunction();
            //         // Notification::make()
            //         //             ->title('發票已建立')
            //         //             ->success()
            //         //             ->send();
            //         return redirect('/admin/invoices');
            //         // try {
            //         //     // $data = $this->data();
            //         //     // ... 其他邏輯

            //         // } catch (\Exception $e) {
            //         //     // 捕獲異常，並記錄或顯示錯誤訊息
            //         //     report($e);
            //         //     $this->notify('錯誤發生，請稍後再試。');
            //         // }
            //         // $data = $this->data();
            //         // dd($data);
            //         // $this->executeCustomFunction($data);
            //     })
            //     ->color('success')
            //     ->size('md'),
        ];
    }
    public function executeCustomFunction()
    {
        $data = $this->form->getState();
        // $validatedData = $this->validate($data, [
            //     'name' => 'required|string',
            //     'email' => 'required|email',
            // ]);
            // dd($data);
        $invoice = Invoice::create($data);
        foreach ($data['invoiceDetails'] as $detail) {
            $invoice->invoiceDetails()->create([
                'name' => $detail['name'],
                'number' => $detail['number'],
                'money' => $detail['money'],
            ]);
        }
        $rsInvoice = static::generateInvoice($invoice->id);
        if($rsInvoice['status']) {
            Notification::make()
                ->title($rsInvoice['message'])
                // ->body('自訂動作已成功執行！')
                ->success()
                ->send();
        }else {
            Notification::make()
                ->title($rsInvoice['message'])
                ->danger()
                ->send();
        }
    }
}
