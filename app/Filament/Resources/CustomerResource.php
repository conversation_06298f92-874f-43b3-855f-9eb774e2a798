<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Customer;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\CustomerResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Resources\RelationManagers\RelationManager;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '客戶管理';
    protected static ?string $navigationGroup = '一般管理';
    protected static ?int $navigationSort = 8;

    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶基本資料')
                        ->schema([
                            Forms\Components\TextInput::make('vendor_id')
                                ->hidden(),
                            Forms\Components\TextInput::make('title')
                                ->label('客戶名稱')
                                ->required(),
                            Forms\Components\TextInput::make('telephone')
                                ->label('電話'),
                            Forms\Components\TextInput::make('full_title')
                                ->label('公司全名')
                                ->columnSpanFull(),
                            Forms\Components\TextInput::make('address')
                                ->label('地址')
                                ->columnSpanFull(),
                            Forms\Components\TextInput::make('contact')
                                ->label('聯絡人'),
                            Forms\Components\TextInput::make('contact_mobile')
                                ->label('聯絡人電話'),
                            Forms\Components\TextInput::make('company_id')
                                ->label('統一編號'),
                            Forms\Components\TextInput::make('contact_email')
                                ->label('Email'),

                        ])
                        ->columns(2),
                ]),
                Group::make([
                    Section::make('客戶基本資料')
                    ->schema([
                        Forms\Components\Toggle::make('status')
                            ->label('客戶狀態'),
                        Forms\Components\MarkdownEditor::make('note')
                            ->label('備註')
                            ->columnSpan(2),
                    ])
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->toggleable()
                    ->label('ID'),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->label('客戶名稱')
                    ->formatStateUsing( function (Model $record){
                        // dd($record);
                        if($record->full_title == '') {
                            // return '<span class="text-success">啟用</span>';
                            return $record->title;
                        }else{
                            return $record->full_title;
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('telephone')
                    ->searchable()
                    ->label('公司電話'),
                Tables\Columns\TextColumn::make('contact')
                    ->searchable()
                    ->label('聯絡人'),
                Tables\Columns\TextColumn::make('company_id')
                    ->searchable()
                    ->label('統一編號'),
                Tables\Columns\TextColumn::make('contact_email')
                    ->searchable()
                    ->label('電子信箱'),
                Tables\Columns\IconColumn::make('status')
                    ->label('狀態')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
            ])
            ->reorderable('sequence')
            ->defaultSort('sequence')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \App\Filament\Resources\CustomerResource\RelationManagers\DepartmentRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
