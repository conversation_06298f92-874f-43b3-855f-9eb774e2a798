<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Models\Car;
use Filament\Forms;
use TextInput\Mask;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Cartype;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use App\Traits\CreateMessageText;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Crypt;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Filament\Tables\Actions\EditAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Session;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TimePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Exports\DispatchExporter;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Forms\Components\DateTimePicker;
use App\Filament\Resources\ZuSanResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ZuSanResource\RelationManagers;
use Illuminate\Support\Facades\Cache;

class ZuSanResource extends Resource
{
    use CreateMessageText;
    protected static ?string $model = Dispatch::class;
    protected static ?string $navigationLabel = '派車單管理';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny MM '.auth()->user()->id); 3 is test james 123
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id == 7){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        $with = ['customer', 'department', 'driver', 'car', 'cartype'];
        $vendorId = Filament::auth()->user()->vendor_id;
        $query = parent::getEloquentQuery()->with($with);
        if ($vendorId != 0) {
            $query->where('vendor_id', $vendorId)->whereNull('deleted_at');
        }
        return $query;
    }
    public static function form(Form $form): Form
    {
        $firstCarTypeId = Cartype::where('vendor_id', Filament::auth()->user()->vendor_id)
                        ->orderBy('sequence', 'asc')
                        ->value('id');
        return $form
            ->schema([
                // <<<--- 加入這個隱藏欄位 --- START --->>>
                Hidden::make('return_query_string')
                ->default(fn () => request()->getQueryString()) // 在表單初始載入時獲取 query string
                ->dehydrated(false), // 通常不需要將這個值存入資料庫
            // <<<--- 加入這個隱藏欄位 --- END --->>>
                Group::make([
                    Section::make('客戶資料')
                        ->columns([
                            'default' => 2,
                            'sm' => 1, // 這裡定義在小尺寸螢幕時顯示一個 column
                            'md' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                            'lg' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                        ])
                        ->schema([
                            Forms\Components\Hidden::make('dispatch_no')
                                ->default(fn () => Dispatch::generateDispatchNumber())
                                ->reactive(),
                            Forms\Components\Hidden::make('vendor_id')
                                ->default(fn () => Filament::auth()->user()->vendor_id)
                                ->reactive(),
                            Forms\Components\Select::make('customer_id')
                                ->relationship('customer', 'title', function ($query) {
                                    return $query->where('vendor_id', Filament::auth()->user()->vendor_id)
                                                ->where('status', 1)
                                                ->orderBy('sequence', 'asc');
                                })
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Hidden::make('vendor_id')
                                            ->default(fn () => Filament::auth()->user()->vendor_id)
                                            ->reactive(),
                                        Forms\Components\TextInput::make('title')
                                            ->label('客戶名稱')
                                            ->required()
                                            ->columnSpanFull(),
                                        Forms\Components\TextInput::make('address')
                                            ->label('地址'),
                                        Forms\Components\TextInput::make('telephone')
                                            ->label('電話'),
                                        Forms\Components\TextInput::make('contact')
                                            ->label('聯絡人'),
                                        Forms\Components\TextInput::make('contact_mobile')
                                            ->label('聯絡人電話'),
                                    ])->columns(2),
                                ])
                                ->live()
                                ->searchable()
                                ->getSearchResultsUsing(fn (Builder $query, string $search) => $query->where('title', 'like', "%{$search}%"))
                                ->preload()
                                ->label('客戶名稱')
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $set('department_id', null);
                                })
                                ->reactive()
                                ->required(),
                            Forms\Components\Select::make('department_id')
                                ->relationship('department', 'name')
                                ->nullable()
                                ->options(
                                    function ($get) {
                                        $customerId = $get('customer_id');
                                        return Department::where('customer_id', $customerId)
                                                            ->where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id');
                                    }
                                )
                                ->label('部門名稱')
                                ->createOptionForm([
                                    Grid::make()->schema([
                                        Forms\Components\Select::make('customer_id')
                                            ->relationship('customer', 'title')
                                            ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('title', 'id'))
                                            ->label('公司名稱')
                                            ->required(),
                                        Forms\Components\TextInput::make('name')
                                            ->label('部門名稱')
                                            ->required(),
                                    ])->columns(3),
                                ])
                                ->createOptionAction(fn ($action) => $action->mutateFormDataUsing(function ($data) {
                                    $data['vendor_id'] = Filament::auth()->user()->vendor_id;
                                    return $data;
                                }))
                                ->live()
                                ->reactive(),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('passenger_name')
                                    ->label('乘客名稱')
                                    ->placeholder('請輸入乘客大名'),
                                Forms\Components\TextInput::make('passenger_mobile')
                                    ->label('乘客電話')
                                    ->placeholder('請輸入乘客電話'),
                            ])
                            ->columns(2)
                            ->default(2),
                        ])->columns(2),
                    Section::make('乘車資料')
                        ->columns([
                            'default' => 2,
                            'sm' => 1, // 這裡定義在小尺寸螢幕時顯示一個 column
                            'md' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                            'lg' => 2, // 這裡定義在大尺寸螢幕時顯示兩個 column
                        ])
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_date')
                                ->placeholder('請選擇日期與時間')
                                ->label('派車日期/時間')
                                ->displayFormat('Y-m-d H:i')
                                ->seconds(false)
                                // ->minutesStep(10)
                                ->firstDayOfWeek(7)
                                ->native(false),
                            Forms\Components\DateTimePicker::make('end_date')
                                ->hidden(),
                            Forms\Components\Select::make('cartype_id')
                                ->label('車輛類型')
                                // >request()
                                ->relationship('cartype', 'name')
                                // ->default(0) 123
                                ->options(Cartype::where('vendor_id', Filament::auth()->user()->vendor_id)->orderBy('sequence', 'asc')->pluck('name', 'id'))
                                ->default($firstCarTypeId)
                                ->live()
                                ->reactive(),
                            Forms\Components\Radio::make('location_type')
                                ->label('派車類別')
                                ->options([
                                    '1' => '接機',
                                    '2' => '送機',
                                    '0' => '單程',
                                    '3' => '包車',
                                    '4' => '短途',
                                ])
                                ->default('1')
                                ->inline()
                                ->live()
                                ->columnSpanFull(),
                            Forms\Components\Select::make('start_location')
                                ->label('上車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '桃園機場',
                                    '5' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '1')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('start_location')
                                ->label('上車地點')
                                ->placeholder('請輸入上車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '2' || $get('location_type') == '3' || $get('location_type') == '4')
                                ->reactive(),
                            Forms\Components\TextInput::make('end_location')
                                ->label('下車地點')
                                ->placeholder('請輸入下車地點')
                                ->visible(fn ($get) => $get('location_type') == '0' || $get('location_type') == '1' || $get('location_type') == '3' || $get('location_type') == '4')
                                ->reactive(),
                            Forms\Components\Select::make('end_location')
                                ->label('下車地點')
                                ->options([
                                    '1' => 'TSA 松山機場',
                                    '2' => '桃園機場 T1',
                                    '3' => '桃園機場 T2',
                                    '4' => '桃園機場',
                                    '5' => '台中機場',
                                ])
                                ->visible(fn ($get) => $get('location_type') == '2')
                                ->live()
                                ->reactive(),
                            Forms\Components\TextInput::make('flight_no')
                                ->label('航班編號')
                                ->placeholder('請輸入航班編號'),
                            Forms\Components\Select::make('pay_type')
                                ->label('付款方式')
                                ->default(0)
                                ->options([
                                    '0' => '月結',
                                    '1' => '現金',
                                    '2' => '匯款',
                                    '3' => '刷卡',
                                ])
                                ->live()
                                ->reactive(),

                            Grid::make()->schema([
                                Forms\Components\TextInput::make('num_of_people')
                                    ->numeric()
                                    ->label('乘車人數')
                                    ->placeholder('請輸入搭乘人數'),
                                Forms\Components\TextInput::make('num_of_bags')
                                    ->numeric()
                                    ->label('行李數')
                                    ->placeholder('請輸入行李數'),
                                Forms\Components\TextInput::make('child_seat')
                                    ->numeric()
                                    ->default(0)
                                    ->label('安全座椅數')
                                    ->placeholder('請輸入安全座椅數'),
                                Forms\Components\TextInput::make('booster_pad')
                                    ->numeric()
                                    ->default(0)
                                    ->label('增高墊數')
                                    ->placeholder('請輸入增高墊數'),
                                Forms\Components\TextInput::make('baby_seat')
                                    ->numeric()
                                    ->default(0)
                                    ->label('嬰兒座椅數')
                                    ->placeholder('請輸入嬰兒座椅數'),
                            ])->columns(4),
                        ])->columns(2),
                    Section::make('派車資料')
                        ->schema([
                            Forms\Components\Select::make('driver_id')
                                ->searchable()
                                ->preload()
                                ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->orderBy('sequence', 'asc')->get()->mapWithKeys(function ($driver) {
                                        $displayName = $driver->join_type == 1 ? "{$driver->name}-[外]" : $driver->name;
                                        return [$driver->id => $displayName];
                                    })->toArray()
                                )
                                // ->options(Driver::where('vendor_id', auth()->user()->vendor_id)
                                //                         ->where('active', 1)
                                //                         ->orderBy('sequence', 'asc')
                                //                         ->pluck('name', 'id')
                                // )
                                ->label('司機')
                                ->live()
                                ->disableOptionWhen(fn (string $value): bool => $value === 2)
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if($state){
                                        $set('status', 1); // 這裡可以根據需求設置下一個狀態
                                        $set('confirm_order_time', null); // 這裡可以根據需求設置下一個狀態
                                    }else{
                                        $set('confirm_order_time', null);
                                        $set('status', 0);
                                    }
                                }),
                            Forms\Components\Select::make('status')
                                ->label('派車狀態')
                                ->default(0)
                                // ->disabled()
                                ->options([
                                    '0' => '派遣中',
                                    '1' => '已指派司機',
                                    '2' => '已完成',
                                    '3' => '已取消',
                                    '4' => '已出發',
                                    '5' => '未出發',
                                    '6' => '已收班',
                                ]),
                            // Grid::make()->schema([
                            //     // Forms\Components\TextInput::make('carno2')
                            //     //     ->label('其他車號')
                            //     //     ->placeholder('請輸入其他車牌號碼'),
                            //     Forms\Components\Select::make('driver_zs_id')
                            //         ->searchable()
                            //         // ->relationship('driver', 'name', function ($query) {
                            //         //     return $query->where('vendor_id', auth()->user()->vendor_id)
                            //         //                 ->where('active', 1)
                            //         //                 ->where('join_type', 1)
                            //         //                 ->orderBy('sequence', 'asc');
                            //         // })
                            //         ->visible(fn ($get) => $get('driver_id') == 44)
                            //         ->options(fn()=>Driver::where('vendor_id', auth()->user()->vendor_id)
                            //                                 ->where('active', 1)
                            //                                 ->where('join_type', 1)
                            //                                 ->orderBy('sequence', 'asc')
                            //                                 ->pluck('name', 'id'))
                            //         ->label('外派司機姓名')
                            //         ->placeholder('請選擇外派司機姓名')
                            //         // ->afterStateUpdated(fn ($set) => $set('driver_zs_id', null))
                            //         ->createOptionForm(function(Form $form){
                            //             return $form // 設定 model
                            //                 ->schema([
                            //                 Grid::make()->schema([
                            //                     Forms\Components\Hidden::make('vendor_id')
                            //                         ->default(fn () => auth()->user()->vendor_id)
                            //                         ->reactive(),
                            //                     Forms\Components\Hidden::make('join_type')
                            //                         ->default(1)
                            //                         ->reactive(),
                            //                     Forms\Components\TextInput::make('name')
                            //                         ->label('司機姓名')
                            //                         ->required(),
                            //                     Forms\Components\TextInput::make('address')
                            //                         ->label('地址'),
                            //                     Forms\Components\TextInput::make('mobile')
                            //                         ->required()
                            //                         ->label('手機'),
                            //                     Forms\Components\TextInput::make('car_brand')
                            //                         ->required()
                            //                         ->label('車輛廠牌'),
                            //                     Forms\Components\TextInput::make('car_model')
                            //                         ->required()
                            //                         ->label('車輛型式'),
                            //                     Forms\Components\TextInput::make('car_license')
                            //                         ->required()
                            //                         ->label('車牌號碼'),
                            //                 ])->columns(2),
                            //                 ]);
                            //         })
                            //         ->createOptionUsing(function (array $data) {
                            //             // dd($data);
                            //             $timestamp = time(); // 取得目前的 Unix timestamp
                            //             $uniqueCode = md5($timestamp);
                            //             $driver = Driver::create([
                            //                 'vendor_id' => auth()->user()->vendor_id,
                            //                 'join_type' => 1,
                            //                 'name' => $data['name'],
                            //                 'address' => $data['address'] ?? null,
                            //                 'mobile' => $data['mobile'] ?? null,
                            //                 'active' => 1,
                            //                 'line_id' => 'line_'.$uniqueCode,
                            //                 // 'car_model' => $data['car_model'] ?? null,
                            //                 // 'car_license' => $data['car_license'] ?? null,
                            //             ]);
                            //             // dd($driver);
                            //             $car = Car::create([
                            //                 'driver_id' => $driver->id,
                            //                 'vendor_id' => auth()->user()->vendor_id,
                            //                 'license' => $data['car_license'] ?? null,
                            //                 'brand' => $data['car_brand'] ?? null,
                            //                 'model' => $data['car_model'] ?? null,
                            //             ]);

                            //             return $driver->id;
                            //         })
                            //         ->live()
                            //         ->reactive(),
                            // ])->columns(2),
                            Grid::make()->schema([
                                Forms\Components\TextInput::make('rental_cost')
                                    ->label('費用')
                                    ->placeholder('請輸入費用')
                                    ->numeric(),

                                Forms\Components\TextInput::make('driver_fee')
                                    ->label('司機費用')
                                    ->placeholder('請輸入司機費用')
                                    ->numeric(),
                                Forms\Components\Select::make('row_color') // Add the new select box here.
                                    ->label('顏色標記')
                                    ->options([
                                        '#FFF' => '無', // Default white
                                        '#F08080' => '淺珊瑚紅', // Light Coral
                                        '#90EE90' => '淺綠色', // Light Green
                                        '#ADD8E6' => '淺藍色', // Light Blue
                                        '#E6E600' => '淺黃色', // Light Yellow
                                        '#FFA07A' => '淺橘色', // Light Salmon
                                        '#EE82EE' => '淺紫色', // Light Violet
                                        '#AFEEEE' => '淺青色', // Light Cyan
                                    ])
                                    ->default('#FFF')
                                    ->live()
                                    ->reactive(),
                                // Forms\Components\TextInput::make('deposit')
                                //     ->label('已付訂金')
                                //     ->placeholder('請輸入訂金費用')
                                //     ->numeric(),
                            ])->columns(3),
                            // Forms\Components\TextInput::make('return_fee')
                            //     ->label('回金')
                            //     ->visible(fn ($get) => $get('pay_type') == '1'),
                            // Forms\Components\TextInput::make('return_boss')
                            //     ->label('車趟來源')
                            //     ->visible(fn ($get) => $get('pay_type') == '1'),
                            Grid::make()->schema([
                                Forms\Components\Toggle::make('return')
                                    ->hiddenOn('edit')
                                    ->default(0)
                                    ->live()
                                    ->reactive()
                                    ->label('回程'),
                                Forms\Components\DateTimePicker::make('return_date')
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程日期/時間')
                                    ->displayFormat('Y-m-d H:i')
                                    ->seconds(false)
                                    ->firstDayOfWeek(7)
                                    ->native(false),
                                Forms\Components\TextInput::make('return_flight_no')
                                    // ->default(0)
                                    ->hiddenOn('edit')
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程航班編號'),
                                Forms\Components\TextInput::make('return_rental_cost')
                                    // ->default(0)
                                    ->hiddenOn('edit')
                                    ->numeric()
                                    ->visible(fn ($get) => $get('return') == 1)
                                    ->label('回程金額'),
                            ])->columns(3),
                        ])->columns(2),
                ]),
                Group::make([
                    Section::make('其他上/下車資訊')
                        ->schema([
                            Repeater::make('pickup_and_dropoff_location')
                                ->label('其他上/下車資訊')
                                ->defaultItems(0)
                                ->schema([
                                    Grid::make(3)->schema([
                                        TextInput::make('name')
                                            ->label('乘客大名'),
                                        TextInput::make('mobile')
                                            ->label('連絡電話'),
                                        // TextInput::make('time')
                                        //     ->label('時間')
                                        //     ->placeholder('請輸入4位數字')
                                        //     ->dehydrateStateUsing(function ($state) {
                                        //         // 格式化为 HH:mm
                                        //         if (preg_match('/^\d{4}$/', $state)) {
                                        //             $hours = substr($state, 0, 2);
                                        //             $minutes = substr($state, 2, 2);
                                        //             return "$hours:$minutes";
                                        //         }
                                        //         return $state;
                                        //     })
                                        //     ->mutateDehydratedStateUsing(function ($state) {
                                        //         // 如果需要重新格式化输入值时调用
                                        //         return $state;
                                        //     })
                                        //     ->helperText('範例:1330 -> 13:30')
                                    ]),
                                    TextInput::make('location')
                                            ->label('上下車地點')
                                            ->columnSpanFull(),
                                ])
                        ]),
                    Section::make('其他資料')
                        ->schema([
                            Group::make([
                                Forms\Components\Textarea::make('note')
                                    ->rows(5)
                                    ->label('備註'),
                                Forms\Components\Textarea::make('driver_note')
                                    ->rows(5)
                                    ->label('其他備註'),
                            ])->columns(2),
                            Grid::make()->schema([
                                Forms\Components\Placeholder::make('image_path')
                                    ->label('簽單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image_path) . "' onclick=window.open('" . asset('storage/' . $record->image_path) . "?".time()."') />");
                                        }
                                        return new HtmlString('');
                                    }),
                                Forms\Components\Placeholder::make('image2_path')
                                    ->label('出租單')
                                    ->hiddenOn('create')
                                    ->content(function ($record): HtmlString {
                                        if(!empty($record->image2_path)){
                                            return new HtmlString("<img src= '" . asset('storage/'.$record->image2_path) . "' onclick=window.open('" . asset('storage/' . $record->image2_path) . "?".time()."')>");
                                        }
                                        return new HtmlString('');
                                    }),

                            ])->columns(2),
                        ])
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('300s')
            ->columns([
                Tables\Columns\TextColumn::make('start_date')
                    ->label('派車日期/時間')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes(function ($record) {
                        $backgroundColor = ($record->row_color == null) ? 'white' : $record->row_color;
                        return ['style' => "background-color: {$backgroundColor};"];
                    })
                    ->getStateUsing( function (Model $record){
                        $startTime = Carbon::parse($record['start_date']);
                        return $startTime->format('Y-m-d').'<br />'.$startTime->format('H:i');
                    })->html(),
                Tables\Columns\TextColumn::make('location_type')
                    ->label('類型')
                    ->searchable()
                    ->formatStateUsing(function (string $state): string {
                        return match ($state) {
                            '1' => '接機',
                            '2' => '送機',
                            '0' => '單程',
                            '3' => '包車',
                            '4' => '短途',
                            default => '未知', // Optional: Handle unknown values
                        };
                    }),
                Tables\Columns\TextColumn::make('cartype_id')
                    ->searchable()
                    ->toggleable()
                    ->label('車型')
                    ->getStateUsing(function (Model $record) {
                        $rs = Cartype::where('id', $record['cartype_id'])->first();
                        if($rs != null){
                            return $rs->short_name;
                        }
                        return '';
                    }),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->toggleable()
                    ->extraAttributes(function ($record) {
                        if($record->status == 5){
                            return ['style' => 'background-color: red;'];
                        }else if($record->status == 4){
                            return ['style' => 'background-color: green;'];
                        }else{
                            return [];
                        }
                    })
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '派遣中';
                        }else if($record['status'] == 1){
                            return '已指派司機';
                        }else if($record['status'] == 2){
                            return '已完成';
                        }else if($record['status'] == 3){
                            return '已取消';
                        }else if($record['status'] == 4){
                            return '已出發';
                        }else if($record['status'] == 5){
                            return '未出發';
                        }else if($record['status'] == 6){
                            return '已收班';
                        }else if($record['status'] == 101){
                            return '異常';
                        }else{
                            return '派遣中';
                        }
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '派遣中' => 'gray',
                        '已指派司機' => 'primary',
                        '已完成' => 'success',
                        '已出發' => 'success',
                        '已取消' => 'danger',
                        '未出發' => 'danger',
                        '已收班' => 'warning',
                        '異常' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable()
                    ->label('客戶名稱')
                    ->copyable()
                    ->copyableState(function (Dispatch $record){
                        $tmpLocationType = '單程';
                        if($record->location_type == 1){
                            $tmpLocationType = '接機';
                        }else if($record->location_type == 2){
                            $tmpLocationType = '送機';
                        }else if($record->location_type == 3){
                            $tmpLocationType = '包車';
                        }else if($record->location_type == 4){
                            $tmpLocationType = '短途';
                        }
                        if($record->driver_id==null){
                            return '請選擇外派司機';
                        }
                        $driver = Driver::find($record->driver_id); // Use find, because it will return null if not exists
                        if ($driver === null) {
                            // No driver found with that ID
                            return '司機資料異常';
                        }
                        if($driver->join_type == 1){
                            $timestamp = time();
                            $mydate = $record->start_date;
                            $passgegerName = $record->passenger_name ??'';
                            $mydate = str_replace(' ', '|', $mydate);
                            return "https://carv2-web.chihlistudio.com/signzs/" . $record->id."?type=".$tmpLocationType."&passenger=".$passgegerName."&date=".$mydate."&v=".$timestamp;
                        }else {
                            // not need to return, just pass.
                            return "";
                        }
                    })
                    // ->copyMessage('已複製')
                    ->copyMessage(function (Dispatch $record) {
                        if($record->driver_id==null){
                            return '請選擇外派司機';
                        }
                        $driver = Driver::find($record->driver_id); // Use find, because it will return null if not exists

                        if ($driver === null) {
                            // No driver found with that ID
                            return '司機資料異常';
                        }
                        if($driver->join_type == 1){
                            return '已複製簽名鏈接';
                        }else{
                            return '請選擇外派司機';
                        }
                    })
                    ->formatStateUsing( function (Model $record){
                        $departmane_name = Department::where('customer_id', $record->customer_id)
                                                    ->where('id', $record->department_id)
                                                    ->pluck('name')->first();
                        return $record->customer->title . '<br />' . $departmane_name;
                    })->html(),
                Tables\Columns\TextColumn::make('passenger_name')
                    ->label('乘客姓名/電話')
                    // Specify the database columns to search within
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        // Use a closure within where() to group the OR conditions correctly
                        return $query->where(function (Builder $subQuery) use ($search) {
                            $subQuery->where('passenger_name', 'like', "%{$search}%")
                                     ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    }, isIndividual: false, isGlobal: true) // Explicitly enable global search for this logic
                    ->sortable() // You might want to sort by name
                    ->toggleable()
                    ->limit(24)
                    ->formatStateUsing( function (Model $record){
                        // Ensure both fields exist before concatenating to avoid errors if one is null
                        $name = $record->passenger_name ?? '';
                        $mobile = $record->passenger_mobile ?? '';
                        return $name . ($name && $mobile ? '<br />' : '') . $mobile;
                    })->html(),
                Tables\Columns\SelectColumn::make('driver_id')
                    ->label('司機')
                    ->toggleable()
                    
                    // ->options(
                    //     Driver::where('vendor_id', auth()->user()->vendor_id)->where('active', 1)->pluck('name', 'id')->toArray()
                    // )
                    ->options(
                        Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->where('active', 1)->orderBy('sequence', 'asc')->get()->mapWithKeys(function ($driver) {
                            $displayName = $driver->join_type == 1 ? "{$driver->name}-[外]" : $driver->name;
                            return [$driver->id => $displayName];
                        })->toArray()
                    )
                    ->afterStateUpdated(function ($state, $record) {
                        // Log::info('ZuSanResource driver_id afterStateUpdated: Start', ['record_id' => $record->id, 'new_driver_id' => $state]);
                        // dd($state);
                        if ($state) {
                            $record->status = 1;
                            $record->confirm_order_time = null;
                            Log::info('ZuSanResource driver_id afterStateUpdated: State is true, updating record status and confirm_order_time.', ['record_id' => $record->id]);
                            $record->save();
                            $driver = Driver::where('id', $record->driver_id)->first();
                            $car = Car::where('driver_id', $record->driver_id)->first();
                            $customer = Customer::where('id', $record->customer_id)->first();
                            if($record->department_id != null){
                                $department = Department::where('id', $record->department_id)->first();
                                $record->department_name = $department->name;
                                Log::info('ZuSanResource driver_id afterStateUpdated: Fetched related models.', [
                                'record_id' => $record->id,
                                'driver_exists' => !is_null($driver),
                                'car_exists' => !is_null($car),
                                'customer_exists' => !is_null($customer),
                                'department_id' => $record->department_id,
                                'department_name_set' => isset($record->department_name) ? $record->department_name : 'Not Set'
                            ]);
                            }
                            $record->customer_title = $customer->title;
                            $record->driver_name = $driver->name;
                            $record->car_license = $car->license;
                            $record->car = $car;
                            if($record->signature_path != null){
                                $signature = storage_path('app/public/' .$record->signature_path);
                            }else{
                                $signature = null;
                            Log::info('ZuSanResource driver_id afterStateUpdated: Attempting to generate images.', ['record_id' => $record->id, 'signature_path_for_v3' => $signature]);
                            }
                            // $rs = Dispatch::generateImage($record);
                            // 產生派車單
                            $rs = Dispatch::generateImageV3('dispatch1',$record, $signature);
                            $rs2 = Dispatch::generateImageV3('dispatch2',$record);
                            Log::info('ZuSanResource driver_id afterStateUpdated: Image generation results.', ['record_id' => $record->id, 'rs_dispatch1' => $rs, 'rs_dispatch2' => $rs2]);
                            if(!$rs){
                                Notification::make()
                                    ->title('簽單圖片生成失敗!請與車行洽詢!')
                                    ->danger()
                                    ->send();
                                    return;
                            }
                            if(!$rs2){
                                Notification::make()
                                    ->title('出租單圖片生成失敗!請與車行洽詢!')
                                    ->danger()
                                    ->send();
                            Log::info('ZuSanResource driver_id afterStateUpdated: Before final update.', [
                                'record_id' => $record->id,
                                'update_data' => [
                                    'image_path' => $rs ?? 'not set', // Use null coalescing for safety
                                    'image2_path' => $rs2 ?? 'not set', // Use null coalescing for safety
                                    'confirm_order_time' => null
                                ]
                            ]);
                            Log::info('ZuSanResource driver_id afterStateUpdated: State is false (driver deselected).', ['record_id' => $record->id]);
                                    return;
                            }
                            Dispatch::where('id', $record->id)
                                ->update([
                                    // 'signature_file' => 'signatures/' . $fileName,
                                    'image_path' => $rs,
                                    'image2_path' => $rs2,
                                    'confirm_order_time' => null,
                                ]);
                        }else{
                            $record->status = 0;
                            $record->confirm_order_time = null;
                            $record->save();
                        }
                    })
                    ->extraAttributes([
                        'style' => 'width: 8rem !important; min-width: 7rem !important; max-width: 8rem !important;',
                    ]),
                Tables\Columns\TextColumn::make('flight_no')
                    ->searchable()
                    ->toggleable()
                    ->label('航班'),


                Tables\Columns\TextColumn::make('start_location')
                    ->label('上下車地點')
                    ->toggleable()
                    ->searchable()
                    // ->limit(30)
                    ->getStateUsing( function (Model $record){
                        $tmpdata = '';
                        if($record['start_location'] == 1 || $record['end_location'] == 1){
                            $tmpdata = 'TSA 松山機場';
                        }else if($record['start_location'] == 2 || $record['end_location'] == 2){
                            $tmpdata = '桃園機場 T1';
                        }else if($record['start_location'] == 3 || $record['end_location'] == 3){
                            $tmpdata = '桃園機場 T2';
                        }else if($record['start_location'] == 4 || $record['end_location'] == 4){
                            $tmpdata = '桃園機場';
                        }else if($record['start_location'] == 5 || $record['end_location'] == 5){
                            $tmpdata = '台中機場';
                        }else{
                            $tmpdata = $record['start_location'];
                        }
                        $redDot = (!empty($record->pickup_and_dropoff_location) && $record->pickup_and_dropoff_location !== '[]')
            ? '<span style="color: red; margin-left: 4px;">●</span>'
            : '';
                        if($record['location_type'] == 1){
                            return $redDot.$tmpdata.'<br />'.$record['end_location'];
                        }else if($record['location_type'] == 2){
                            return $redDot.$record['start_location'].'<br />'.$tmpdata;
                        }else{
                            return $redDot.$record['start_location'].'<br />'.$record['end_location'];
                        }
                    })->html(),
                Tables\Columns\TextColumn::make('up_time')
                    ->label('出發/上/下車時間')
                    ->toggleable()
                    ->formatStateUsing(function (Model $record) {

                        $startTime = $record->car_start_time ? Carbon::parse($record->car_start_time)->format('H:i') : null;
                        $upTime = $record->up_time ? Carbon::parse($record->up_time)->format('H:i') : null;
                        $downTime = $record->finish_time ? Carbon::parse($record->finish_time)->format('H:i') : null;

                        if ($startTime || $upTime || $downTime) {
                            return ($startTime ? '出: ' . $startTime : '') . '<br />' .
                                   ($upTime ? '上: ' . $upTime : '') . '<br />' .
                                   ($downTime ? '下: ' . $downTime : '');
                        }

                        // Return an empty string if no values are present
                        return '';
                    })->html(),
                Tables\Columns\TextColumn::make('pay_type')
                    ->toggleable()
                    ->label('付款方式')->formatStateUsing(function ($state) {
                        switch ($state) {
                            case 0:
                                return '月結';
                            case 1:
                                return '現金';
                            case 2:
                                return '匯款';
                            case 3:
                                return '刷卡';
                            default:
                                return '月結';
                        }
                    }),
                Tables\Columns\TextInputColumn::make('rental_cost')
                    ->toggleable()
                    ->label('月結/現金')
                    ->extraAttributes([
                        'style' => 'width: 6rem !important; min-width: 4rem !important; max-width: 6rem !important;',
                    ]),
                Tables\Columns\TextInputColumn::make('driver_fee')
                    ->toggleable()
                    ->label('司機車資')
                    ->extraAttributes([
                        'style' => 'width: 6rem !important; min-width: 4rem !important; max-width: 6rem !important;',
                    ]),

            ])
            ->striped()
            ->defaultPaginationPageOption(25)
            // ->deferLoading()
            ->persistFiltersInSession(true)
            ->defaultSort('start_date', 'asc')
            ->recordUrl(function (Model $record, Table $table): string {
                // 1. Get the base URL for the edit page
                $baseUrl = static::getUrl('edit', ['record' => $record]);

                // 2. Get the Livewire component instance for the table
                $livewire = $table->getLivewire();

                // 3. Extract relevant state properties from the Livewire component
                $queryParams = [];

                // Preserve activeTab if it exists and is set
                if (property_exists($livewire, 'activeTab') && !is_null($livewire->activeTab)) {
                    $queryParams['activeTab'] = $livewire->activeTab;
                }

                // Preserve table search query
                if (property_exists($livewire, 'tableSearch') && !empty($livewire->tableSearch)) {
                    $queryParams['tableSearch'] = $livewire->tableSearch;
                }

                // Preserve column search queries (if you use them)
                if (property_exists($livewire, 'tableColumnSearchQueries') && is_array($livewire->tableColumnSearchQueries) && !empty($livewire->tableColumnSearchQueries)) {
                    $queryParams['tableColumnSearchQueries'] = $livewire->tableColumnSearchQueries;
                }

                // Preserve filters
                if (property_exists($livewire, 'tableFilters') && is_array($livewire->tableFilters)) {
                    // Filter out null/empty values which signify no filter applied
                    $activeFilters = array_filter($livewire->tableFilters, function ($value) {
                        // Adjust this condition if your filters can have '0' or false as valid non-empty values
                        return !is_null($value) && $value !== '';
                    });
                    if (!empty($activeFilters)) {
                        $queryParams['tableFilters'] = $activeFilters;
                    }
                }

                // Preserve sorting
                if (property_exists($livewire, 'tableSortColumn') && $livewire->tableSortColumn) {
                    $queryParams['tableSortColumn'] = $livewire->tableSortColumn;
                }
                if (property_exists($livewire, 'tableSortDirection') && $livewire->tableSortDirection) {
                    $queryParams['tableSortDirection'] = $livewire->tableSortDirection;
                }

                // Preserve pagination page (optional, but good practice)
                if (property_exists($livewire, 'tableRecordsPerPage') && $livewire->getTableRecordsPerPage() !== $livewire->getTable()->getDefaultPaginationPageOption()) {
                    $queryParams['tableRecordsPerPage'] = $livewire->getTableRecordsPerPage();
                }
                if (property_exists($livewire, 'paginators') && isset($livewire->paginators['page']) && $livewire->paginators['page'] > 1) {
                    $queryParams['page'] = $livewire->paginators['page'];
                }
                // 4. Build the query string if there are any parameters
                $queryString = Arr::query($queryParams); // Use Arr::query for robust query string building
                // 5. Combine base URL and query string
                return $queryString ? ($baseUrl . '?' . $queryString) : $baseUrl;
            })
            // --- END of recordUrl modification ---

            ->filters([
                Filter::make('customer_id')
                    ->label('查詢')
                    ->form([
                        Select::make('customer_id')
                            ->label('公司名稱')
                            ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)->orderBy('sequence')->pluck('title', 'id'))
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('department_id', null);
                            }),
                        Select::make('department_id')
                            ->label('部門')
                            ->options(function ($get) {
                                return Department::where('customer_id', $get('customer_id'))
                                    ->where('vendor_id', Filament::auth()->user()->vendor_id)
                                    ->pluck('name', 'id');
                            }),
                            // ->options(Department::where('vendor_id', auth()->user()->vendor_id)->pluck('name', 'id'))
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // dd($data);
                        return $query
                            ->when($data['customer_id'], fn ($query, $customer_id) => $query->where('customer_id', $customer_id)->where('vendor_id', Filament::auth()->user()->vendor_id))
                            ->when($data['department_id'], fn ($query, $department_id) => $query->where('department_id', $department_id))->where('vendor_id', Filament::auth()->user()->vendor_id);
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['customer_id']) {
                            $customer_name = Customer::find($data['customer_id'])->title;
                            $indicators['customer_id'] = '公司: ' . $customer_name;
                        }
                        if ($data['department_id']) {
                            $department_name = Department::find($data['department_id'])->name;
                            $indicators['department_id'] = '部門: ' . $department_name;
                        }
                        return $indicators;
                    })
                    ,
                    SelectFilter::make('driver_id')
                    ->label('司機')
                    ->options(
                        Driver::where('vendor_id', Filament::auth()->user()->vendor_id)
                            ->where('active', 1)
                            ->get()
                            ->mapWithKeys(function ($driver) {
                                $displayName = $driver->join_type == 1 ? "{$driver->name}-[外]" : $driver->name;
                                return [$driver->id => $displayName];
                            })->toArray()
                    )
                    ->searchable()
                    ->preload(),
                Filter::make('start_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('start_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('start_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
                SelectFilter::make('status')
                    ->label('狀態')
                    ->searchable()
                    ->options([
                        '0' => '派遣中',
                        '1' => '已指派司機',
                        '2' => '已完成',
                        '3' => '已取消',
                        '4' => '已出發',
                        '5' => '未出發',
                    ]),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                //Access to filter data
                $request = Request::instance();
                $filters = $request->query();

                //You can now access the filter values directly.
                //Example
                $startDate = $filters['tableFilters']['start_date']['start_date'] ?? null;
                $endDate = $filters['tableFilters']['start_date']['end_date'] ?? null;
                $customerId = $filters['tableFilters']['customer_id']['customer_id'] ?? null;
                $departmentId = $filters['tableFilters']['customer_id']['department_id'] ?? null;

                //You can also store it in Session manually if needed.
                Session::put('my_custom_filters', $filters);
                //or get it in Session
                $myFilters = Session::get('my_custom_filters');
                foreach ($filters as $key => $value) {
                    if (strpos($key, 'tableFilters') === 0) {
                        $query->when($value, function ($query, $value) use ($key) {
                            switch ($key) {
                                case 'tableFilters.status.0':
                                    $query->where('status', $value);
                                    break;
                                case 'tableFilters.location_type.0':
                                    $query->where('location_type', $value);
                                    break;
                                case 'tableFilters.driver_id.0':
                                    $query->where('driver_id', $value);
                                    break;
                                case 'tableFilters.customer_id.0':
                                    $query->where('customer_id', $value);
                                    break;
                                    //date range
                                case 'tableFilters.start_date.start_date':
                                    $query->whereDate('start_date', '>=', $value);
                                    break;
                                case 'tableFilters.start_date.end_date':
                                    $query->whereDate('start_date', '<=', $value);
                                    break;
                                case 'tableFilters.customer_id.customer_id':
                                    $query->where('customer_id', $value);
                                    break;
                                case 'tableFilters.customer_id.department_id':
                                    $query->where('department_id', $value);
                                    break;
                            }
                        });
                    }
                }
            })
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('custom-copy')
                        ->label('拷貝文字')
                        ->view('filament.actions.copy-action'),

                    Tables\Actions\EditAction::make()
                        ->after(function () {
                            if (session()->has('activeTab')) {
                                static::setActiveTab(session('activeTab'));
                            }
                        }),
                    Tables\Actions\ViewAction::make(),
                    // Tables\Actions\Action::make('copy-text')
                    //         ->label('拷貝文字')
                    //         ->icon('heroicon-o-document-duplicate')
                    //         ->action(function ($livewire, $record) {
                    //             $textToCopy = $record->copy_text_1 ?? '';
                    //             $tooltipStr = empty($textToCopy) ? '目前無文字內容' : '已拷貝到剪貼簿中!';
                    //             $livewire->js(
                    //                 'window.navigator.clipboard.writeText(' . json_encode($textToCopy) . ');
                    //                 $tooltip(' . json_encode(__($tooltipStr)) . ', { timeout: 1500 });'
                    //             );
                    //         }),
                    Tables\Actions\Action::make('update-start')
                            ->label('移除出發')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['car_start_time' => null, 'status' => 1]);
                                Notification::make()
                                    ->title('已移除出發時間')
                                    ->success()
                                    ->send();
                            }),
                    Tables\Actions\Action::make('update-status')
                            ->label('移除客上')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['up_time' => null, 'status' => 1]);
                                Notification::make()
                                    ->title('已移除客上時間')
                                    ->success()
                                    ->send();
                            }),
                    Tables\Actions\Action::make('update-signature')
                            ->label('移除簽名檔')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['signature_path' => null, 'down_time' => null, 'status' => 1]);
                                Notification::make()
                                    ->title('已移除簽名檔')
                                    ->success()
                                    ->send();
                            }),
                    Tables\Actions\Action::make('update-finish')
                            ->label('移除客下')
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record) {
                                Dispatch::find($record->id)->update(['finish_time' => null, 'status' => 1]);
                                Notification::make()
                                    ->title('已移除簽客下時間')
                                    ->success()
                                    ->send();
                            }),
                    Tables\Actions\DeleteAction::make()
                        ->label('刪除')
                        ->icon('heroicon-o-trash')
                        ->action(function ($record) {
                            Dispatch::find($record->id)->update(['deleted_at' => now(), 'status' => 11]);
                        }),
                ])
                ->label('')
                ->icon('heroicon-o-cog'),
            ])
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\ExportBulkAction::make()
            //             ->exporter(DispatchExporter::class)
            //             ->label('匯出'),
            //         Tables\Actions\DeleteBulkAction::make(),
            //     ]),
            // ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListZuSans::route('/'),
            'create' => Pages\CreateZuSan::route('/create'),
            'edit' => Pages\EditZuSan::route('/{record}/edit'),
            // 'edit' => Pages\EditZuSan::route('/{record}/edit'. (Session::has('activeTab') ? '?activeTab=' . Session::get('activeTab') : '')),
        ];
    }
}
