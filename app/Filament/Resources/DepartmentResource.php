<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Customer;
use Filament\Forms\Form;
use App\Models\Department;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DepartmentResource\Pages;
use App\Filament\Resources\DepartmentResource\RelationManagers;

class DepartmentResource extends Resource
{
    protected static ?string $model = Department::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '部門管理';
    protected static ?string $navigationGroup = '一般管理';
    protected static ?int $navigationSort = 9;

    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('customer_id')
                    ->relationship('customer', 'title')
                    ->options(function ($get) {
                        return Customer::where('vendor_id', auth()->user()->vendor_id)->pluck('title', 'id');
                    })
                    ->label('客戶名稱')
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->label('部門名稱')
                    ->required(),
                Forms\Components\TextInput::make('vendor_id')
                    ->hidden()
                    ->default(auth()->user()->vendor_id)
                    ->required(),
            ]);
    }
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['vendor_id'] = auth()->user()->vendor_id;
        dd($data);
        return $data;
        // Department::create($data);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->searchable()
                    ->label('ID'),
                Tables\Columns\TextColumn::make('customer.title')
                    ->searchable()
                    ->label('客戶名稱'),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->label('部門名稱'),
            ])
            ->filters([
                SelectFilter::make('customer_id')
                    ->label('客戶名稱')
                    ->options(Customer::where('vendor_id', auth()->user()->vendor_id)->pluck('title', 'id'))
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                   ->requiresConfirmation()
                   ->modalHeading('刪除部門'),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // RelationManagers\CustomersRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDepartments::route('/'),
            'create' => Pages\CreateDepartment::route('/create'),
            'edit' => Pages\EditDepartment::route('/{record}/edit'),
        ];
    }
}
