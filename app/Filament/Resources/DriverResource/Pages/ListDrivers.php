<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Auth;
use App\Models\User;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\DriverResource;

class ListDrivers extends ListRecords
{
    protected static string $resource = DriverResource::class;
    protected static ?string $title = '司機列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('新增司機')
                ->visible(fn () => Filament::auth()->user()->id == 1 || Filament::auth()->user()->id == 7),
        ];
    }
}
