<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Actions;
use App\Models\Driver;

use App\Models\Dispatch;
use App\Traits\CreateImage;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\DriverResource;

class EditDriver extends EditRecord
{
    use CreateImage;
    protected static string $resource = DriverResource::class;
    protected static ?string $title = '編輯司機';

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function handleRecordUpdate(Model $record, array $data):Driver
    {
        // dd($data);
        $driver = Driver::find($record->id);
        $driver->name = $data['name'];
        $driver->mobile = $data['mobile'];
        $driver->sex = $data['sex'];
        $driver->person_id = $data['person_id'];
        $driver->address = $data['address'];
        $driver->active = $data['active'];
        $driver->note = $data['note'];
        $driver->driver_image = $data['driver_image'];
        $driver->original_filename = $data['original_filename'];

        if($record->vendor_id == 4){
            $printResult = static::generateImage('driver_info', $driver);
            $driver->driver_info_image_path = $printResult;
        }
        // dd($driver);
        $driver->save();
        return $driver;
    }
}
