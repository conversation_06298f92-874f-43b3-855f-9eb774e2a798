<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Actions;
use App\Models\Driver;
use App\Filament\Resources\DriverResource;
use Filament\Resources\Pages\CreateRecord;

class CreateDriver extends CreateRecord
{
    protected static ?string $title = '建立司機';
    protected static string $resource = DriverResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public static function canCreateAnother(): bool
    {
        // 返回 false 以隐藏 "建立後再建一筆" 按钮
        return false;
    }
    protected function handleRecordCreation(array $data): Driver
    {
        $timestamp = time(); // 取得目前的 Unix timestamp
        $uniqueCode = md5($timestamp); // 產生唯一的 MD5 雜湊值
        $data['join_type'] = 1;
        $data['line_id'] = 'line_'.$uniqueCode;
        $data['active'] = 1;
        return Driver::create($data);
    }
}
