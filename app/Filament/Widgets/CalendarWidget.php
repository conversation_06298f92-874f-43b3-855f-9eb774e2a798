<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\DispatchResource;
use App\Filament\Resources\Dispatchv2Resource;
use App\Filament\Resources\HongkaiResource;
use App\Filament\Resources\SinShengResource;
use App\Filament\Resources\YuKunResource;
use App\Filament\Resources\ZuSanResource;
use App\Models\Dispatch;
use Filament\Facades\Filament;
use Filament\Widgets\Widget;
use Saade\FilamentFullCalendar\Widgets\FullCalendarWidget;

class CalendarWidget extends FullCalendarWidget
{
    protected static ?int $sort = 2;

    // protected static string $view = 'filament.widgets.calendar-widget';
    public function fetchEvents(array $fetchInfo): array
    {
        // dd($fetchInfo);
        return Dispatch::query()
            ->with('driver')
            ->where('vendor_id', Filament::auth()->user()->vendor_id)
            ->where('start_date', '>=', $fetchInfo['start'])
            ->where('end_date', '<=', $fetchInfo['end'])
            ->get()
            ->map(
                function (Dispatch $dispatch) {
                    if ($dispatch->driver_id == null) {
                        $mytitle = '(' . $dispatch->num_of_people . '*' . $dispatch->num_of_bags . ') ' . $dispatch->route;
                        $tmpColor = 'red';
                    } else {
                        $mytitle = '(' . $dispatch->num_of_people . '*' . $dispatch->num_of_bags . ') ' . $dispatch->route . ' (' . (empty($dispatch->driver->name) ? '' : $dispatch->driver->name) . ')';
                        $tmpColor = 'green';
                    }
                    if ($dispatch->vendor_id == 1) {
                        $myUrl = DispatchResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]);
                    } else if ($dispatch->vendor_id == 3) {
                        $myUrl = HongkaiResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]);
                    } else if ($dispatch->vendor_id == 4 || $dispatch->vendor_id == 5) {
                        $myUrl = Dispatchv2Resource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]);
                    } else if ($dispatch->vendor_id == 6) {
                        $myUrl = ZuSanResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]);
                    } else if ($dispatch->vendor_id == 9) {
                        $myUrl = SinShengResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]);
                    } else {
                        $myUrl = DispatchResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]);
                    }
                    return [
                        'id' => $dispatch->id,
                        'title' => $mytitle,
                        'start' => $dispatch->start_date,
                        'end' => $dispatch->end_date,
                        'color' => $tmpColor,
                        'url' => $myUrl,
                        // 'shouldOpenUrlInNewTab' => true
                    ];
                }
            )
            ->toArray();
        // $myUrl = match ($dispatch->vendor_id) {
        //     1 => DispatchResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]),
        //     3 => HongkaiResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]),
        //     4, 5 => Dispatchv2Resource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]),
        //     6 => ZuSanResource::getUrl(name: 'edit', parameters: ['record' => $dispatch->id]),
        //     default => DispatchResource::getUrl(name: 'edit', parameters: ['record'=> $dispatch->id]),
    }

    protected function getOptions(): array
    {
        return [
            'initialView' => 'dayGridMonth',
            'events' => $this->getEvents(),  // 假设你有一个方法返回事件数据
            'eventContent' => function ($eventInfo) {
                // 自定义事件的内容，将时间和标题交换位置
                return "
                    <div class='fc-event-time'>{$eventInfo['event']['title']}</div>
                    <div class='fc-event-title'>{$eventInfo['timeText']}</div>
                ";
            },
            'eventTimeFormat' => [
                'hour' => '2-digit',
                'minute' => '2-digit',
                'meridiem' => false,
                'hour12' => false,  // 使用 24 小時制
            ],
        ];
    }

    public function eventDidMount(): string
    {
        return <<<JS
                function({ event, timeText, isStart, isEnd, isMirror, isPast, isFuture, isToday, el, view }){
                    el.setAttribute("x-tooltip", "tooltip");
                    el.setAttribute("x-data", "{ tooltip: '"+event.title+"' }");
                }
            JS;
    }
}
