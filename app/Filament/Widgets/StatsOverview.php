<?php

namespace App\Filament\Widgets;

use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class StatsOverview extends BaseWidget
{
    public ?Driver $record;
    protected static ?int $sort = 1;
    protected function getStats(): array
    {
        if(auth()->user()->vendor_id==0){
            $totalDriver = Driver::query()->count();
            $successActive = Driver::where('active', '=', 1)->count();
            $totalCustomer = Customer::query()->count();
            $totalDispatch = Dispatch::query()->count();
        }else{
            $totalDriver =Driver::where('vendor_id', '=', auth()->user()->vendor_id)->count();
            $successActive = Driver::where('active', '=', 1)
                ->where('vendor_id', '=', auth()->user()->vendor_id)->count();
            $totalDispatch = Dispatch::where('vendor_id', '=', auth()->user()->vendor_id)->count();
            $totalCustomer = Customer::where('vendor_id', '=', auth()->user()->vendor_id)->count();
        }
        // dd($totalDriver);
        return [
            Stat::make('註冊人數', $totalDriver)
                ->icon('heroicon-o-users')
                ->color('primary')
                ->chart([1,2,3,4,5,6,7])
                ->description('駕駛註冊總人數'),
            Stat::make('審核通過', $successActive)
                ->icon('heroicon-o-users')
                ->color('success')
                ->chart([1,2,3,4,5,6,7])
                ->description('審核成功駕駛總數'),
            Stat::make('客戶總數', $totalCustomer)
                ->color('success')
                ->chart([1,2,3,4,5,6,7])
                ->description('客戶總數量'),
            Stat::make('派車單數', $totalDispatch)
                ->icon('heroicon-o-truck')
                ->color('info')
                ->chart([1,5,2,3,4,6,7])
                ->description('所有派車單總數'),
        ];
    }
}
