<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;

class PageMarqueeWidget extends Widget
{
    protected static string $view = 'filament.widgets.custom-marquee';

    public string $message = '這裡是預設的跑馬燈訊息！'; // Default message

    // You can add a static make method if you prefer to instantiate with a custom message easily,
    // but for this simple case, setting the public property after instantiation is also fine.
    public static function canView(): bool
    {
        // Return false to prevent this widget from being displayed on the dashboard.
        return false;
    }
}

