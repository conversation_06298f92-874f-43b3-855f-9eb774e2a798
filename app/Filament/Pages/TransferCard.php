<?php

namespace App\Filament\Pages;

use Carbon\Carbon;
use Filament\Forms;
use League\Csv\Reader;
use App\Models\Dispatch;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Tables\Table;
use App\Models\TempCardData;
use Forms\Components\Button;
use Livewire\WithFileUploads;
use Filament\Tables\Actions\Action;
use App\Imports\TransferCardImport;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Concerns\InteractsWithTable;

class TransferCard extends Page implements Forms\Contracts\HasForms, HasTable
{
    use Forms\Concerns\InteractsWithForms;
    use InteractsWithTable;
    use WithFileUploads;
    public $file;
    public $data = [];
    public $isFileUploaded = false;
    public $activeTab = 'check'; // Default active tab
    public $isLoading = false; // Loading state
    public $checkFilter = 99; // 99 = all, 0 = no match, 1 = match
    public $dispatchCheckFilter = 99; // 99 = all, 0 = no match, 1 = match
    public $tempCardRecords = []; // TempCardData records
    public $dispatchRecords = []; // Dispatch records
    protected static ?string $navigationGroup = '一般管理';
    protected static ?int $navigationSort = 21;
    protected static ?string $navigationLabel = '信用卡帳單檢核';
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.transfer-card';
    // public function canViewAny(): bool
    // {
    //     Log::info('canViewAny tsfc --'.auth()->user()->id);
    //     // 只有 ID 為 1 的使用者可以查看此頁面
    //     return auth()->user()->id === 1;
    // }
    public static function canAccess(): bool
    {
        return Auth::id()==1 || Auth::id()==4;
    }
    // public static function canViewAny($user): bool
    // {
    //     Log::info('canViewAny MM '.auth()->user()->id);
    //     // if(auth()->user()->id==1){
    //     //     return true;
    //     // }
    //     // return false;
    //     return $user->id == 1;
    // }
    // public static function canView(): bool
    // {
    //     Log::info('canViewAny  tsfc --'.auth()->user()->id);
    //     // 只有 ID 為 1 的使用者可以查看此頁面
    //     return Auth::user()->id === 1;
    // }
    public function mount()
    {
        // Log::info('canViewAny tsfc --'.auth()->user()->id);
        $this->file = null;
        $this->isFileUploaded = false;

        // Initialize tempCardRecords and dispatchRecords
        $this->tempCardRecords = collect();
        $this->dispatchRecords = collect();

        // Load data
        $this->loadData();
    }

    // 計算筆數的屬性
    public $totalTempCardCount = 0;
    public $matchedTempCardCount = 0;
    public $unmatchedTempCardCount = 0;
    public $totalDispatchCount = 0;
    public $matchedDispatchCount = 0;
    public $unmatchedDispatchCount = 0;
public array $dispatchToMatchedTempCardMap = []; // For N+1 optimization

    public function loadData()
    {
        // Set loading state to true
        $this->isLoading = true;

        // Get the selected month from the form
        $selectedMonth = $this->data['year'] ?? Carbon::now()->format('Y-m');
        $startOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth();

        // Load TempCardData records with filters
        $tempCardQuery = TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth);

        // Apply check filter
        if ($this->checkFilter !== 99) {
            $tempCardQuery->where('check', $this->checkFilter);
        }

        $this->tempCardRecords = $tempCardQuery->get();

        // Load Dispatch records with filter
        $dispatchQuery = Dispatch::where('vendor_id', 3)
            ->whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth);

        // Apply dispatch check filter
        if ($this->dispatchCheckFilter !== 99) {
            if ($this->dispatchCheckFilter === 1) {
                // Records that have matching TempCardData
                $dispatchQuery->whereIn('id', function ($query) {
                    $query->select('data_id2')
                        ->from('temp_card_data')
                        ->where('check2', 1)
                        ->whereNotNull('data_id2');
                });
            } else {
                // Records that don't have matching TempCardData
                $dispatchQuery->whereNotIn('id', function ($query) {
                    $query->select('data_id2')
                        ->from('temp_card_data')
                        ->where('check2', 1)
                        ->whereNotNull('data_id2');
                });
            }
        }

        $this->dispatchRecords = $dispatchQuery->get();

        // 計算 TempCardData 筆數
        $selectedMonth = $this->data['year'] ?? Carbon::now()->format('Y-m');
        $startOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth();

        $this->totalTempCardCount = TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->count();

        $this->matchedTempCardCount = TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->where('check', 1)
            ->count();

        $this->unmatchedTempCardCount = TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->where('check', 0)
            ->count();

        // 計算 Dispatch 筆數
        $this->totalDispatchCount = Dispatch::where('vendor_id', 3)
            ->whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->count();

        $this->matchedDispatchCount = Dispatch::where('vendor_id', 3)
            ->whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->whereIn('id', function ($query) {
                $query->select('data_id2')
                    ->from('temp_card_data')
                    ->where('check2', 1)
                    ->whereNotNull('data_id2');
            })
            ->count();

        $this->unmatchedDispatchCount = Dispatch::where('vendor_id', 3)
            ->whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->whereNotIn('id', function ($query) {
                $query->select('data_id2')
                    ->from('temp_card_data')
                    ->where('check2', 1)
                    ->whereNotNull('data_id2');
            })
            ->count();

        // Set loading state to false
        // N+1 Optimization: Preload TempCardData matches for the loaded Dispatches
        $dispatchIds = collect($this->dispatchRecords)->pluck('id')->filter()->all();
        $this->dispatchToMatchedTempCardMap = []; // Reset map
        if (!empty($dispatchIds)) {
            // Fetch TempCardData records that are reverse-matched (check2=true)
            // to any of the loaded dispatch records.
            // We only need the TempCardData's ID and the dispatch_id it's linked to (data_id2).
            $matchedTempCardsInfo = TempCardData::whereIn('data_id2', $dispatchIds)
                ->where('check2', true)
                ->select('id', 'data_id2') // Select only necessary columns
                ->get()
                ->keyBy('data_id2'); // Key by data_id2 (which is the dispatch_id)

            $this->dispatchToMatchedTempCardMap = $matchedTempCardsInfo->all();
        }
        // Removed duplicated N+1 logic block and comments
        $this->isLoading = false;
    }
    // protected function getHeaderActions(): array
    // {
    //     return [
    //         // Actions\CreateAction::make()->label('新增派車單'),
    //         Action::make('importTransferCard')
    //             ->label('匯入信用卡對帳單')
    //             ->form([
    //                 Select::make('year')
    //                     ->label('選擇年份')
    //                     ->options($this->getMonthOptions())
    //                     ->required(),
    //                 FileUpload::make('attachments')
    //                 ->label('選擇檔案'),
    //             ])
    //             ->action(function (array $data) {
    //                 // $data['vendor_id'] = auth()->user()->vendor_id;
    //                 // $data['status'] = 0;
    //                 // $data['start_date'] = now();
    //                 $file = public_path('storage/'.$data['attachments']);
    //                 $year = $data['year'];
    //                 Excel::import(new TransferCardImport(), $file);
    //                 Notification::make()
    //                     ->title('匯入完成')
    //                     ->success()
    //                     ->send();
    //                 $this->isFileUploaded = true;
    //                 Log::info('匯入完成', ['isFileUploaded' => $this->isFileUploaded]);
    //                 // return redirect('/admin/transfer-card');
    //                 $this->emit('fileUploaded');
    //                 // Dispatch::create($data);
    //             }),
    //     ];
    // }


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('year')
                    ->label('選擇年份')
                    ->options($this->getMonthOptions())
                    ->required(),
                FileUpload::make('attachments')
                    ->label('選擇檔案')
                    ->disk('public')
                    ->directory('csv_file')
                    ->required(),
            ])->statePath('data');
    }
    public function table(Table $table): Table
    {
        // Get the selected month from the form
        $selectedMonth = $this->data['year'] ?? Carbon::now()->format('Y-m');
        $startOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth();

        return $table
            ->query(
                TempCardData::query()
                    ->whereDate('start_date', '>=', $startOfMonth)
                    ->whereDate('start_date', '<=', $endOfMonth)
            )
            ->columns([
                TextColumn::make('id')->label('ID'),
                TextColumn::make('mydate')->label('日期'),
                TextColumn::make('mytime')->label('時間'),
                TextColumn::make('driver')->label('司機'),
                TextColumn::make('route')->label('路程'),
                TextColumn::make('cost')->label('金額'),
                IconColumn::make('check')
                    ->label('正向檢查')
                    ->boolean(),
                IconColumn::make('check2')
                    ->label('反向檢查')
                    ->boolean(),
                TextColumn::make('data_id')->label('派車單ID'),
            ])
            ->filters([
                Filter::make('check')
                    ->query(function ($query, $data) {
                        $check = (int) ($data['check'] ?? 99); // 強制轉換為數字，避免字串問題
                        if ($check !== 99) {
                            $query->where('check', $check);
                        }
                    })
                    ->form([
                        Radio::make('check')
                            ->default(99)
                            ->inline()
                            ->label('正向檢查結果')
                            ->options([
                                99 => '全部',
                                0 => '無',
                                1 => '有',
                            ])
                            ->live()
                            ->extraAttributes(['class' => 'inline-radio']),
                    ]),
                Filter::make('check2')
                    ->query(function ($query, $data) {
                        $check = (int) ($data['check2'] ?? 99); // 強制轉換為數字，避免字串問題
                        if ($check !== 99) {
                            $query->where('check2', $check);
                        }
                    })
                    ->form([
                        Radio::make('check2')
                            ->default(99)
                            ->inline()
                            ->label('反向檢查結果')
                            ->options([
                                99 => '全部',
                                0 => '無',
                                1 => '有',
                            ])
                            ->live()
                            ->extraAttributes(['class' => 'inline-radio']),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Action::make('check')
                    ->label('檢核正確')
                    ->button()
                    ->requiresConfirmation()
                    ->visible(function ($record) {
                        if($record->check == 1){
                            return false;
                        }
                        return true;
                    })
                    ->action(function ($record) {
                        // 這裡是更新資料庫狀態的邏輯
                        $record->check = 1;
                        $record->save();
                    })
                    ->modalHeading('確認更新')
                    ->modalSubheading('你確定要更新狀態嗎？')
                    ->modalButton('確認'),
            ]);
    }
    public function getFormActions()
    {
        return [
            Action::make('importTransferCard')
                ->label('匯入信用卡對帳單')
                ->action(function () {
                    $year = $this->data['year'];
                    $file = $this->data['attachments'];
                    Excel::import(new TransferCardImport(), Storage::disk('public')->path($file));
                    Notification::make()
                        ->title('匯入完成')
                        ->success()
                        ->send();
                    $this->isFileUploaded = true;
                    Log::info('匯入完成', ['isFileUploaded' => $this->isFileUploaded]);
                    // $this->emit('fileUploaded');
                    return redirect('/admin/transfer-card');
                }),
            ];
    }
    public function save()
    {
        $data = $this->form->getState();
        TempCardData::truncate();
        // dd($data);
        $file = $data['attachments'];
        $year = $data['year'];
        try {
            Excel::import(new TransferCardImport(), Storage::path('public/'.$file));
            $this->isFileUploaded = true;
            Notification::make()
                ->title('匯入完成')
                ->success()
                ->send();
            Log::info('匯入完成', ['isFileUploaded' => $this->isFileUploaded]);
            $this->loadData(); // Reload data after successful import
            $this->dispatch('refreshComponent');
            // return redirect('/admin/transfer-card');
        }catch (\Exception $e) {
            Notification::make()
                ->title('匯入失敗')
                ->danger()
                ->send();
            Log::error('File import failed: ' . $e->getMessage());

            // return redirect('/admin/transfer-card');
        }

    }
    public function check()
    {
        // Get the selected month from the form
        $selectedMonth = $this->data['year'] ?? Carbon::now()->format('Y-m');
        $startOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth();

        // Get TempCardData records for the selected month
        $monthlyCards = TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->get();

        // Reset all check values to 0 for the selected month
        TempCardData::whereDate('start_date', '>=', $startOfMonth) // More robust date range for reset
            ->whereDate('start_date', '<=', $endOfMonth)
            ->update(['check' => false, 'data_id' => null]); // Use boolean for check

        $matchCount = 0;
        $noMatchCount = 0;

        // 在這裡添加你的檢查邏輯
        foreach ($monthlyCards as $monthlyCard) {
            $cardDateForQuery = Carbon::parse($monthlyCard->start_date)->toDateString(); // Use TempCardData.start_date for date comparison
            $cardTimeShort = '';
            if (!empty($monthlyCard->mytime) && trim($monthlyCard->mytime) !== '') {
                $cardTimeShort = Carbon::parse($monthlyCard->mytime)->format('H:i');
            }

            $potentialDispatches = Dispatch::query()
                ->where('vendor_id', 3) // Fixed vendor_id to 3
                ->whereDate('start_date', $cardDateForQuery) // Compare with Dispatch.start_date
                ->where('rental_cost', $monthlyCard->cost)
                ->get();

            $matchingDispatch = null;
            if ($potentialDispatches->isNotEmpty()) {
                foreach ($potentialDispatches as $dispatch_item) {
                    if (empty($cardTimeShort)) {
                        continue;
                    }
                    if (!empty($dispatch_item->start_date)) {
                        $dispatchItemTimeFormatted = Carbon::parse($dispatch_item->start_date)->format('H:i');
                        if ($dispatchItemTimeFormatted == $cardTimeShort) {
                            $matchingDispatch = $dispatch_item;
                            break;
                        }
                    }
                }
            }

            if ($matchingDispatch) {
                $monthlyCard->data_id = $matchingDispatch->id;
                $monthlyCard->check = true;
                $monthlyCard->save();
                $matchCount++;
            } else {
                // $monthlyCard->check is already false due to reset
                $noMatchCount++;
            }
        }

        Notification::make()
            ->title('檢查完成')
            ->body("找到 {$matchCount} 筆匹配記錄，{$noMatchCount} 筆無匹配記錄")
            ->success()
            ->send();

        // Refresh the table to show updated results
        $this->loadData(); // Use loadData to refresh component data
    }
    protected function getMonthOptions(): array
    {
        $currentMonth = Carbon::now()->format('Y-m');
        $lastMonth = Carbon::now()->subMonth()->format('Y-m');
        $twoMonthsAgo = Carbon::now()->subMonths(2)->format('Y-m');

        return [
            $currentMonth => $currentMonth,
            $lastMonth => $lastMonth,
            $twoMonthsAgo => $twoMonthsAgo,
        ];
    }

    public function setActiveTab($tabName)
    {
        // Set loading state to true
        $this->isLoading = true;

        // Change active tab
        $this->activeTab = $tabName;

        // Set loading state to false after a short delay
        $this->isLoading = false;
    }

    public function setCheckFilter($value)
    {
        $this->checkFilter = (int)$value;
        $this->loadData();
    }

    // 手動審核 TempCardData 記錄
    public function manualCheckTempCard($recordId)
    {
        $record = TempCardData::find($recordId);
        if ($record) {
            $record->check = $record->check ? 0 : 1;
            $record->save();

            Notification::make()
                ->title('狀態已更新')
                ->body($record->check ? '已標記為匹配' : '已標記為未匹配')
                ->success()
                ->send();

            $this->loadData();
        }
    }

    public function setDispatchCheckFilter($value)
    {
        $this->dispatchCheckFilter = (int)$value;
        $this->loadData();
    }

    // 手動審核 Dispatch 記錄
    public function manualCheckDispatch($recordId)
    {
        $dispatch = Dispatch::find($recordId);
        if ($dispatch) {
            // 查找對應的 TempCardData 記錄
            $tempCard = TempCardData::where('data_id2', $dispatch->id)->first();

            if ($tempCard) {
                // 如果已經有匹配的記錄，則取消匹配
                $tempCard->data_id2 = null;
                $tempCard->check2 = 0;
                $tempCard->save();

                Notification::make()
                    ->title('狀態已更新')
                    ->body('已標記為未匹配')
                    ->success()
                    ->send();
            } else {
                // 將 dispatch 的 start_date 拆分為日期和時間
                $dispatchDate = date('Y-m-d', strtotime($dispatch->start_date));
                $dispatchTime = date('H:i:s', strtotime($dispatch->start_time));

                // 查找可能匹配的記錄（相同日期和金額）
                $tempCard = TempCardData::where('mydate', $dispatchDate)
                    ->where('cost', $dispatch->rental_cost)
                    ->first();

                // 如果沒有找到匹配的記錄，嘗試使用更寬鬆的時間條件
                if (!$tempCard) {
                    // 標準化時間格式
                    $dispatchTimeShort = date('H:i', strtotime($dispatchTime));

                    // 查找所有符合日期和金額的記錄
                    $possibleMatches = TempCardData::where('mydate', $dispatchDate)
                        ->where('cost', $dispatch->rental_cost)
                        ->get();

                    // 手動檢查時間是否匹配（考慮不同的時間格式）
                    foreach ($possibleMatches as $match) {
                        $tempCardTime = date('H:i', strtotime($match->mytime));
                        if ($tempCardTime == $dispatchTimeShort) {
                            // 找到匹配的記錄
                            $tempCard = $match;
                            break;
                        }
                    }
                }

                if ($tempCard) {
                    $tempCard->data_id2 = $dispatch->id;
                    $tempCard->check2 = 1;
                    $tempCard->save();

                    Notification::make()
                        ->title('狀態已更新')
                        ->body('已標記為匹配')
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('無法更新')
                        ->body('找不到匹配的信用卡記錄')
                        ->warning()
                        ->send();
                }
            }

            $this->loadData();
        }
    }

    public function checkReverse(): void // New logic as per user request
    {
        $this->isLoading = true;
        $selectedMonth = $this->data['year'] ?? Carbon::now()->format('Y-m');
        $startOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth();

        // 1. Reset reverse check status for TempCardData of the current month
        TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->update(['check2' => false, 'data_id2' => null]);

        // 2. Preload all relevant Dispatches for the month
        $monthlyDispatches = Dispatch::where('vendor_id', 3)
            ->whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->get();

        // Keep track of used dispatch IDs to ensure one dispatch marks at most one temp_card_data
        $usedDispatchIds = [];

        // 3. Iterate through TempCardData for the month
        $monthlyCards = TempCardData::whereDate('start_date', '>=', $startOfMonth)
            ->whereDate('start_date', '<=', $endOfMonth)
            ->get();

        $matchCount = 0;
        $noMatchCount = 0;

        foreach ($monthlyCards as $card) {
            // Use TempCardData.start_date for date part, and TempCardData.mytime for time part
            $cardDate = Carbon::parse($card->start_date)->toDateString();
            $cardTime = '';
            if (!empty($card->mytime) && trim($card->mytime) !== '') {
                $cardTime = Carbon::parse($card->mytime)->format('H:i');
            }
            $cardCost = $card->cost;

            if (empty($cardTime)) {
                $noMatchCount++;
                continue;
            }

            $foundDispatch = null;
            // Iterate through preloaded dispatches to find a match
            foreach ($monthlyDispatches as $dispatch) {
                // Skip if this dispatch has already been used to mark another TempCardData
                if (in_array($dispatch->id, $usedDispatchIds)) {
                    continue;
                }

                // Time for dispatch is taken from its start_date field's time part
                $dispatchDate = Carbon::parse($dispatch->start_date)->toDateString();
                $dispatchTime = '';
                if (!empty($dispatch->start_date)) { // Dispatch.start_date should be datetime
                     $dispatchTime = Carbon::parse($dispatch->start_date)->format('H:i');
                }

                if (empty($dispatchTime)) {
                    continue; // Cannot compare if dispatch time is invalid
                }

                if ($dispatchDate === $cardDate && $dispatchTime === $cardTime && $dispatch->rental_cost == $cardCost) {
                    $foundDispatch = $dispatch;
                    break;
                }
            }

            if ($foundDispatch) {
                $card->check2 = true;
                $card->data_id2 = $foundDispatch->id;
                $card->save();
                $usedDispatchIds[] = $foundDispatch->id;
                $matchCount++;
            } else {
                $noMatchCount++;
            }
        }

        Notification::make()->title('反向檢查完成 (新邏輯)')->body("{$matchCount} 筆信用卡資料被標記，{$noMatchCount} 筆未被標記。")->success()->send();
        $this->loadData();
        $this->isLoading = false;
    }

    public function queryTable(Table $table): Table
    {
        // Get the selected month from the form
        $selectedMonth = $this->data['year'] ?? Carbon::now()->format('Y-m');
        $startOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth();
        $endOfMonth = Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth();

        // Pre-fetch Dispatch IDs that have a corresponding TempCardData with check2 = 1 for the selected month
        $matchedDispatchIds = TempCardData::where('check2', 1)
            ->whereNotNull('data_id2')
            ->whereDate('start_date', '>=', $startOfMonth) // Assuming TempCardData.start_date is relevant for month scoping
            ->whereDate('start_date', '<=', $endOfMonth)
            ->pluck('data_id2') // Get a collection of Dispatch IDs
            ->flip(); // Flip the collection for efficient key checking (e.g., offsetExists)

        return $table
            ->query(
                Dispatch::query()
                    ->where('vendor_id', 3)
                    ->whereDate('start_date', '>=', $startOfMonth)
                    ->whereDate('start_date', '<=', $endOfMonth)
            )
            ->columns([
                TextColumn::make('id')->label('ID'),
                TextColumn::make('start_date')->label('日期'),
                TextColumn::make('start_time')->label('時間'),
                TextColumn::make('driver.name')->label('司機'),
                TextColumn::make('route')->label('路程'),
                TextColumn::make('rental_cost')->label('金額'),
                IconColumn::make('check_status')
                    ->label('檢查結果')
                    ->boolean()
                    ->getStateUsing(function ($record) use ($matchedDispatchIds) {
                        return $matchedDispatchIds->offsetExists($record->id);
                    }),
            ])
            ->filters([
                Filter::make('check_status')
                    ->query(function ($query, $data) {
                        $check = (int) ($data['check_status'] ?? 99);
                        if ($check !== 99) {
                            if ($check === 0) {
                                // Find Dispatch records that don't have matching TempCardData
                                $query->whereNotIn('id', function ($subquery) {
                                    $subquery->select('data_id2')
                                        ->from('temp_card_data')
                                        ->where('check2', 1)
                                        ->whereNotNull('data_id2');
                                });
                            } else {
                                // Find Dispatch records that have matching TempCardData
                                $query->whereIn('id', function ($subquery) {
                                    $subquery->select('data_id2')
                                        ->from('temp_card_data')
                                        ->where('check2', 1)
                                        ->whereNotNull('data_id2');
                                });
                            }
                        }
                    })
                    ->form([
                        Radio::make('check_status')
                            ->default(99)
                            ->inline()
                            ->label('檢查結果')
                            ->options([
                                99 => '全部',
                                0 => '無匹配',
                                1 => '有匹配',
                            ])
                            ->live()
                            ->extraAttributes(['class' => 'inline-radio']),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Action::make('check_reverse')
                    ->label('執行反向檢查')
                    ->button()
                    ->action(function () {
                        $this->checkReverse();
                    }),
            ]);
    }
}
