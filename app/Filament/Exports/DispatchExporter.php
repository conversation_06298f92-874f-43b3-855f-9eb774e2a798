<?php

namespace App\Filament\Exports;

use Carbon\Carbon;
use App\Models\Driver;
use App\Models\Dispatch;
use Filament\Actions\Exports\Exporter;
use Illuminate\Database\Eloquent\Model;
use OpenSpout\Common\Entity\Style\Color;
use OpenSpout\Common\Entity\Style\Style;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Models\Export;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Common\Entity\Style\CellVerticalAlignment;

class DispatchExporter extends Exporter
{
    protected static ?string $model = Dispatch::class;
    protected static ?string $title = '派車單';

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('start_date')
                ->label('日期')
                ->formatStateUsing(function (Model $record) {
                    return Carbon::parse($record['start_date'])->format('m-d');
                }),
            ExportColumn::make('start_date2')
                ->label('時間(起)')
                ->formatStateUsing(function (Model $record) {
                    return Carbon::parse($record['start_date'])->format('H:i');
                }),
            ExportColumn::make('customer.title')
                ->label('客戶'),
            ExportColumn::make('route')
                ->label('地點'),
            ExportColumn::make('pay_type')
                ->formatStateUsing(function (Model $record) {
                    switch ($record->pay_type) {
                        case 0:
                            return '月結';
                        case 1:
                            return '現金';
                        case 2:
                            return 'LINE PAY';
                        case 3:
                            return '匯款';
                        default:
                            return '月結';
                    }
                })
                ->label('付款方式'),
            ExportColumn::make('rental_cost')
                ->label('車資'),
            ExportColumn::make('driver.name')
                ->formatStateUsing(function (Model $record) {
                    if(!is_null($record->driver_id)){
                        return Driver::where('id', $record->driver_id)->first()->name;
                    }
                })
                ->label('司機'),
            ExportColumn::make('car.license')
                ->formatStateUsing(function (Model $record) {
                    if(!is_null($record->driver_id)){
                        return Driver::with('car')->where('id', $record->driver_id)->first()->car->license;
                    }
                })
                ->label('車號'),
            ExportColumn::make('car_type')
                ->formatStateUsing(function (Model $record) {
                    switch ($record->car_type) {
                        case 0:
                            return 'Benz';
                        case 1:
                            return 'Vito';
                        case 2:
                            return 'GRANVIA';
                        default:
                            return 'Benz';
                    }
                })
                ->label('車型'),
            ExportColumn::make('flight_no')
                ->label('航班'),
            ExportColumn::make('passenger_name')
                ->formatStateUsing(function (Model $record) {
                    return $record->passenger_name . $record->passenger_mobile ;
                })
                ->label('使用人'),
            ExportColumn::make('note')
                ->label('備註'),
        ];
    }

    public function getXlsxHeaderCellStyle(): ?Style
    {
        return (new Style())
            ->setFontBold()
            ->setFontItalic()
            ->setFontSize(14)
            ->setFontName('Consolas')
            ->setFontColor(Color::rgb(255, 255, 77))
            ->setBackgroundColor(Color::rgb(0, 0, 0))
            ->setCellAlignment(CellAlignment::CENTER)
            ->setCellVerticalAlignment(CellVerticalAlignment::CENTER);
    }
    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = '你的派車單已匯出 ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
