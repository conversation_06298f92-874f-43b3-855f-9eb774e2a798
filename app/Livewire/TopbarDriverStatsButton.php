<?php

namespace App\Livewire;

use Livewire\Component;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Actions\Action; // Import the Action class
use Filament\Actions\Contracts\Action as ActionContract; // Import the Action contract

class TopbarDriverStatsButton extends Component implements HasActions, HasForms
{
    use InteractsWithActions;
    use InteractsWithForms;

    // Define the action directly in this component
    public function statistikAction(): Action
    {
        // This re-defines the action. Alternatively, we could try to fetch the globally registered one.
        // However, defining it here makes this component self-contained for rendering its button.
        return \Filament\Actions\Action::make('viewDriverMonthlyStatsInButton') // Use a unique name if needed
            ->label('司機月統計')
            ->icon('heroicon-o-chart-bar-square')
            ->color('gray')
            ->extraAttributes(['class' => 'fi-btn-size-sm']) // Ensure button size is small like topbar buttons
            ->slideOver()
            ->modalContent(view('filament.actions.modals.driver-stats-widget-container'))
            ->modalSubmitAction(false)
            ->modalCancelAction(fn ($action) => $action->label('關閉')) // Removed type hint
            ->modalWidth('2xl');
    }

    protected function getActions(): array
    {
        return [
            $this->statistikAction(),
        ];
    }

    public function render()
    {
        return view('livewire.topbar-driver-stats-button');
    }
}
