<?php

namespace App\Livewire;

use App\Models\Driver;
use App\Models\Dispatch;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB; // Kept for potential future use, though not strictly needed for current query
use Livewire\Component;
use Filament\Facades\Filament; // Import Filament Facade
class DriverMonthlyStatsWidget extends Component
{
    public array $stats = []; // Initialize as array

    public function mount(): void
    {
        $this->loadStats();
    }

    public function loadStats(): void
    {
        // dd('loadStats called'); // Debugging line 1: Check if method is called
        $currentUserVendorId = Filament::auth()->user()->vendor_id;

        // 假設只統計 active = 1 的司機
        $driverQuery = Driver::where('active', 1);
        if ($currentUserVendorId != 0) {
            $driverQuery->where('vendor_id', $currentUserVendorId);
        }
        $drivers = $driverQuery->orderBy('name')->get();
        // dd($drivers); // Debugging line 2: Check if drivers are fetched
        $this->stats = []; // Reset stats before loading

        $currentMonthStart = Carbon::now()->startOfMonth();
        // Ensure no overflow when subtracting months, especially on 31st, 30th, 29th
        $lastMonthStart = Carbon::now()->subMonthNoOverflow()->startOfMonth();
        $monthBeforeLastStart = Carbon::now()->subMonthsNoOverflow(2)->startOfMonth();

        foreach ($drivers as $driver) {
            $driverStats = [
                'name' => $driver->name,
                'current_month_count' => $this->getDispatchCountForDriver($driver->id, $currentUserVendorId, $currentMonthStart),
                'last_month_count' => $this->getDispatchCountForDriver($driver->id, $currentUserVendorId, $lastMonthStart),
                'month_before_last_count' => $this->getDispatchCountForDriver($driver->id, $currentUserVendorId, $monthBeforeLastStart),
            ];
            $this->stats[] = $driverStats;
        }
        // dd($this->stats); // Debugging line 3: Check the final stats array
    }

    public function render()
    {
        return view('livewire.driver-monthly-stats-widget');
    }

    private function getDispatchCountForDriver(int $driverId, int $currentUserVendorId, Carbon $monthStart): int
    {
        $query = Dispatch::where('driver_id', $driverId)
            ->where('status', 2) // 已完成
            ->where('start_date', '>=', $monthStart->toDateString())
            ->where('start_date', '<', $monthStart->copy()->addMonthNoOverflow()->toDateString());

        if ($currentUserVendorId != 0) {
            // If not admin, filter dispatches by the current user's vendor_id as well.
            // This assumes that a dispatch record also has a vendor_id.
            // If a dispatch's vendor context is solely determined by the driver's vendor,
            // and drivers are already filtered by vendor_id, this might be redundant for dispatch query.
            // However, it's safer to include if dispatches can be associated with vendors independently.
            // Let's assume Dispatch model has a 'vendor_id' field.
            $query->where('vendor_id', $currentUserVendorId);
        }

        return $query->count();
    }
}
