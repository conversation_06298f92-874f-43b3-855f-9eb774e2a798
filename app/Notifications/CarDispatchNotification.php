<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Support\Facades\Log;

class CarDispatchNotification extends Notification implements ShouldQueue
{
    use Queueable;
    public $userid;
    /**
     * Create a new notification instance.
     */
    public function __construct($userid)
    {
        $this->userid = $userid;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    // public function toMail(object $notifiable): MailMessage
    // {
    //     return (new MailMessage)
    //                 ->line('The introduction to the notification.')
    //                 ->action('Notification Action', url('/'))
    //                 ->line('Thank you for using our application!');
    // }
    public function toDatabase($notifiable) {
        return [ 'message' => '您的派車AA已啟動!', ];
    }
    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
    public function toBroadcast($notifiable) {
        Log::info('broadcastOn triggered.'); // 測試日誌輸出

        return new BroadcastMessage([ 'message' => '您的派車已啟動!', ]);
    }
    public function broadcastOn() {
        return ['dispatch-channel.'.$this->userid];
    }
}
