<?php

namespace App\Models;

use Log;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Invoice extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'vendor_id',
        'customer_id',
        'department_id',
        'company_name',
        'phone',
        'invoice_date',
        'email',
        'state',
        'tax_type',
        'company_code',
        'free_amount',
        'zero_amount',
        'sales',
        'amount',
        'total_fee',
        'content',
        'invoice_no',
        'status',
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d' // 將 created_at 轉換為 Y-m-d 格式
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
                    ->logOnly(['dispatch_no', 'customer_id', 'department_id', 'passenger_name', 'passenger_mobile', 'driver_id', 'start_date', 'route', 'image_path', 'signature_path', 'rental_cost', 'driver_fee', 'flight_no', 'vendor_id', 'status', 'note', 'driver_note', 'pay_type']);
    }
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
    public function department()
    {
        return $this->belongsTo(Department::class);
    }
    public function invoiceDetails() {
        return $this->hasMany(InvoiceDetail::class);
    }
}
