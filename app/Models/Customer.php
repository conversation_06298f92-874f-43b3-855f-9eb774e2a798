<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;
    protected $fillable = [
        'sequence',
        'title',
        'full_title',
        'address',
        'telephone',
        'contact',
        'contact_mobile',
        'contact_email',
        'vendor_id',
        'company_id',
        'note',
        'status',
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];
    function dispatch()
    {
        return $this->hasMany(Dispatch::class);
    }
    function department()
    {
        return $this->hasMany(Department::class);
    }
}
