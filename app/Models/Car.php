<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Car extends Model
{
    use HasFactory;
    protected $fillable = [
        'driver_id',
        'brand',
        'model',
        'license',
        'color',
        'note',
        'status',
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];
    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }
}
