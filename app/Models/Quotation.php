<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Quotation extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'order_type',
        'location_from',
        'location_to',
        'tour_type',
        'car_type',
        'location_from_id',
        'location_from_name',
        'location_area_id',
        'location_city_id',
        'location_district_id',
        'location_area_name',
        'location_city_name',
        'location_district_name',
        'num_of_people',
        'num_of_bags',
        'child_seat',
        'booster_pad',
        'other_service',
        'total',
        'status',
        'user_line_id',
        'note',
        'passenger_name',
        'passenger_mobile',
        'passenger_address',
        'pay_type',
        'deleted_at',
        'vendor_id',
        'appointment_date',
        'flightno',
        'service_type',
    ];

    protected $casts = [
        'created_at'  => 'date:Y-m-d',
        'other_service' => 'array',
    ];
}
