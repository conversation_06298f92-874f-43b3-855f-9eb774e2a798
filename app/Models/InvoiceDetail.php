<?php

namespace App\Models;

use Log;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class InvoiceDetail extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'invoice_id',
        'name',
        'money',
        'number',
        'tax_type',
        'remark',
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d'
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
                    ->logOnly(['dispatch_no', 'customer_id', 'department_id', 'passenger_name', 'passenger_mobile', 'driver_id', 'start_date', 'route', 'image_path', 'signature_path', 'rental_cost', 'driver_fee', 'flight_no', 'vendor_id', 'status', 'note', 'driver_note', 'pay_type']);
    }
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}
