<?php

namespace App\Models;

use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Reserve extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'reserve_id',
        'driver_line_id',
        'customer_name',
        'customer_mobile',
        'reserve_type',
        'reserve_date',
        'reserve_time',
        'flight_number',
        'from_district_code',
        'from',
        'to_district_code',
        'to',
        'to_district',
        'address',
        'vehicle_type',
        'fee',
        'charter_hours',
        'passenger',
        'baggage',
        'note',
        'status',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['name', 'text']);
    }
}
