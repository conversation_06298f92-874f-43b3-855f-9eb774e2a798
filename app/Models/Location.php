<?php

namespace App\Models;

use Log;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Location extends Model
{
    use HasFactory;
    protected $fillable = [
        'vandor_id',
        'sequence',
        'title',
        'short_title'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d'
    ];
    public function lscar()
    {
        return $this->hasMany(Lscar::class);
    }
}
