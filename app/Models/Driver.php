<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Driver extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'name',
        'sex',
        'mobile',
        'active',
        'follow',
        'line_id',
        'nickname',
        'avatar',
        'vendor_id',
        'person_id',
        'address',
        'note',
        'driver_info_image_path',
        'driver_image',
        'original_filename',
        'bg_image',
        'bg_position',
        'sequence',
        'join_type',
        'deleted_at'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
        'bg_position' => 'array',
    ];
    public function car()
    {
        return $this->hasOne(Car::class);
    }
    public function vendor()
    {
        return $this->hasOne(Vendor::class);
    }
    public function dispatch()
    {
        return $this->hasMany(Dispatch::class);
    }
}
