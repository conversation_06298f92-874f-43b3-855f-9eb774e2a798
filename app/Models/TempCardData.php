<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TempCardData extends Model
{
    use HasFactory;
    protected $fillable = [
        'mydate',
        'mytime',
        'route',
        'cartype',
        'driver',
        'cost',
        'note',
        'status',
        'check',
        'check2',
        'start_date',
        'data_id',
        'data_id2',
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
        'updated_at'  => 'date:Y-m-d',
    ];
}
