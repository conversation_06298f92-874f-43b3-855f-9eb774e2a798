<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cartype extends Model
{
    use HasFactory;
    protected $fillable = [
        'vendor_id',
        'name',
        'short_name',
    ];
    protected $casts = [
        // 'created_at'  => 'date:Y-m-d',
    ];
    function dispatch()
    {
        return $this->hasMany(Dispatch::class);
    }
}
