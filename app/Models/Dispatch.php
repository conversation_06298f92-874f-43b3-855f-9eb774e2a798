<?php

namespace App\Models;

use Log;
use DateTime;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Traits\ClearsSinShengCache;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Dispatch extends Model
{
    use HasFactory, LogsActivity, Notifiable, ClearsSinShengCache;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($dispatch) {
            $dispatch->dispatch_no = self::generateDispatchNumber();
        });

        // 清除快取當資料異動時
        static::created(function ($dispatch) {
            static::clearSinShengTabCache($dispatch->vendor_id);
        });

        static::updated(function ($dispatch) {
            static::clearSinShengTabCache($dispatch->vendor_id);
        });

        static::deleted(function ($dispatch) {
            static::clearSinShengTabCache($dispatch->vendor_id);
        });
    }

    protected $fillable = [
        'dispatch_no',
        'source',
        'customer_id',
        'department_id',
        'passenger_name',
        'passenger_mobile',
        'driver_id',
        'start_date',
        'start_time',
        'end_date',
        'end_time',
        'cartype_id',
        'location_type',
        'start_location',
        'end_location',
        'route',
        'rental_cost',
        'driver_fee',
        'return_fee',
        'return_boss',
        'flight_no',
        'vendor_id',
        'image_path',
        'image2_path',
        'signature_path',
        'up_time',
        'down_time',
        'printing',
        'status',
        'note',
        'driver_note',
        'pay_type',
        'bg_image',
        'bg_image2',
        'bg_position',
        'bg_position2',
        'attachment',
        'expatriate',
        'driver2',
        'car_licence2',
        'num_of_people',
        'num_of_bags',
        'child_seat',
        'booster_pad',
        'baby_seat',
        'carno2',
        'cartype2',
        'cash',
        'deposit',
        'print_driver',
        'print_customer',
        'return',
        'return_date',
        'return_flight_no',
        'return_rental_cost',
        'night',
        'raise_a_placard',
        'across_city',
        'waiting',
        'car_start_time',
        'pickup_and_dropoff_location',
        'copy_text_1',
        'deleted_at',
        'finish_time',
        'driver_zs_id',
        'row_color',
        'confirm_order_time'
    ];

    protected $casts = [
        'created_at' => 'date:Y-m-d',
        'bg_image' => 'array',
        'bg_position' => 'array',
        'night' => 'array',
        'raise_a_placard' => 'array',
        'across_city' => 'array',
        'pickup_and_dropoff_location' => 'array',
    ];

    // public function setMetaAttribute($value)
    // {
    //     $bg_position = [];
    //     foreach ($value as $array_item) {
    //         if (!is_null($array_item['key'])) {
    //             $bg_position[] = $array_item;
    //         }
    //     }

    //     $this->attributes['bg_position'] = json_encode($bg_position);
    // }
    function cartype()
    {
        return $this->belongsTo(Cartype::class);
    }

    function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    function department()
    {
        return $this->belongsTo(Department::class);
    }

    function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function car()
    {
        return $this->hasOneThrough(Car::class, Driver::class, 'id', 'driver_id', 'driver_id', 'id');
    }

    // public static function generateDispatchNumber()
    // {
    //     return 'DP-' . date('YmdHis') . Str::random(3);
    // }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['dispatch_no', 'customer_id', 'department_id', 'passenger_name', 'passenger_mobile', 'driver_id', 'start_date', 'route', 'image_path', 'signature_path', 'rental_cost', 'driver_fee', 'flight_no', 'vendor_id', 'status', 'note', 'driver_note', 'pay_type']);
    }

    public static function generateImage($record, $signature = null)
    {
        // dd($record);
        $tmpPayType = '';
        $tmpCarType = '';
        if ($record['pay_type'] == 0) {
            $tmpPayType = '月結';
        } else if ($record['pay_type'] == 1) {
            $tmpPayType = '現金';
        } else if ($record['pay_type'] == 2) {
            $tmpPayType = 'LINE PAY';
        } else if ($record['pay_type'] == 3) {
            $tmpPayType = '匯款';
        } else if ($record['pay_type'] == 4) {
            $tmpPayType = '刷卡';
        }else if ($record['pay_type'] == 5) {
            $tmpPayType = '入房帳';
        }
        if ($record['car_type'] == 0) {
            $tmpCarType = '轎車';
        } else if ($record['car_type'] == 1) {
            $tmpCarType = 'Vito';
        } else if ($record['car_type'] == 2) {
            $tmpCarType = 'GRANVIA';
        }
        if ($record['carno2'] != null || $record['carno2'] != '') {
            $carLicense = $record['carno2'];
        } else {
            $carLicense = $record['car_license'];
        }
        if ($record['cartype2'] != null || $record['cartype2'] != '') {
            $tmpCarType = $record['cartype2'];
        } else {
            $tmpCarType = static::getCarType($record['vendor_id'], $record['cartype_id']);
        }
        $mystartdate = date('Y-m-d', strtotime($record['start_date']));
        $mystarttime = date('H:i', strtotime($record['start_date']));
        // dd($record);
        // 使用適當的工具生成圖片並保存
        $backgroundImagePath = static::getBackgroundImagePath($record);
        $background = imagecreatefromjpeg(public_path($backgroundImagePath));
        // $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $fontPath = public_path('fonts/SimHei.ttf');
        $fontPath2 = public_path('fonts/times.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $red = imagecolorallocate($background, 255, 87, 87);
        $vendorId = $record['vendor_id'];
        if ($record['location_type'] == 1) {
            $blueBox = imagecreatetruecolor(150, 150);
            $blue = imagecolorallocate($blueBox, 0, 0, 255);
            $white = imagecolorallocate($blueBox, 255, 255, 255);
            imagefilledrectangle($blueBox, 0, 0, 150, 150, $blue);
            imagettftext($blueBox, 100, 0, 10, 130, $white, $fontPath, '接');
            imagecopy($background, $blueBox, 110, 16, 0, 0, 150, 150);
            imagedestroy($blueBox);
        } else if ($record['location_type'] == 2) {
            $redBox = imagecreatetruecolor(150, 150);
            $red = imagecolorallocate($redBox, 255, 0, 0);
            $white = imagecolorallocate($redBox, 255, 255, 255);
            imagefilledrectangle($redBox, 0, 0, 150, 150, $red);
            imagettftext($redBox, 100, 0, 10, 130, $white, $fontPath, '送');
            imagecopy($background, $redBox, 110, 16, 0, 0, 150, 150);
            imagedestroy($redBox);
        }
        $dispatch_id_dest_x = 1500;
        $dispatch_id_dest_y = 140;
        $passenger_name_x = 1320;
        $passenger_name_y = 376;
        $passenger_mobile_x = 1320;
        $passenger_mobile_y = 445;
        $customer_title_x = 540;
        $customer_title_y = 375;
        $customer_department_x = 540;
        $customer_department_y = 445;
        $tmpPayType_x = 1320;
        $tmpPayType_y = 218;
        $tmpCarType_x = 1320;
        $tmpCarType_y = 293;
        $start_date_x = 540;
        $start_date_y = 218;
        $start_time_x = 540;
        $start_time_y = 293;
        $route_x = 500;
        $route_y = 580;

        if ($vendorId == 1) {
            $driver_name_x = 1400;
            $driver_name_y = 760;
            $rental_cost_x = 1550;
            $rental_cost_y = 1010;
            $flight_no_x = 1400;
            $flight_no_y = 925;
            $car_license_x = 1400;
            $car_license_y = 840;
            $note_x = 230;
            $note_y = 840;
        } else {
            $driver_name_x = 540;
            $driver_name_y = 760;
            $rental_cost_x = 540;
            $rental_cost_y = 1010;
            $flight_no_x = 540;
            $flight_no_y = 925;
            $car_license_x = 540;
            $car_license_y = 840;
            $note_x = 1130;
            if ($vendorId == 3) {
                $note_y = 760;
            } else {
                $note_y = 910;
            }
        }
        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
        imagettftext($background, 40, 0, $passenger_name_x, $passenger_name_y, $black, $fontPath, $record['passenger_name']);
        imagettftext($background, 40, 0, $passenger_mobile_x, $passenger_mobile_y, $black, $fontPath, $record['passenger_mobile']);
        imagettftext($background, 40, 0, $customer_title_x, $customer_title_y, $black, $fontPath, $record['customer_title']);
        imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, empty($record['department_name']) ? '' : $record['department_name']);
        imagettftext($background, 40, 0, $start_date_x, $start_date_y, $red, $fontPath, $mystartdate);
        imagettftext($background, 40, 0, $start_time_x, $start_time_y, $red, $fontPath, $mystarttime);
        imagettftext($background, 40, 0, $tmpPayType_x, $tmpPayType_y, $red, $fontPath, $tmpPayType);
        imagettftext($background, 40, 0, $tmpCarType_x, $tmpCarType_y, $black, $fontPath, $tmpCarType);
        if (strlen($record['route']) > 36) {
            $y = 550;
            $maxWidth = 1600;
            $lines = static::wrapText(48, 0, $fontPath, $record['route'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 36, 0, $route_x, $y, $black, $fontPath, $line);
                $y += 60;
            }
        } else {
            imagettftext($background, 48, 0, $route_x, $route_y, $black, $fontPath, $record['route']);
        }
        // imagettftext($background, 48, 0, $route_x, $route_y, $black, $fontPath, $record['route']);
        imagettftext($background, 36, 0, $driver_name_x, $driver_name_y, $black, $fontPath, $record['driver_name']);
        if ($record['pay_type'] == 1 || $record['pay_type'] == 2) {
            imagettftext($background, 36, 0, $rental_cost_x, $rental_cost_y, $black, $fontPath, $record['rental_cost']);
        } else if ($record['pay_type'] == 0 || $record['pay_type'] == 3 || $record['pay_type'] == 4 || $record['pay_type'] == 5) {
            if($record['vendor_id']==9){
                imagettftext($background, 36, 0, $rental_cost_x, $rental_cost_y, $black, $fontPath, $record['rental_cost']);
            }
            imagettftext($background, 36, 0, $rental_cost_x, $rental_cost_y, $black, $fontPath, ' - ');
        }
        imagettftext($background, 36, 0, $flight_no_x, $flight_no_y, $red, $fontPath2, $record['flight_no']);
        imagettftext($background, 36, 0, $car_license_x, $car_license_y, $black, $fontPath, $carLicense);
        // Log::info('6660 >> '.strlen($record['route']));
        if (strlen($record['note']) > 30) {
            // Log::info('> 60');
            $y = $note_y - 20;
            $maxWidth = 610;
            $lines = explode("\n", $record['note']);
            foreach ($lines as $line) {
                if (strlen($line) > 30) {
                    $wrappedLines = static::wrapText(30, 0, $fontPath, $line, $maxWidth);
                    foreach ($wrappedLines as $wrappedLine) {
                        imagettftext($background, 30, 0, $note_x, $y, $black, $fontPath, $wrappedLine);
                        $y += 40;
                    }
                } else {
                    // 如果行長度沒超過，直接顯示
                    imagettftext($background, 30, 0, $note_x, $y, $black, $fontPath, $line);
                    $y += 40;
                }
            }
        } else {
            imagettftext($background, 40, 0, $note_x, $note_y, $black, $fontPath, $record['note']);
        }
        $outputFileName = static::generateDispatchNumber() . '.jpg';
        $outputFilePath = storage_path('app/public/dispatch/' . $outputFileName);
        if (!empty($signature)) {
            // $signature = storage_path('app/public/' . $signature);
            $signature = imagecreatefrompng($signature);
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 2;
            $new_height = $signature_height / 2;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,  // 源图像资源
                0, 0,  // 目标图像的 x, y 坐标
                0, 0,  // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
            if ($vendorId == 1) {
                $tmp_signature_x = 1300;
                $tmp_signature_y = 1050;
            } else {
                $tmp_signature_x = 500;
                $tmp_signature_y = 1080;
            }
            imagecopy($background, $resized_signature, $tmp_signature_x, $tmp_signature_y, 0, 0, $new_width, $new_height);
        }
        if ($vendorId == 1 ) {
                if($record['night'] && $record['raise_a_placard'] && $record['across_city']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '夜間加成，舉牌，跨縣市');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                } else if($record['night'] && $record['raise_a_placard']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '夜間加成，舉牌');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                } else if($record['raise_a_placard'] && $record['across_city']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '舉牌，跨縣市');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                } else if($record['night'] && $record['across_city']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '夜間加成，跨縣市');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                } else if($record['night']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '夜間加成');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                } else if($record['raise_a_placard']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '舉牌');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                } else if($record['across_city']) {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '跨縣市');
                    $check_image_path = public_path('images/check.png');
                    $check_image = imagecreatefrompng($check_image_path);
                    $check_image_width = imagesx($check_image);
                    $check_image_height = imagesy($check_image);
                    $x = 510;
                    $y = 710;
                    // 將 icon 圖片合併到背景圖片上
                    imagecopy($background, $check_image, $x, $y, 0, 0, $check_image_width, $check_image_height);
                    imagedestroy($check_image);
                }else {
                    imagettftext($background, 24, 0, 600, 755, $black, $fontPath, '');
                }
            // imagettftext($background, 24, 0, 700, 755, $black, $fontPath, '');
            // imagettftext($background, 24, 0, 800, 755, $black, $fontPath, '跨縣市');

        }
        if (file_exists($outputFilePath)) {
            // Log::info('檔案已存在: ' . $outputFilePath);
            unlink($outputFilePath);
        }
        if (imagejpeg($background, $outputFilePath)) {
            $result = 'dispatch/' . $outputFileName;
        } else {
            $result = 'error';
        }
        if ($signature) {
            imagedestroy($resized_signature);
            imagedestroy($signature);
        }
        imagedestroy($background);
        return $result;
    }

    public static function generateImageV2($page = 'dispatch1', $record, $signature = null)
    {
        // dispatch1 派車單 dispatch2 出租單 dispatch3 訂車單 dispatch4 服務司機資訊
        if ($page == 'dispatch1') {
            // dd($record);
            $mystartdate = date('Y-m-d', strtotime($record['start_date']));
            $mystarttime = date('H:i', strtotime($record['start_date']));
            $bg_position = Vendor::where('id', '=', $record['vendor_id'])->first()->bg_position;
            $bg_image = Vendor::where('id', '=', $record['vendor_id'])->first()->bg_image;
            $decodedData = json_decode($bg_position, true);
            $imageDecodedData = json_decode($bg_image, true);
            $background = imagecreatefromjpeg(public_path('images/bg/' . $imageDecodedData['temp']));
            $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
            $fontPath2 = public_path('fonts/times.ttf');
            // dd($imageDecodedData);
            if ($record['location_type'] == 1) {
                $redBox = imagecreatetruecolor(150, 150);
                $red = imagecolorallocate($redBox, 255, 49, 49);
                $white = imagecolorallocate($redBox, 255, 255, 255);
                imagefilledrectangle($redBox, 0, 0, 150, 150, $red);
                imagettftext($redBox, 100, 0, 10, 130, $white, $fontPath, '接');
                imagecopy($background, $redBox, 90, 10, 0, 0, 150, 150);
                imagedestroy($redBox);
            } else if ($record['location_type'] == 2) {
                $blueBox = imagecreatetruecolor(150, 150);
                $blue = imagecolorallocate($blueBox, 0, 74, 173);
                $white = imagecolorallocate($blueBox, 255, 255, 255);
                imagefilledrectangle($blueBox, 0, 0, 150, 150, $blue);
                imagettftext($blueBox, 100, 0, 10, 130, $white, $fontPath, '送');
                imagecopy($background, $blueBox, 90, 10, 0, 0, 150, 150);
                imagedestroy($blueBox);
            }
            if ($record['carno2'] != null || $record['carno2'] != '') {
                $carLicense = $record['carno2'];
            } else {
                $carLicense = $record['car_license'];
            }
            $tmpRentalCost = empty($record['rental_cost']) ? 0 : $record['rental_cost'];
            if ($record['deposit'] != null || $record['deposit'] != '') {
                $$tmpRentalCost = (int)$record['rental_cost'] - $record['deposit'];
            }

            $black = imagecolorallocate($background, 0, 0, 0);
            $red = imagecolorallocate($background, 255, 87, 87);
            $dispatch_id_dest_x = $decodedData['dispatch_id_dest_x'];
            $dispatch_id_dest_y = $decodedData['dispatch_id_dest_y'];
            $start_date_dest_x = $decodedData['start_date_dest_x'];
            $start_date_dest_y = $decodedData['start_date_dest_y'];
            $start_time_dest_x = $decodedData['start_time_dest_x'];
            $start_time_dest_y = $decodedData['start_time_dest_y'];
            $customer_title_dest_x = $decodedData['customer_title_dest_x'];
            $customer_title_dest_y = $decodedData['customer_title_dest_y'];
            $customer_department_x = $decodedData['customer_department_x'];
            $customer_department_y = $decodedData['customer_department_y'];
            $pay_type_x = $decodedData['pay_type_x'];
            $pay_type_y = $decodedData['pay_type_y'];
            $car_type_x = $decodedData['car_type_x'];
            $car_type_y = $decodedData['car_type_y'];
            $passenger_name_x = $decodedData['passenger_name_x'];
            $passenger_name_y = $decodedData['passenger_name_y'];
            $passenger_mobile_x = $decodedData['passenger_mobile_x'];
            $passenger_mobile_y = $decodedData['passenger_mobile_y'];
            $route_x = $decodedData['route_x'];
            $route_y = $decodedData['route_y'];
            $driver_name_x = $decodedData['driver_name_x'];
            $driver_name_y = $decodedData['driver_name_y'];
            $car_license_x = $decodedData['car_license_x'];
            $car_license_y = $decodedData['car_license_y'];
            $flight_no_x = $decodedData['flight_no_x'];
            $flight_no_y = $decodedData['flight_no_y'];
            $rental_cost_x = $decodedData['rental_cost_x'];
            $rental_cost_y = $decodedData['rental_cost_y'];
            $num_of_people_x = $decodedData['num_of_people_x'];
            $num_of_people_y = $decodedData['num_of_people_y'];
            $num_of_bags_x = $decodedData['num_of_bags_x'];
            $num_of_bags_y = $decodedData['num_of_bags_y'];
            $child_seat_x = $decodedData['child_seat_x'];
            $child_seat_y = $decodedData['child_seat_y'];
            $booster_pad_x = $decodedData['booster_pad_x'];
            $booster_pad_y = $decodedData['booster_pad_y'];
            $note_x = $decodedData['note_x'];
            $note_y = $decodedData['note_y'];
            $signature_x = $decodedData['signature_x'];
            $signature_y = $decodedData['signature_y'];
            $tmpPayType = '';
            $tmpCarType = '';
            if ($record['pay_type'] == 0) {
                $tmpPayType = '月結';
            } else if ($record['pay_type'] == 1) {
                $tmpPayType = '現金';
            } else if ($record['pay_type'] == 2) {
                $tmpPayType = 'LINE PAY';
            } else if ($record['pay_type'] == 3) {
                $tmpPayType = '匯款';
            } else if ($record['pay_type'] == 4) {
                $tmpPayType = '刷卡';
            } else if ($record['pay_type'] == 5) {
                $tmpPayType = '入房帳';
            }
            if ($record['cartype2'] != null || $record['cartype2'] != '') {
                $tmpCarType = $record['cartype2'];
            } else {
                $tmpCarType = static::getCarType($record['vendor_id'], $record['cartype_id']);
            }
            imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
            imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $red, $fontPath, $mystartdate);
            imagettftext($background, 40, 0, $start_time_dest_x, $start_time_dest_y, $red, $fontPath, $mystarttime);
            imagettftext($background, 40, 0, $customer_title_dest_x, $customer_title_dest_y, $black, $fontPath, $record['customer_title']);
            imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, empty($record['department_name']) ? '' : $record['department_name']);
            imagettftext($background, 40, 0, $pay_type_x, $pay_type_y, $red, $fontPath, empty($tmpPayType) ? '' : $tmpPayType);
            imagettftext($background, 40, 0, $car_type_x, $car_type_y, $black, $fontPath, empty($tmpCarType) ? '' : $tmpCarType);
            imagettftext($background, 40, 0, $passenger_name_x, $passenger_name_y, $black, $fontPath, empty($record['passenger_name']) ? '' : $record['passenger_name']);
            imagettftext($background, 40, 0, $passenger_mobile_x, $passenger_mobile_y, $black, $fontPath, empty($record['passenger_mobile']) ? '' : $record['passenger_mobile']);
            if (strlen($record['route']) > 36) {
                $y = $route_y;
                $maxWidth = 1600;
                $lines = static::wrapText(48, 0, $fontPath, $record['route'], $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 32, 0, $route_x, $y, $black, $fontPath, $line);
                    $y += 60;
                }
            } else {
                imagettftext($background, 48, 0, $route_x, $route_y, $black, $fontPath, $record['route']);
            }
            imagettftext($background, 40, 0, $driver_name_x, $driver_name_y, $black, $fontPath, empty($record['driver_name']) ? '' : $record['driver_name']);
            imagettftext($background, 40, 0, $car_license_x, $car_license_y, $black, $fontPath, empty($carLicense) ? '' : $carLicense);
            imagettftext($background, 40, 0, $flight_no_x, $flight_no_y, $red, $fontPath, empty($record['flight_no']) ? '' : $record['flight_no']);
            imagettftext($background, 40, 0, $rental_cost_x, $rental_cost_y, $black, $fontPath, $tmpRentalCost);
            imagettftext($background, 40, 0, $num_of_people_x, $num_of_people_y, $black, $fontPath, empty($record['num_of_people']) ? '' : $record['num_of_people']);
            imagettftext($background, 40, 0, $num_of_bags_x, $num_of_bags_y, $black, $fontPath, empty($record['num_of_bags']) ? '' : $record['num_of_bags']);
            imagettftext($background, 40, 0, $child_seat_x, $child_seat_y, $black, $fontPath, empty($record['child_seat']) ? '' : $record['child_seat']);
            imagettftext($background, 40, 0, $booster_pad_x, $booster_pad_y, $black, $fontPath, empty($record['booster_pad']) ? '' : $record['booster_pad']);
            if (strlen($record['note']) > 30) {
                // Log::info('> 60');
                $y = $note_y - 40;
                $maxWidth = 610;
                $lines = explode("\n", $record['note']);
                foreach ($lines as $line) {
                    if (strlen($line) > 30) {
                        $wrappedLines = static::wrapText(30, 0, $fontPath, $line, $maxWidth);
                        foreach ($wrappedLines as $wrappedLine) {
                            imagettftext($background, 30, 0, $note_x, $y, $red, $fontPath, $wrappedLine);
                            $y += 40;
                        }
                    } else {
                        // 如果行長度沒超過，直接顯示
                        imagettftext($background, 30, 0, $note_x, $y, $red, $fontPath, $line);
                        $y += 40;
                    }
                }
            } else {
                imagettftext($background, 40, 0, $note_x, $note_y, $black, $fontPath, $record['note']);
            }

            $outputFileName = static::generateDispatchNumber() . '.jpg';
            $outputFilePath = storage_path('app/public/dispatch/' . $outputFileName);
            if (!empty($signature)) {
                // $signature = storage_path('app/public/' . $signature);
                $signature = imagecreatefrompng($signature);
                $target_width = 500;
                $target_height = 240;
                $signature_width = imagesx($signature);
                $signature_height = imagesy($signature);
                $scale = min($target_width / $signature_width, $target_height / $signature_height);
                $new_width = (int) ($signature_width * $scale);
                $new_height = (int) ($signature_height * $scale);
                $resized_signature = imagecreatetruecolor($new_width, $new_height);
                imagealphablending($resized_signature, false);
                imagesavealpha($resized_signature, true);
                imagecopyresampled(
                    $resized_signature,  // 目标图像资源
                    $signature,  // 源图像资源
                    0, 0,  // 目标图像的 x, y 坐标
                    0, 0,  // 源图像的 x, y 坐标
                    $new_width, $new_height,  // 目标图像的宽度和高度
                    $signature_width, $signature_height  // 源图像的宽度和高度
                );
                imagecopy($background, $resized_signature, 500, 1060, 0, 0, $new_width, $new_height);
            }
            if (file_exists($outputFilePath)) {
                // Log::info('檔案已存在: ' . $outputFilePath);
                unlink($outputFilePath);
            }
            if (imagejpeg($background, $outputFilePath)) {
                $result = 'dispatch/' . $outputFileName;
            } else {
                $result = 'error';
            }
            if ($signature) {
                imagedestroy($resized_signature);
                imagedestroy($signature);
            }
            imagedestroy($background);
            return $result;
        } elseif ($page == 'dispatch2') {
            // dd($record);
            $vendor = Vendor::where('id', '=', $record['vendor_id'])->first();
            $decodedData = json_decode($vendor->bg_position2, true);
            $background = imagecreatefromjpeg(public_path('images/bg/' . $vendor['bg_image2']));
            $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
            $fontPath2 = public_path('fonts/times.ttf');
            $black = imagecolorallocate($background, 0, 0, 0);
            $red = imagecolorallocate($background, 255, 87, 87);
            if ($record['carno2'] != null || $record['carno2'] != '') {
                $carLicense = $record['carno2'];
            } else {
                $carLicense = ($record->car && isset($record->car->license)) ? $record->car->license : null;
            }
            if ($record['location_type'] == 1) {
                $customerAdd = static::getAirportChinese($record['end_location']) ?? $record['end_location'];
            } else if ($record['location_type'] == 2) {
                $customerAdd = $record['start_location'];
            } else {
                $customerAdd = $record['start_location'];
            }
            $dispatch_id_dest_x = $decodedData['dispatch_id_dest_x'];
            $dispatch_id_dest_y = $decodedData['dispatch_id_dest_y'];
            $passenger_name_dest_x = $decodedData['passenger_name_dest_x'];
            $passenger_name_dest_y = $decodedData['passenger_name_dest_y'];
            $passenger_mobile_dest_x = $decodedData['passenger_mobile_dest_x'];
            $passenger_mobile_dest_y = $decodedData['passenger_mobile_dest_y'];
            $customer_addr_dist_x = $decodedData['customer_addr_dist_x'];
            $customer_addr_dist_y = $decodedData['customer_addr_dist_y'];
            $driver_name_dest_x = $decodedData['driver_name_dest_x'];
            $driver_name_dest_y = $decodedData['driver_name_dest_y'];
            $person_id_dest_x = $decodedData['person_id_dest_x'];
            $person_id_dest_y = $decodedData['person_id_dest_y'];
            $address_dest_x = $decodedData['address_dest_x'];
            $address_dest_y = $decodedData['address_dest_y'];
            $car_license_dest_x = $decodedData['car_license_dest_x'];
            $car_license_dest_y = $decodedData['car_license_dest_y'];
            $car_type_dest_x = $decodedData['car_type_dest_x'];
            $car_type_dest_y = $decodedData['car_type_dest_y'];
            $rental_cost_dest_x = $decodedData['rental_cost_x'];
            $rental_cost_dest_y = $decodedData['rental_cost_y'];
            $date_start_dest_x = $decodedData['date_start_dest_x'];
            $date_start_dest_y = $decodedData['date_start_dest_y'];
            $flight_no_x = $decodedData['flight_no_x'];
            $flight_no_y = $decodedData['flight_no_y'];
            $dateConvert = static::convertToChineseDate($record['start_date'], $record['end_date']);
            // dd($dateConvert);die;
            imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
            imagettftext($background, 30, 0, $passenger_name_dest_x, $passenger_name_dest_y, $black, $fontPath, $record['passenger_name']);
            imagettftext($background, 30, 0, $passenger_mobile_dest_x, $passenger_mobile_dest_y, $black, $fontPath, $record['passenger_mobile']);
            imagettftext($background, 30, 0, $customer_addr_dist_x, $customer_addr_dist_y, $black, $fontPath, $customerAdd);
            imagettftext($background, 30, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $record->driver_name ?? '');

            $driverPersonId = ($record->driver && isset($record->driver->person_id)) ? $record->driver->person_id : null;
            imagettftext($background, 28, 0, $person_id_dest_x, $person_id_dest_y, $black, $fontPath, $driverPersonId ?? '');

            $driverAddress = ($record->driver && isset($record->driver->address)) ? $record->driver->address : null;
            if ($driverAddress && strlen($driverAddress) > 26) {
                $y = $address_dest_y - 25;
                $maxWidth = 320;
                $lines = static::wrapText(20, 0, $fontPath, $driverAddress, $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 20, 0, $address_dest_x, $y, $black, $fontPath, $line);
                    $y += 30;
                }
            } else {
                imagettftext($background, 30, 0, $address_dest_x, $address_dest_y, $black, $fontPath, $driverAddress ?? '');
            }

            imagettftext($background, 28, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $carLicense);

            $carBrand = ($record->car && isset($record->car->brand)) ? $record->car->brand : null;
            imagettftext($background, 30, 0, $car_type_dest_x, $car_type_dest_y, $black, $fontPath, $carBrand ?? '');

            imagettftext($background, 28, 0, $date_start_dest_x, $date_start_dest_y, $black, $fontPath, $dateConvert);
            imagettftext($background, 30, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $record['rental_cost']);
            imagettftext($background, 30, 0, $flight_no_x, $flight_no_y, $black, $fontPath, $record['flight_no']);
            $outputFileName = static::generateDispatchNumber() . '.jpg';
            $outputFilePath = storage_path('app/public/dispatch2/' . $outputFileName);

            if (file_exists($outputFilePath)) {
                // Log::info('檔案已存在: ' . $outputFilePath);
                unlink($outputFilePath);
            }
            if (imagejpeg($background, $outputFilePath)) {
                $result = 'dispatch2/' . $outputFileName;
            } else {
                $result = 'error';
            }
            imagedestroy($background);
            return $result;
        }
    }
    public static function generateImageV3($page = 'dispatch1', $record, $signature = null)
    {
        // V3: vendor 6 瑞陞以後調整版面，加入上下車地點
        // dispatch1 派車單 dispatch2 出租單 dispatch3 訂車單 dispatch4 服務司機資訊
        if ($page == 'dispatch1') {
            // dd($record);
            // Log::info('gen image v3 >> ',$record->toArray());
            $itemCounter = 1; // 初始化計數器
            $maxLineLength = 36; // 定義最大行寬度 (根據你的版面調整)
            $tmpLocatiionStr = ''; // 確保字串也初始化
            $tmpStartChinese = '';
            $tmpEndChinese = '';
            $mystartdate = date('Y-m-d', strtotime($record['start_date']));
            $mystarttime = date('H:i', strtotime($record['start_date']));
            $bg_position = Vendor::where('id', '=', $record['vendor_id'])->first()->bg_position;
            $bg_image = Vendor::where('id', '=', $record['vendor_id'])->first()->bg_image;
            $decodedData = json_decode($bg_position, true);
            $imageDecodedData = json_decode($bg_image, true);
            $background = imagecreatefromjpeg(public_path('images/bg/' . $imageDecodedData['temp']));
            $fontPath = public_path('fonts/SimHei.ttf');
            $fontPath2 = public_path('fonts/times.ttf');
            // dd($imageDecodedData);
            // Log::info('gen image v3 location_type >> '.$record['location_type']);
            if ($record['location_type'] == 1) {
                // 接機
                $greenBox = imagecreatetruecolor(200, 120);
                $green = imagecolorallocate($greenBox, 0, 153, 51);
                $white = imagecolorallocate($greenBox, 255, 255, 255);
                imagefilledrectangle($greenBox, 0, 0, 200, 120, $green);
                imagettftext($greenBox, 70, 0, 10, 90, $white, $fontPath, '接機');
                imagecopy($background, $greenBox, 90, 10, 0, 0, 200, 120);
                imagedestroy($greenBox);
                $tmpStartChinese = static::getAirportChinese($record['start_location']);
                // $tmpLocatiionStr .= $record['start_location'];
            } else if ($record['location_type'] == 2) {
                $blueBox = imagecreatetruecolor(200, 120);
                $blue = imagecolorallocate($blueBox, 0, 74, 173);
                $white = imagecolorallocate($blueBox, 255, 255, 255);
                imagefilledrectangle($blueBox, 0, 0, 200, 120, $blue);
                imagettftext($blueBox, 70, 0, 10, 90, $white, $fontPath, '送機');
                imagecopy($background, $blueBox, 90, 10, 0, 0, 200, 120);
                imagedestroy($blueBox);
                $tmpEndChinese = static::getAirportChinese($record['end_location']);
                // $tmpLocatiionStr .= $record['start_location'];
            }else{
                if ($record['location_type'] == 0) {
                    $tmpText = '單程';
                } else if ($record['location_type'] == 3) {
                    $tmpText = '包車';
                } else if ($record['location_type'] == 4) {
                    $tmpText = '短途';
                } else {
                    $tmpText = '';
                }
                $greyBox = imagecreatetruecolor(200, 120);
                $grey = imagecolorallocate($greyBox, 90, 90, 140);
                $white = imagecolorallocate($greyBox, 255, 255, 255);
                imagefilledrectangle($greyBox, 0, 0, 200, 120, $grey);
                imagettftext($greyBox, 70, 0, 10, 90, $white, $fontPath, $tmpText);
                imagecopy($background, $greyBox, 90, 10, 0, 0, 200, 120);
                imagedestroy($greyBox);
            }
            // --- Start Building Location String ---
            $locations = $record['pickup_and_dropoff_location'];
            $hasExtraStops = !empty($locations) && is_array($locations);

            if ($record['location_type'] == 1) { // 接機 (Airport Pickup)
                $startAirport = static::getAirportChinese($record['start_location']) ?? $record['start_location'];
                // 1. 加入起點機場
                $tmpLocatiionStr .= $itemCounter++ . ". " . $startAirport . PHP_EOL . "--------------" . PHP_EOL; // $itemCounter 變成 2

                // 2. 加入主要乘客資訊及其目的地
                $tmpLocatiionStr .= $itemCounter++ . '. 乘客: ' . ($record['passenger_name'] ?? '') . ', 手機: ' . ($record['passenger_mobile'] ?? '') . PHP_EOL; // 使用 2, 然後 $itemCounter 變成 3
                // *** 確保這一行前面沒有多餘的 $itemCounter++ 或數字輸出 ***
                $tmpLocatiionStr .= "   地點: " . ($record['end_location'] ?? '') . PHP_EOL; // 這一行只輸出 "地點:" 和內容
                $tmpLocatiionStr .= "---------------------" . PHP_EOL;

                // 3. 如果有額外停靠點，依序加入
                if ($hasExtraStops) {
                    foreach ($locations as $value) {
                        // 這裡會從 3 開始輸出
                        $tmpLocatiionStr .= $itemCounter++ . '. 乘客: ' . ($value['name'] ?? '') . ', 手機: ' . ($value['mobile'] ?? '') . PHP_EOL;
                        $tmpLocatiionStr .= "   地點: " . ($value['location'] ?? '') . PHP_EOL;
                        $tmpLocatiionStr .= "---------------------" . PHP_EOL;
                    }
                }
                // 注意：最後不需要再加一次 end_location 了，因為已在第 2 點加入

            } else { // 送機 (Airport Dropoff) or Other Types
                $startLocationDisplay = $record['start_location'];
                // 1. 加入主要乘客資訊及起點 (分成多行)
                $tmpLocatiionStr .= $itemCounter++ . '. 乘客: ' . ($record['passenger_name'] ?? ''); // 主要乘客姓名
                $tmpLocatiionStr .= "   手機: " . ($record['passenger_mobile'] ?? '') . PHP_EOL; // 主要乘客手機
                $tmpLocatiionStr .= "   地點: " . $startLocationDisplay . PHP_EOL; // 起點
                $tmpLocatiionStr .= "--------------" . PHP_EOL; // 第一項後的分隔線

                // 2. 如果有額外停靠點，依序加入 (也分成多行)
                if ($hasExtraStops) {
                    foreach ($locations as $value) {
                        $tmpLocatiionStr .= $itemCounter++ . '. 乘客: ' . ($value['name'] ?? '');
                        $tmpLocatiionStr .= "   手機: " . ($value['mobile'] ?? '') . PHP_EOL;
                        $tmpLocatiionStr .= "   地點: " . ($value['location'] ?? '') . PHP_EOL;
                        $tmpLocatiionStr .= "---------------------" . PHP_EOL; // 中間項後的分隔線
                    }
                }

                // 3. 加入最終目的地 (只有地點)
                $endLocationDisplay = ($record['location_type'] == 2)
                                      ? (static::getAirportChinese($record['end_location']) ?? $record['end_location']) // 送機轉機場名
                                      : $record['end_location']; // 其他類型直接用
                $tmpLocatiionStr .= $itemCounter++ . ". " . $endLocationDisplay . PHP_EOL; // 只顯示最終地點
                // 注意：這裡不再需要分隔線，因為是最後一項
            }

            // Split the final string into lines for drawing
            $locationlines = static::splitTextIntoLines($tmpLocatiionStr, $maxLineLength);
            // --- End Building Location String ---
            // dd($tmpLocatiionStr);
            if($record['pay_type'] == 1){
                $redBox = imagecreatetruecolor(200, 120);
                $red = imagecolorallocate($redBox, 255, 49, 49);
                $white = imagecolorallocate($redBox, 255, 255, 255);
                imagefilledrectangle($redBox, 0, 0, 200, 120, $red);
                imagettftext($redBox, 70, 0, 10, 90, $white, $fontPath, '現金');
                imagecopy($background, $redBox, 290, 10, 0, 0, 200, 120);
                imagedestroy($redBox);
            }
            if ($record['carno2'] != null || $record['carno2'] != '') {
                $carLicense = $record['carno2'];
            } else {
                $carLicense = $record['car_license'];
            }
            if($record['vendor_id'] == 6){
                $record['department_name'] = '';

            }
            $black = imagecolorallocate($background, 0, 0, 0);
            $red = imagecolorallocate($background, 255, 87, 87);
            $dispatch_id_dest_x = $decodedData['dispatch_id_dest_x'];
            $dispatch_id_dest_y = $decodedData['dispatch_id_dest_y'];
            $start_date_dest_x = $decodedData['start_date_dest_x'];
            $start_date_dest_y = $decodedData['start_date_dest_y'];
            $start_time_dest_x = $decodedData['start_time_dest_x'];
            $start_time_dest_y = $decodedData['start_time_dest_y'];
            $customer_title_dest_x = $decodedData['customer_title_dest_x'];
            $customer_title_dest_y = $decodedData['customer_title_dest_y'];
            $customer_department_x = $decodedData['customer_department_x'];
            $customer_department_y = $decodedData['customer_department_y'];
            $pay_type_x = $decodedData['pay_type_x'];
            $pay_type_y = $decodedData['pay_type_y'];
            $car_type_x = $decodedData['car_type_x'];
            $car_type_y = $decodedData['car_type_y'];
            $passenger_name_x = $decodedData['passenger_name_x'];
            $passenger_name_y = $decodedData['passenger_name_y'];
            $passenger_mobile_x = $decodedData['passenger_mobile_x'];
            $passenger_mobile_y = $decodedData['passenger_mobile_y'];
            $route_x = $decodedData['route_x'];
            $route_y = $decodedData['route_y'];
            $driver_name_x = $decodedData['driver_name_x'];
            $driver_name_y = $decodedData['driver_name_y'];
            $car_license_x = $decodedData['car_license_x'];
            $car_license_y = $decodedData['car_license_y'];
            $flight_no_x = $decodedData['flight_no_x'];
            $flight_no_y = $decodedData['flight_no_y'];
            $rental_cost_x = $decodedData['rental_cost_x'];
            $rental_cost_y = $decodedData['rental_cost_y'];
            $num_of_people_x = $decodedData['num_of_people_x'];
            $num_of_people_y = $decodedData['num_of_people_y'];
            $num_of_bags_x = $decodedData['num_of_bags_x'];
            $num_of_bags_y = $decodedData['num_of_bags_y'];
            $child_seat_x = $decodedData['child_seat_x'];
            $child_seat_y = $decodedData['child_seat_y'];
            $booster_pad_x = $decodedData['booster_pad_x'];
            $booster_pad_y = $decodedData['booster_pad_y'];
            $baby_seat_x = $decodedData['baby_seat_x'];
            $baby_seat_y = $decodedData['baby_seat_y'];
            $location_x = $decodedData['location_x'];
            $location_y = $decodedData['location_y'];
            $note_x = $decodedData['note_x'];
            $note_y = $decodedData['note_y'];
            $signature_x = $decodedData['signature_x'];
            $signature_y = $decodedData['signature_y'];
            $tmpPayType = '';
            $tmpCarType = '';
            $tmpCost = $record['rental_cost'];

            if ($record['pay_type'] == 0) {
                $tmpPayType = '月結';
                if ($record->vendor_id != 9) { // 非新生廠商的月結顯示 '-'
                    $tmpCost = '-';
                }
            } else if ($record['pay_type'] == 1) {
                $tmpPayType = '現金';
                // 現金時，$tmpCost 保持為 $record->rental_cost (所有廠商都顯示實際金額)
            } else if ($record->pay_type == 2) {
                $tmpPayType = '匯款';
                if ($record->vendor_id != 9) { // 非新生廠商的匯款顯示 '-'
                    $tmpCost = '-';
                }
            } else if ($record->pay_type == 3) {
                $tmpPayType = '刷卡';
                if ($record->vendor_id != 9) { // 非新生廠商的刷卡顯示 '-'
                    $tmpCost = '-';
                }
            } else {
                $tmpPayType = ''; // 未知付款方式
                if ($record->vendor_id != 9) {
                    $tmpCost = '-'; // 對於未知付款方式，非新生廠商也顯示 '-'
                }
            }

            if ($record['cartype2'] != null || $record['cartype2'] != '') {
                $tmpCarType = $record['cartype2'];
            } else {
                $tmpCarType = static::getCarType($record['vendor_id'], $record['cartype_id']);
            }

            imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
            imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $red, $fontPath, $mystartdate);
            imagettftext($background, 40, 0, $start_time_dest_x, $start_time_dest_y, $red, $fontPath, $mystarttime);
            imagettftext($background, 40, 0, $customer_title_dest_x, $customer_title_dest_y, $black, $fontPath, $record['customer_title']);
            imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, empty($record['department_name']) ? '' : $record['department_name']);
            imagettftext($background, 40, 0, $pay_type_x, $pay_type_y, $red, $fontPath, empty($tmpPayType) ? '' : $tmpPayType);
            imagettftext($background, 40, 0, $car_type_x, $car_type_y, $black, $fontPath, empty($tmpCarType) ? '' : $tmpCarType);
            imagettftext($background, 36, 0, $passenger_name_x, $passenger_name_y, $black, $fontPath, empty($record['passenger_name']) ? '' : $record['passenger_name']);
            imagettftext($background, 40, 0, $passenger_mobile_x, $passenger_mobile_y, $black, $fontPath, empty($record['passenger_mobile']) ? '' : $record['passenger_mobile']);
            // if (strlen($record['route']) > 26) {
            //     $y = $route_y;
            //     $maxWidth = 800;
            //     $lines = static::wrapText(44, 0, $fontPath, $record['route'], $maxWidth);
            //     foreach ($lines as $line) {
            //         imagettftext($background, 28, 0, $route_x, $y, $black, $fontPath, $line);
            //         $y += 60;
            //     }
            // } else {
            //     imagettftext($background, 44, 0, $route_x, $route_y, $black, $fontPath, $record['route']);
            // }

            imagettftext($background, 32, 0, $driver_name_x, $driver_name_y, $black, $fontPath, empty($record['driver_name']) ? '' : $record['driver_name']);
            imagettftext($background, 32, 0, $car_license_x, $car_license_y, $black, $fontPath, empty($carLicense) ? '' : $carLicense);
            imagettftext($background, 32, 0, $flight_no_x, $flight_no_y, $red, $fontPath, empty($record['flight_no']) ? '' : $record['flight_no']);
            imagettftext($background, 40, 0, $rental_cost_x, $rental_cost_y, $black, $fontPath, empty($tmpCost) ? '-' : $tmpCost);
            imagettftext($background, 40, 0, $num_of_people_x, $num_of_people_y, $black, $fontPath, empty($record['num_of_people']) ? '' : $record['num_of_people']);
            imagettftext($background, 40, 0, $num_of_bags_x, $num_of_bags_y, $black, $fontPath, empty($record['num_of_bags']) ? '' : $record['num_of_bags']);
            imagettftext($background, 40, 0, $child_seat_x, $child_seat_y, $black, $fontPath, empty($record['child_seat']) ? '' : $record['child_seat']);
            imagettftext($background, 40, 0, $booster_pad_x, $booster_pad_y, $black, $fontPath, empty($record['booster_pad']) ? '' : $record['booster_pad']);
            imagettftext($background, 40, 0, $baby_seat_x, $baby_seat_y, $black, $fontPath, empty($record['baby_seat']) ? '' : $record['baby_seat']);
            $y = $location_y - 30;
            // $y2 = $note_y;
            // dd($locationlines);
            if (!empty($locationlines)) {
                $maxWidth = 680;
                $fontSize = 30;
                $lineHeight = $fontSize * 1.5; // 調整行高，這裡假設行高是 1.2 倍字體大小

                // 計算總文本高度
                $numLines = count($locationlines);
                $textHeight = $lineHeight * $numLines;

                // 繪製每一行文字
                foreach ($locationlines as $line) {
                    // dd($line);
                    imagettftext($background, $fontSize, 0, $location_x, $y, $black, $fontPath, $line);
                    $y += $lineHeight;
                }

                // 更新 $y 值，用於後續文本繪製
                $y += 60;
            }
            $notey = $note_y;
            if (strlen($record['note']) > 30) {
                // Log::info('> 60');
                $maxWidth = 610;
                $lines = explode("\n", $record['note']);
                foreach ($lines as $line) {
                    if (strlen($line) > 30) {
                        $wrappedLines = static::wrapText(30, 0, $fontPath, $line, $maxWidth);
                        foreach ($wrappedLines as $wrappedLine) {
                            imagettftext($background, 30, 0, $note_x, $notey, $red, $fontPath, $wrappedLine);
                            $notey += 40;
                        }
                    } else {
                        // 如果行長度沒超過，直接顯示
                        imagettftext($background, 30, 0, $note_x, $notey, $red, $fontPath, $line);
                        $notey += 40;
                    }
                }
            } else {
                imagettftext($background, 40, 0, $note_x, $notey, $black, $fontPath, $record['note']);
            }

            $outputFileName = static::generateDispatchNumber() . '.jpg';
            $outputFilePath = storage_path('app/public/dispatch/' . $outputFileName);
            if (!empty($signature)) {
                // $signature = storage_path('app/public/' . $signature);
                $signature = imagecreatefrompng($signature);
                $target_width = 500;
                $target_height = 240;
                $signature_width = imagesx($signature);
                $signature_height = imagesy($signature);
                $scale = min($target_width / $signature_width, $target_height / $signature_height);
                $new_width = (int) ($signature_width * $scale);
                $new_height = (int) ($signature_height * $scale);
                $resized_signature = imagecreatetruecolor($new_width, $new_height);
                imagealphablending($resized_signature, false);
                imagesavealpha($resized_signature, true);
                imagecopyresampled(
                    $resized_signature,  // 目标图像资源
                    $signature,  // 源图像资源
                    0, 0,  // 目标图像的 x, y 坐标
                    0, 0,  // 源图像的 x, y 坐标
                    $new_width, $new_height,  // 目标图像的宽度和高度
                    $signature_width, $signature_height  // 源图像的宽度和高度
                );
                imagecopy($background, $resized_signature, 500, 1060, 0, 0, $new_width, $new_height);
            }
            if (file_exists($outputFilePath)) {
                // Log::info('檔案已存在: ' . $outputFilePath);
                unlink($outputFilePath);
            }
            if (imagejpeg($background, $outputFilePath)) {
                $result = 'dispatch/' . $outputFileName;
            } else {
                $result = 'error';
            }
            if ($signature) {
                imagedestroy($resized_signature);
                imagedestroy($signature);
            }
            imagedestroy($background);
            return $result;
        } elseif ($page == 'dispatch2') {
            // dd($record);
            $vendor = Vendor::where('id', '=', $record['vendor_id'])->first();
            $decodedData = json_decode($vendor->bg_position2, true);
            $background = imagecreatefromjpeg(public_path('images/bg/' . $vendor['bg_image2']));
            $fontPath = public_path('fonts/SimHei.ttf');
            $fontPath2 = public_path('fonts/times.ttf');
            $black = imagecolorallocate($background, 0, 0, 0);
            $red = imagecolorallocate($background, 255, 87, 87);
            if ($record['carno2'] != null || $record['carno2'] != '') {
                $carLicense = $record['carno2'];
            } else {
                $carLicense = $record['car']['license'];
            }
            if ($record['location_type'] == 1) {
                $customerAdd = $record['end_location'];
            } else if ($record['location_type'] == 2) {
                $customerAdd = $record['start_location'];
            } else {
                $customerAdd = $record['start_location'];
            }
            $dispatch_id_dest_x = $decodedData['dispatch_id_dest_x'];
            $dispatch_id_dest_y = $decodedData['dispatch_id_dest_y'];
            $passenger_name_dest_x = $decodedData['passenger_name_dest_x'];
            $passenger_name_dest_y = $decodedData['passenger_name_dest_y'];
            $passenger_mobile_dest_x = $decodedData['passenger_mobile_dest_x'];
            $passenger_mobile_dest_y = $decodedData['passenger_mobile_dest_y'];
            $customer_addr_dist_x = $decodedData['customer_addr_dist_x'];
            $customer_addr_dist_y = $decodedData['customer_addr_dist_y'];
            $driver_name_dest_x = $decodedData['driver_name_dest_x'];
            $driver_name_dest_y = $decodedData['driver_name_dest_y'];
            $person_id_dest_x = $decodedData['person_id_dest_x'];
            $person_id_dest_y = $decodedData['person_id_dest_y'];
            $address_dest_x = $decodedData['address_dest_x'];
            $address_dest_y = $decodedData['address_dest_y'];
            $car_license_dest_x = $decodedData['car_license_dest_x'];
            $car_license_dest_y = $decodedData['car_license_dest_y'];
            $car_type_dest_x = $decodedData['car_type_dest_x'];
            $car_type_dest_y = $decodedData['car_type_dest_y'];
            $rental_cost_dest_x = $decodedData['rental_cost_x'];
            $rental_cost_dest_y = $decodedData['rental_cost_y'];
            $date_start_dest_x = $decodedData['date_start_dest_x'];
            $date_start_dest_y = $decodedData['date_start_dest_y'];
            $flight_no_x = $decodedData['flight_no_x'];
            $flight_no_y = $decodedData['flight_no_y'];
            $dateConvert = static::convertToChineseDate($record['start_date'], $record['end_date']);
            // dd($dateConvert);die;
            imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $record['dispatch_no']);
            imagettftext($background, 30, 0, $passenger_name_dest_x, $passenger_name_dest_y, $black, $fontPath, $record['passenger_name']);
            imagettftext($background, 30, 0, $passenger_mobile_dest_x, $passenger_mobile_dest_y, $black, $fontPath, $record['passenger_mobile']);
            imagettftext($background, 30, 0, $customer_addr_dist_x, $customer_addr_dist_y, $black, $fontPath, $customerAdd);
            imagettftext($background, 30, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $record->driver_name ?? '');

            $driverPersonIdV3 = ($record->driver && isset($record->driver->person_id)) ? $record->driver->person_id : null;
            imagettftext($background, 28, 0, $person_id_dest_x, $person_id_dest_y, $black, $fontPath, $driverPersonIdV3 ?? '');

            $driverAddressV3 = ($record->driver && isset($record->driver->address)) ? $record->driver->address : null;
            if ($driverAddressV3 && strlen($driverAddressV3) > 26) {
                $y = $address_dest_y - 25;
                $maxWidth = 320;
                $lines = static::wrapText(20, 0, $fontPath, $driverAddressV3, $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 20, 0, $address_dest_x, $y, $black, $fontPath, $line);
                    $y += 30;
                }
            } else {
                imagettftext($background, 30, 0, $address_dest_x, $address_dest_y, $black, $fontPath, $driverAddressV3 ?? '');
            }
            $tmpCost = $record['rental_cost'];
            if ($record['pay_type'] == 0) {
                $tmpPayType = '月結';
                if ($record->vendor_id != 9) { // 非新生廠商的月結顯示 '-'
                    $tmpCost = '-';
                }
            } else if ($record['pay_type'] == 1) {
                $tmpPayType = '現金';
                // 現金時，$tmpCost 保持為 $record->rental_cost (所有廠商都顯示實際金額)
            } else if ($record->pay_type == 2) {
                $tmpPayType = '匯款';
                if ($record->vendor_id != 9) { // 非新生廠商的匯款顯示 '-'
                    $tmpCost = '-';
                }
            } else if ($record->pay_type == 3) {
                $tmpPayType = '刷卡';
                if ($record->vendor_id != 9) { // 非新生廠商的刷卡顯示 '-'
                    $tmpCost = '-';
                }
            } else {
                $tmpPayType = ''; // 未知付款方式
                if ($record->vendor_id != 9) {
                    $tmpCost = '-'; // 對於未知付款方式，非新生廠商也顯示 '-'
                }
            }
            imagettftext($background, 28, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $carLicense);
            $carBrandV3 = ($record->car && isset($record->car->brand)) ? $record->car->brand : null;
            imagettftext($background, 30, 0, $car_type_dest_x, $car_type_dest_y, $black, $fontPath, $carBrandV3 ?? '');

            imagettftext($background, 28, 0, $date_start_dest_x, $date_start_dest_y, $black, $fontPath, $dateConvert);
            imagettftext($background, 30, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $tmpCost);
            $outputFileName = static::generateDispatchNumber() . '.jpg';
            $outputFilePath = storage_path('app/public/dispatch2/' . $outputFileName);

            if (file_exists($outputFilePath)) {
                // Log::info('檔案已存在: ' . $outputFilePath);
                unlink($outputFilePath);
            }
            if (imagejpeg($background, $outputFilePath)) {
                $result = 'dispatch2/' . $outputFileName;
            } else {
                $result = 'error';
            }
            imagedestroy($background);
            return $result;
        }
    }

    public static function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = '';
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            } else {
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
    public static function splitTextIntoLines($text, $maxLength) {
        $lines = [];

        // 按照換行符拆分段落
        $paragraphs = explode("\n", $text);

        foreach ($paragraphs as $paragraph) {
            $currentLine = "";
            $currentWidth = 0;

            // 逐字處理
            for ($i = 0; $i < mb_strlen($paragraph); $i++) {
                $char = mb_substr($paragraph, $i, 1); // 取得當前字
                $charWidth = (mb_strwidth($char, 'UTF-8') > 1) ? 2 : 1; // 判斷字寬

                // 若當前行寬度加上該字超過限制，則換行
                if ($currentWidth + $charWidth > $maxLength) {
                    $lines[] = $currentLine; // 儲存當前行
                    $currentLine = ""; // 重置行內容
                    $currentWidth = 0; // 重置行寬度
                }

                $currentLine .= $char;
                $currentWidth += $charWidth;
            }

            // 加入最後一行
            if ($currentLine !== "") {
                $lines[] = $currentLine;
            }
        }

        return $lines;
    }
    public static function getAirportChinese($location) {
        if($location == '1'){
            return '松山機場';
        }else if($location == '2'){
            return '桃園機場 T1';
        }else if($location == '3'){
            return '桃園機場 T2';
        }else if($location == '4'){
            return '桃園機場';
        }else if($location == '5'){
            return '台中機場';
        }
    }
    public static function wrapText2($fontSize, $angle, $fontPath, $text, $maxWidth) {
        $words = explode(' ', $text);
        $lines = [];
        $line = '';

        foreach ($words as $word) {
            $testLine = $line ? $line . ' ' . $word : $word;
            $testBox = imagettfbbox($fontSize, $angle, $fontPath, $testLine);
            $lineWidth = $testBox[2] - $testBox[0];

            if ($lineWidth > $maxWidth && $line) {
                $lines[] = $line;
                $line = $word;
            } else {
                $line = $testLine;
            }
        }

        if ($line) {
            $lines[] = $line;
        }

        return $lines;
    }

    public static function formatChineseDate($datetime)
    {
        if (empty($datetime)) {
            return '';
        }
        // 创建 DateTime 对象
        $date = new DateTime($datetime);
        // 获取公历年份
        $year = $date->format('Y');
        // 将公历年份转换为民国年份
        $rocYear = $year - 1911;
        // 获取月份和日期
        $month = $date->format('n');  // 'n' 获取没有前导零的月份
        $day = $date->format('j');  // 'j' 获取没有前导零的日期
        $hour = $date->format('G');  // 'G' 获取24小时制的小时，范围是 0-23
        $minute = $date->format('i');  // 'i' 获取分钟，范围是 00-59

        // 构建中文日期时间字符串
        return sprintf(
            '%d 年 %d 月 %d 日 %d 時 %d 分',
            $rocYear,
            $month,
            $day,
            $hour,
            $minute
        );
    }

    public static function convertToChineseDate($start_datetime, $end_datetime)
    {
        // 定义一个内部函数来处理日期格式化
        // 格式化开始和结束时间
        $startFormatted = static::formatChineseDate($start_datetime);
        $endFormatted = static::formatChineseDate($end_datetime);

        // 如果开始或结束时间为空，返回空白
        if (empty($startFormatted) || empty($endFormatted)) {
            return '';
        }

        // 构建最终的中文时间区间字符串
        return sprintf(
            '自民國 %s 起至 %s止',
            $startFormatted,
            $endFormatted
        );
    }

    public static function getCarType($vendor_id, $car_type)
    {
        $tmpCarType = '轎車';
        if ($vendor_id == 3) {
            if ($car_type == 0) {
                $tmpCarType = 'Benz';
            } else if ($car_type == 1) {
                $tmpCarType = 'Vito';
            } else if ($car_type == 2) {
                $tmpCarType = 'LM';
            }
        } else if ($vendor_id == 4) {
            if ($car_type == 7) {
                $tmpCarType = '隨機 SUV 車款';
            } else if ($car_type == 8) {
                $tmpCarType = '隨機 MPV 車款';
            } else if ($car_type == 12) {
                $tmpCarType = '隨機 SEDAN 車款';
            } else if ($car_type == 11) {
                $tmpCarType = '依客戶需求';
            } else {
                $tmpCarType = '依客戶需求';
            }
        } else if ($vendor_id == 6) {
            if ($car_type == 20) {
                $tmpCarType = '轎車';
            } else if ($car_type == 21) {
                $tmpCarType = '進口車';
            } else if ($car_type == 22) {
                $tmpCarType = 'VAN';
            } else if ($car_type == 23) {
                $tmpCarType = 'BENZ';
            }
        } else if ($vendor_id == 9) {
            if ($car_type == 28) {
                $tmpCarType = '五人轎車';
            } else if ($car_type == 29) {
                $tmpCarType = '九人座';
            } else if ($car_type == 30) {
                $tmpCarType = '七人座';
            }
        } else {
            if ($car_type == 0) {
                $tmpCarType = '轎車';
            } else if ($car_type == 1) {
                $tmpCarType = 'Vito';
            } else if ($car_type == 2) {
                $tmpCarType = 'GRANVIA';
            }
        }
        return $tmpCarType;
    }

    public static function getBackgroundImagePath($record)
    {
        $vendorId = $record['vendor_id'];
        if ($vendorId == 1) {
            $bgPath = 'images/bg/admin_bg_001.jpg';
        } else if ($vendorId == 2) {
            $bgPath = 'images/bg/admin_bg_001.jpg';
        } else if ($vendorId == 3) {
            $bgPath = 'images/bg/admin_bg_002.jpg';
        } else if ($vendorId == 4) {
            $bgPath = 'images/bg/admin_bg_004.jpg';
        }else{
            $bgPath = 'images/bg/admin_bg_00'.$record['vendor_id'].'.jpg';
        }
        return $bgPath;
    }
    public static function calculateTextHeight($fontSize, $lineHeight, $numLines) {
        return $fontSize * $lineHeight * $numLines;
    }
    // In App/Models/Dispatch.php

    // ... (existing model code)

    public static $payTypeOptions = [
        '0' => '月結',
        '1' => '現金',
        '2' => 'LINE PAY', // 或 '匯款'，根據您的 DispatchResource
        '3' => '匯款',   // 或其他，請與 DispatchResource.php 中的定義保持一致
        // '4' => '刷卡', // 如果有此選項
    ];

    public static $locationTypeOptions = [
        '1' => '接機',
        '2' => '送機',
        '0' => '其他', // 或 '單程'
        '3' => '包車', // 如果有此選項
        '4' => '短途', // 如果有此選項
    ];

    // 方法來獲取機場選項 (範例)
    public static function getAirportOptions(): array
    {
        // 這裡應該回傳您的機場選項，例如：
        return [
            '1' => 'TSA 松山機場',
            '2' => '桃園機場 T1',
            '3' => '桃園機場 T2',
            '4' => '桃園機場', // 通用桃園機場選項
            '5' => '台中機場',
        ];
    }

    // 方法來將機場 ID 轉換為名稱 (用於預填文字輸入框)
    public static function getAirportName($value)
    {
        $options = self::getAirportOptions();
        return $options[$value] ?? $value; // 如果是 ID 就回傳名稱，否則回傳原值
    }

    public static function generateDispatchNumber()
    {
        // 您的派車單號生成邏輯，例如：
        return 'DP-' . date('YmdHis') . \Illuminate\Support\Str::random(3);
    }

    // ... (rest of the model code)

}
