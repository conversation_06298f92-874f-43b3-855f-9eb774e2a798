<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Vendor extends Model
{
    use HasFactory;
    protected $fillable = [
        'title',
        'contact',
        'mobile',
        'tel',
        'fax',
        'address',
        'note',
        'status',
        'bg_image',
        'bg_image2',
        'bg_driver_template',
        'bg_driver_position',
        'bg_order_template',
        'bg_order_position',
        'bg_position',
        'bg_position2',
    ];

    protected $casts = [
        'created_at'  => 'date:Y-m-d',
        'bg_position' => 'array',
        'bg_position2' => 'array',
        'bg_driver_position' => 'array',
        'bg_order_position' => 'array',
    ];

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function user()
    {
        return $this->hasOne(User::class);
    }
}
