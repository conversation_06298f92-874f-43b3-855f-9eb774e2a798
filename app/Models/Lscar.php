<?php

namespace App\Models;

use Log;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Lscar extends Model
{
    use HasFactory, LogsActivity;
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($dispatch) {
            $dispatch->dispatch_no = self::generateDispatchNumber();
        });
    }
    protected $fillable = [
        'dispatch_no',
        'source',
        'customer_id',
        'department_id',
        'passenger_name',
        'passenger_mobile',
        'driver_id',
        'start_date',
        'start_time',
        'end_date',
        'end_time',
        'cartype_id',
        'location_type',
        'start_location',
        'end_location',
        'route',
        'rental_cost',
        'driver_fee',
        'return_fee',
        'return_boss',
        'flight_no',
        'vendor_id',
        'image_path',
        'image2_path',
        'signature_path',
        'up_time',
        'down_time',
        'printing',
        'status',
        'note',
        'driver_note',
        'pay_type',
        'bg_image',
        'bg_image2',
        'bg_position',
        'bg_position2',
        'attachment',
        'expatriate',
        'driver2',
        'car_licence2',
        'num_of_people',
        'num_of_bags',
        'child_seat',
        'booster_pad',
        'carno2',
        'deposit',
        'print_driver',
        'print_customer',
        'return',
        'return_date',
        'return_flight_no',
        'other_passenger',
        'other_fee',
        'other_schedule',
        'repeater_schedule',
        'customize_schedule'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
        'bg_image' => 'array',
        'bg_position' => 'array',
        'other_passenger' => 'json',
        'other_fee' => 'json',
        'repeater_schedule' => 'json',
        'customize_schedule' => 'json'
    ];
    function cartype()
    {
        return $this->belongsTo(Cartype::class);
    }
    function customer()
    {
        return $this->belongsTo(Customer::class);
    }
    function department()
    {
        return $this->belongsTo(Department::class);
    }
    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }
    public function location()
    {
        return $this->belongsTo(Location::class);
    }
    public function statustype()
    {
        return $this->belongsTo(Statustype::class);
    }
    public function paytype()
    {
        return $this->belongsTo(Paytype::class);
    }
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
                    ->logOnly(['dispatch_no', 'customer_id', 'department_id', 'passenger_name', 'passenger_mobile', 'driver_id', 'start_date', 'route', 'image_path', 'signature_path', 'rental_cost', 'driver_fee', 'flight_no', 'vendor_id', 'status', 'note', 'driver_note', 'pay_type']);
    }
    public static function generateDispatchNumber()
    {
        return 'LS-' . date("YmdHis") . Str::random(3);
    }
}
