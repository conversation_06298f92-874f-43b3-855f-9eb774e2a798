<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class City extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'area_code',
        'city_code',
        'distance_code',
        'city',
        'zip',
        'status'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];

}
