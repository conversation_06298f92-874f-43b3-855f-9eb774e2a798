<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;
    protected $fillable = ['name', 'customer_id', 'vendor_id'];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
    public function dispatch()
    {
        return $this->belongsTo(Dispatch::class);
    }
}
