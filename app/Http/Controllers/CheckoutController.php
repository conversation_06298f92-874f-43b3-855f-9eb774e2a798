<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use TsaiYiHua\ECPay\Checkout;
use Illuminate\Support\Facades\Log;
use TsaiYiHua\ECPay\Services\StringService;
use Ecpay;
 use Ecpay\Services\EcpayServices;

class CheckoutController extends Controller
{
    protected $checkout;

    public function __construct(Checkout $checkout)
    {
        $this->checkout = $checkout;
    }

    public function sendOrder()
    {
        // $formData = [
        //     'UserId' => 1, // 用戶ID , Optional
        //     'ItemDescription' => '產品簡介',
        //     'ItemName' => 'Product Name',
        //     'TotalAmount' => '2000',
        //     'PaymentMethod' => 'Credit', // ALL, Credit, ATM, WebATM
        // ];
        // return $this->checkout->setPostData($formData)->send();
        $orderId = uniqid(); // 生成唯一訂單編號
        $amount = 1000; // 訂單金額
        $formData = [ 'UserId' => 1, // 用戶ID, Optional
            'ItemDescription' => '產品簡介',
            'ItemName' => 'Product Name',
            'TotalAmount' => $amount,
            'PaymentMethod' => 'Credit', // ALL, Credit, ATM, WebATM
            'MerchantTradeNo' => $orderId,
            'MerchantTradeDate' => now()->format('Y/m/d H:i:s'),
            'ReturnURL' => 'https://2fd5-123-252-20-145.ngrok-free.app/callback',
            // 'OrderResultURL' => route('payment.result'),
            'ClientBackURL' => 'https://2fd5-123-252-20-145.ngrok-free.app/success',
        ];
        // Log::info('formData >> ' . json_encode($formData));
        $payment = $this->checkout;
        return $payment->setPostData($formData)->send();
    }
    public function notifyUrl(Request $request)
    {
        var_dump($request->all());
        $serverPost = $request->post();
        Log::info($serverPost);
        $checkMacValue = $request->post('CheckMacValue');
        unset($serverPost['CheckMacValue']);
        $checkCode = StringService::checkMacValueGenerator($serverPost);
        if ($checkMacValue == $checkCode) {
            return '1|OK';
        } else {
            return '0|FAIL';
        }
    }

    public function returnUrl(Request $request)
    {
        var_dump($request->all());
        $serverPost = $request->post();
        Log::info($serverPost);
        $checkMacValue = $request->post('CheckMacValue');
        unset($serverPost['CheckMacValue']);
        $checkCode = StringService::checkMacValueGenerator($serverPost);
        if ($checkMacValue == $checkCode) {
            dd($serverPost);
            if (!empty($request->input('redirect'))) {
                return redirect($request->input('redirect'));
            } else {

                //付款完成，下面接下來要將購物車訂單狀態改為已付款
                //目前是顯示所有資料將其DD出來
                dd($this->checkout->getPostData());
            }
        }
    }
    public function callback(Request $request)
    {
        dd(request());
        // var_dump($request->all());
        // $serverPost = $request->post();
        // Log::info($serverPost);
        // $checkMacValue = $request->post('CheckMacValue');
        // unset($serverPost['CheckMacValue']);
        // $checkCode = StringService::checkMacValueGenerator($serverPost);
    }
    public function success(Request $request)
    {
        dd(request());
    }
}
