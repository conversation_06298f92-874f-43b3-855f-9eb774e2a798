<?php

namespace App\Http\Controllers;

use DateTime;
use App\Models\Mytest;
use App\Models\Vendor;
use App\Models\Customer;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TestController extends Controller
{

    public function getvendor(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'token' => 'required|max:50',
            'vendor_id' => 'required|max:50',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        if($input['token']!='123456'){
            return $this->sendError('Validation Error.', 'Token 錯誤!');
        }
        $vendor = Vendor::where('join_code', '=', $input['vendor_id'])->first(['title', 'tel']);
        if($vendor){
            return  $this->sendResponse($vendor, '查詢成功.');
        }else{
            return $this->sendError('查無此車行!');
        }
    }
    public function saveorder(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'token' => 'required|max:50',
            'vendor_id' => 'required|max:50',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        if($input['token']!='123456'){
            return $this->sendError('Validation Error.', 'Token 錯誤!');
        }
        $order = new Mytest();
        $order->source = 2;
        $order->customer_id = $input['customer_id'];
        $order->department_id = null;
        $order->passenger_name = $input['passenger_name'];
        $order->passenger_mobile = $input['passenger_mobile'];
        $order->start_date = $input['start_date'];
        $order->cartype_id = 0;
        $order->paytype_id = 0;
        $order->vendor_id = 2;
        $order->location_type = $input['location_type'];
        $order->start_location = $input['route_start'];
        $order->end_location = $input['route_end'];
        $order->route = $input['route_start'] . ' >> '.$input['route_end'];
        $order->driver_note = $input['note'];
        if($order->save()){
            return  $this->sendResponse($order, '新增成功.');
        }else{
            return $this->sendError('查無此車行!');
        }
    }
    public function createtoken(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required|max:50',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        if($input['token']!='123456'){
            return $this->sendError('Validation Error.', 'Token 錯誤!');
        }
        Vendor::where('id', '=', $input['vendor_id'])
                        ->update(['api_token' => Str::random(32)]);
        $vendor = Vendor::where('id', '=', $input['vendor_id'])->first();
        return response()->json(['token' => $vendor->api_token], 201);
    }
    public function checktoken(Request $request){
        $input = $request->all();
        $token = $request->bearerToken(); // 獲取 Bearer Token
        if (!$token) {
            return response()->json(['error' => 'Token not provided.'], 401);
        }
        $vendor = Vendor::where('api_token', $token)->first();
        if (!$vendor) {
            return response()->json(['error' => 'Invalid token.'], 401);
        } // 在這裡返回使用者的數據 return response()->json(['user' => $user],
        return response()->json(['vendor' => $vendor], 200);
    }
    public function texttocase(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'data_str' => 'required|max:500',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $token = $request->bearerToken(); // 獲取 Bearer Token
        if (!$token) {
            return response()->json(['error' => 'Token not provided.'], 401);
        }
        $inputString = $input['data_str'];
        // 定義正則匹配模式
        $patterns = [
            'customer_title' => '/公司名稱[:：](.+?)(?:，|,|\n|$)/u',
            'passenger_name' => '/乘客名稱[:：](.+?)(?:，|,|\n|$)/u',
            'passenger_mobile' => '/乘客手機[:：](.+?)(?:，|,|\n|$)/u',
            'start_date' => '/租車日期[:：](\d{4}-\d{2}-\d{2})(?:，|,|\n|$)/u', // 匹配 YYYY-MM-DD 格式的日期
            'start_time' => '/租車時間[:：](\d{1,2}:\d{2})(?:，|,|\n|$)/u',      // 匹配 HH:MM 格式的時間
            'passager_of_num' => '/人數[:：](\d+)(?:，|,|\n|$)/u',                // 匹配數字人數
            'bags' => '/行李[:：](\d+)(?:，|,|\n|$)/u',                           // 匹配行李數字
            'from' => '/起始點[:：](.+?)(?:，|,|\n|$)/u',                         // 匹配起始地址
            'to' => '/目的地[:：](.+?)(?:，|,|\n|$)/u',
            'note' => '/備註[:：](.+?)(?:，|,|\n|$)/u',
            'driver' => '/司機[:：](.+?)(?:，|,|\n|$)/u',
        ];
        $data = [];
        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $inputString, $matches)) {
                $data[$key] = trim($matches[1]);
            } else {
                $data[$key] = null; // 如果沒有匹配到，設置為空
            }
        }
        $customer = Customer::where('title', 'like','%'.$data['customer_title'].'%')->first();
        if (!$customer) {
            return response()->json(['message' => '找不到對應客戶'], 404);
        }
        if (isset($data['from']) && str_contains($data['from'], '機場')) {
            // 接機
            $airportKey = 'from';
            $data['from_id'] = static::getAirportIdByName($data['from']);
        } elseif (isset($data['to']) && str_contains($data['to'], '機場')) {
            // 送機
            $airportKey = 'to';
            $data['to_id'] = static::getAirportIdByName($data['to']);
        }else{
            // 一般
            $data['location_type'] = 3;
        }
        if(!empty($data['from_id'])){
            $data['start_location'] = $data['from_id'];
            $data['end_location'] = $data['to'];
            $data['location_type'] = 1;
        }
        if(!empty($data['to_id'])){
            $data['start_location'] = $data['from'];
            $data['end_location'] = $data['to_id'];
            $data['location_type'] = 2;
        }
        $tmp_start_date = $data['start_date'].' '.$data['start_time'];
        $tmpStartDateTime = new DateTime($tmp_start_date);
        $data['start_date'] = $tmpStartDateTime->format('Y-m-d H:i:s');
        $endDateTime = $tmpStartDateTime->modify('+2 hours');
        $data['end_date'] = $endDateTime->format('Y-m-d H:i:s');
        // var_dump($data);die;
        $data['customer_id'] = $customer->id;
        $data['route'] = $data['from'] . ' >> ' . $data['to'];
        $data['vendor_id'] = 2;
        $data['source'] = 2; //web
        $data['status'] = 102; // 未審核
        // var_dump($data);
        $dispatch = Mytest::create($data);
        if($dispatch){
            return  $this->sendResponse($dispatch, '新增成功.');
        }
        return response()->json(['error' => '新增失敗.', 500]);
    }
    public static function getAirportIdByName($input)
    {
        $mapping = [ '桃園機場' => 2, 'TSA 松山機場' => 1, '台中機場' => 4, '桃園' => 2, '松山' => 1, 'TSA' => 1 ]; // 檢查是否有匹配的字串
        foreach ($mapping as $key => $value) {
            if (strpos($input, $key) !== false) {
                return $value;
            }
        }
        return null;
    }
}
