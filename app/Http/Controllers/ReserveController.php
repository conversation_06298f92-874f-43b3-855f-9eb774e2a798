<?php

namespace App\Http\Controllers;

use App\Models\AirportPrice;
use App\Models\City;
use App\Models\Quotation;
use App\Models\Reserve;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class ReserveController extends Controller
{
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'driver_line_id' => 'required|max:255',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $input['reserve_id'] = 'RV_' . date('YmdHis') . Str::random(3);
        $order = Reserve::create([
            'driver_line_id' => $input['driver_line_id'],
            'reserve_id' => $input['reserve_id'],
            'customer_name' => $input['customer_name'],
            'customer_mobile' => $input['customer_mobile'],
            'reserve_type' => $input['reserve_type'],
            'reserve_date' => $input['reserve_date'],
            'reserve_time' => $input['reserve_time'],
            'from' => $input['from'],
            'from_district_code' => $input['from_area'],
            'to' => $input['to'],
            'to_district_code' => $input['to_area'],
            'address' => $input['address'],
            'flight_no' => $input['flight_no'],
            'no_of_passengers' => $input['no_of_passengers'],
            'no_of_bags' => $input['no_of_bags'],
            'vehicle_type' => $input['vehicle_type_data'],
            'fee' => $input['total'],
            'updated_at' => now(),
        ]);
        return $this->sendResponse($order, '成功取得資料.');
    }

    public function order(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'driver_line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $rs = Reserve::where('driver_line_id', $input['driver_line_id'])->get();
        if ($rs) {
            // foreach ($rs as $key => $value) {
            //     $value->from_district_name = $this->getDistrictName($value->from_district);
            //     $value->to_name = $this->getToName($value->to_city, $value->to_district);
            // }
            return $this->sendResponse($rs, '成功取得資料.');
        } else {
            return $this->sendError('ERROR', '查無資料.');
        }
    }

    public function invoicetest(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'driver_line_id' => 'required|max:255',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $timeStamp = round(microtime(true) * 1000);  // 設定 idno 和 password $idno = '123123'; $password = '123123';
        // $timeStamp2 = microtime(true);  // 設定 idno 和 password $idno = '123123'; $password = '123123';
        $idno = 'Giveme08';
        $password = '2Y7Nh88';
        // $url = 'https://www.giveme.com.tw/invoice.do?action=addB2C';
        $url = 'https://www.giveme.com.tw/invoice.do?action=addB2B';
        // 將 timeStamp、idno 和 password 組合成一個字串
        $combinedString = $timeStamp . $idno . $password;
        // 使用 MD5 進行哈希處理，並將結果轉換為大寫
        $md5Hash = strtoupper(md5($combinedString));
        Log::info('invouice >> ' . $timeStamp . $idno . $md5Hash);
        // 顯示結果
        // echo "timeStamp: " . $timeStamp . PHP_EOL;
        // echo "MD5 Hash: " . $md5Hash . PHP_EOL;
        $postData = ['timeStamp' => $timeStamp,
            'uncode' => '53418005',
            'idno' => $idno,
            'sign' => $md5Hash,
            'customerName' => '志-測試',
            'phone' => '96832636',
            'orderCode' => '',
            'datetime' => '2024-11-26',
            'email' => '<EMAIL>',
            'state' => '0',
            'donationCode' => '0',
            'taxType' => 0,
            'companyCode' => '0',
            'freeAmount' => 0,
            'zeroAmount' => 0,
            'sales' => 95,
            'amount' => 5,
            'totalFee' => 100,
            'content' => 'test',
            'items' => [['name' => '測試商品', 'money' => 1, 'number' => 1, 'remark' => '']]];
        Log::info('payload >> ' . json_encode($postData));
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postData),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        Log::info('invouice rs >> ' . $response);
        curl_close($curl);
        echo $response;
    }

    public function outputxls(Request $request)
    {
        //         $data = [
        //             ['id' => '1', 'date' => '2024-12-16', 'cartype' => 'VAN', 'time' => '15:30', 'detail' => '台北市', 'passenger' => '陳先生', 'amount' => 100, 'note' => '測試'],
        //             ['id' => '2', 'date' => '2024-12-26', 'cartype' => '轎車', 'time' => '12:30', 'detail' => '老爺商務旅館(光復路二段167號)>桃園機場', 'passenger' => '裡小姐', 'amount' => 1200, 'note' => '測試 222'],

        //         ];
        //         $spreadsheet = new Spreadsheet();
        //         $sheet       = $spreadsheet->getActiveSheet();
        //         $sheet->getPageSetup()->setPaperSize(PageSetup::PAPERSIZE_A4);
        //         $imagePath = storage_path('app/public/images/zs_logo2.jpg');

        //         $drawing = new Drawing();
        //         $drawing->setPath($imagePath);

        //         // 獲取圖片尺寸 (假設使用 GD 庫)
        //         list($width, $height) = getimagesize($imagePath);
        //         // A4 頁面尺寸 (mm)
        //         $pageWidth = 210;
        //         $pageHeight = 297;
        //         // 设置页边距（单位：英寸）
        //         // $sheet->getPageMargins()->setTop(0.5);
        //         $sheet->getPageMargins()->setRight(0.5);
        //         $sheet->getPageMargins()->setLeft(0.5);
        //         // $sheet->getPageMargins()->setBottom(0.5);
        //         // 計算縮放比例 (以寬度為基準，確保圖片不會超出頁面)
        //         $scale = 0.55; //$pageWidth / $width;
        //         $newHeight = $height * $scale;

        //         $drawing->setWidth($pageWidth);
        //         $drawing->setHeight($newHeight);
        //         $drawing->setName('Logo');
        //         $drawing->setDescription('Paid');
        //         $drawing->setPath($imagePath); // put your path and image here
        //         $drawing->setCoordinates('A1');
        //         $drawing->setOffsetX(0);
        //         // $drawing->setWorksheet($spreadsheet->getActiveSheet());
        //         $drawing->setWorksheet($sheet);

        //         $sheet->setCellValue('A5', '公 司 : ');
        //         $sheet->setCellValue('A6', '聯絡人 : ');
        //         $sheet->setCellValue('A7', '電 話 : ');
        //         $sheet->setCellValue('A8', '地 址 : ');
        //         $sheet->setCellValue('B8', '300新竹市光復路二');

        //         // 设置表头
        //         $headers = ['序', '日期', '車型', '時間', '明細', '乘車人', '金額', '備註'];
        //         $sheet->fromArray($headers, null, 'A10');

        //         $row = 11;
        //         $total = 0;
        //         // 遍历数据并写入 Excel
        //         foreach ($data as $item) {
        //             $sheet->fromArray($item, null, 'A' . $row);
        //             $total += $item['amount'];
        //             $row++;
        //         }
        //         // 在最后一行下方添加横线
        //         $lastRow = $row;
        //         $sheet->getStyle('A'.$lastRow.':H'.$lastRow)
        //             ->getBorders()
        //             ->getBottom()
        //                 ->setBorderStyle(Border::BORDER_THIN);
        //         // 自动调整列宽
        //         // foreach (range('A', 'H') as $columnID) {
        //         //     $sheet->getColumnDimension($columnID)->setAutoSize(true);
        //         // }
        //         $headerRow = 10; // 假设 Header 行是第 11 行
        //         $sheet->getStyle('A'.$headerRow.':H'.$headerRow)
        //             ->getAlignment()
        //             // ->setWrapText(true)
        //             ->setHorizontal(Alignment::HORIZONTAL_CENTER)
        //             ->setVertical(Alignment::VERTICAL_CENTER);
        //         $sheet->getStyle('E10:E'.$lastRow)
        //             ->getAlignment()
        //             ->setWrapText(true);
        //         // 添加小计行
        //         $sheet->setCellValue('F'.($lastRow + 1), '小計');
        //         $sheet->setCellValue('G'.($lastRow + 1), $total);

        //         // 添加稅額行
        //         $tax = $total * 0.05;
        //         $sheet->setCellValue('F'.($lastRow + 2), '稅額');
        //         $sheet->setCellValue('G'.($lastRow + 2), $tax);

        //         // 添加總計行
        //         $grandTotal = $total + $tax;
        //         $sheet->setCellValue('F'.($lastRow + 3), '總計');
        //         $sheet->setCellValue('G'.($lastRow + 3), $grandTotal);
        //         // 添加 Header 行的上下边框
        //         $styleArray = [
        //             'borders' => [
        //                 'top' => ['borderStyle' => Border::BORDER_THIN],
        //                 'bottom' => ['borderStyle' => Border::BORDER_THIN],
        //             ],
        //         ];
        //         $sheet->getStyle('A'.$headerRow.':H'.$headerRow)->applyFromArray($styleArray);
        //         $sheet->getColumnDimension('A')->setWidth(4);
        //         $sheet->getColumnDimension('B')->setWidth(10);
        //         $sheet->getColumnDimension('C')->setWidth(8);  // 車型
        //         $sheet->getColumnDimension('D')->setWidth(8);  // 時間
        //         $sheet->getColumnDimension('E')->setWidth(34);  // 明細
        //         $sheet->getColumnDimension('F')->setWidth(10);  // 乘車人
        //         $sheet->getColumnDimension('G')->setWidth(10);  // 金額
        //         $sheet->getColumnDimension('H')->setWidth(10);  // 備註
        // // Log::info('Hello World');
        //         $writer = new Xlsx($spreadsheet);
        //         $writer->save('存放於主機檔名.xlsx');
    }

    public function getcitys(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required',
            // 'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $user =
        $results = DB::table('airport_prices')
            ->select(
                'to_area_id',
                DB::raw('MAX(to_area_name) as to_area_name'),
                'to_city_id',
                DB::raw('MAX(to_city_name) as to_city_name'),
                'to_district_id',
                DB::raw('MAX(to_district_name) as to_district_name')
            )
            ->where('vendor_id', $input['vendor_id'])
            ->where('status', 0)
            ->groupBy('to_area_id', 'to_city_id', 'to_district_id')
            ->get();

        $toAreaIds = [];
        $toCityIds = [];
        $toDistrictIds = [];

        foreach ($results as $result) {
            $toAreaIds[$result->to_area_id] = $result->to_area_name;
            $toCityIds[$result->to_city_id] = $result->to_city_name;
            $toDistrictIds[$result->to_district_id] = $result->to_district_name;
        }

        $data = [
            'province_list' => $toAreaIds,
            'city_list' => $toCityIds,
            'county_list' => $toDistrictIds,
        ];
        return $this->sendResponse($data, '成功取得城市資料!');
    }

    public function getquote(Request $request)
    {
        $tmpPrice = 0;
        $tmpPriceArray = '';
        $bagStr = '';
        $otherStr = '';
        $message = '';
        $quotation = '';
        $lang = 'zh_TW';
        $messageTitle = '您的即時詢價資訊如下:';
        $input = $request->all();
        $validator = Validator::make($input, [
            'order_type' => 'required',
            'car_type' => 'required',
            'quo_type' => 'required',
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // Log::info("input->> -", $input);
        $lang = $input['lang'] ?? 'zh_TW';
        if ($lang == 'en-US') {
            $messageTitle = 'Your instant inquiry information is as follows:';
            $messageCarType = 'Car Type:';
            $messageOrderType = 'Order Type:';
            $messageRoute = 'Route:';
            $messageNumberOfPeople = 'Number of people:';
            $messageOtherService = 'Other Service:';
            $messageBags = 'Bags and Children Seat: ';
        } else {
            $messageTitle = '您的即時詢價資訊如下:';
            $messageCarType = '車型:';
            $messageOrderType = '類別:';
            $messageRoute = '往來地點:';
            $messageNumberOfPeople = '乘車人數:';
            $messageOtherService = '其他服務:';
            $messageBags = '行李與兒童座椅: ';
        }
        Log::info('Before calling getAddPrice, input is:', [
            'order_type' => $input['order_type'],
            'type' => gettype($input['order_type'])
        ]);

        $tmpPriceArray = $this->getAddPrice('q', $input);

        Log::info('After calling getAddPrice, tmpPriceArray is:', [
            'tmpPriceArray' => $tmpPriceArray,
            'tmpTotal' => $tmpPriceArray['tmpTotal'] ?? 'not set'
        ]);

        if ($input['order_type'] == '0' || $input['order_type'] == '1') {
            // var_dump($tmpPrice);die;
            Log::info('input->> -', $tmpPriceArray);

            if (!empty($tmpPriceArray['price'])) {
                $bagServices = empty($tmpPriceArray['bagStr']) ? '' : PHP_EOL . $messageBags . $tmpPriceArray['bagStr'] . PHP_EOL;
                $OtherServices = empty($tmpPriceArray['otherStr']) ? '' : $messageOtherService . $tmpPriceArray['otherStr'] . PHP_EOL;
                $message = $messageTitle . PHP_EOL
                    . $messageOrderType . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . $messageCarType . $this->getCarTypeChinese($lang, $input['car_type']) . PHP_EOL
                    . $messageRoute . $this->getLocationChinese($input['airport']) . '-' . $tmpPriceArray['price']['to_city_name'] . ' ' . $this->getLocationChinese($input['to_district_id']) . PHP_EOL
                    . $messageNumberOfPeople . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                $quotation = Quotation::create([
                    'vendor_id' => $input['vendor_id'],
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    'location_from_id' => $tmpPriceArray['price']['from_district_id'],
                    'location_from_name' => $tmpPriceArray['price']['from_district_name'],
                    'location_area_id' => $tmpPriceArray['price']['to_area_id'],
                    'location_area_name' => $tmpPriceArray['price']['to_area_name'],
                    'location_city_id' => $tmpPriceArray['price']['to_city_id'],
                    'location_city_name' => $tmpPriceArray['price']['to_city_name'],
                    'location_district_id' => $tmpPriceArray['price']['to_district_id'],
                    'location_district_name' => $tmpPriceArray['price']['to_district_name'],
                    'num_of_people' => $input['num_of_people'],
                    'num_of_bags' => $input['num_of_bags'],
                    'child_seat' => $input['child_seat'],
                    'booster_pad' => $input['booster_pad'],
                    'other_service' => $input['other_service'],
                    'total' => (int) $tmpPriceArray['tmpTotal'],
                    'created_at' => now(),
                    'note' => $input['note'],
                ]);
                // $quotation['quotation_id'] = $quotation->id;
            } else {
                $quotation = [
                    'total' => 0
                ];
                if ($lang == 'en-US') {
                    $message = 'Currently no quotation for this area!' . PHP_EOL . 'We will contact you later!';
                } else {
                    $message = '目前查無該地區報價或符合車型!' . PHP_EOL . '後續將由專人與您聯繫!';
                }
            }
            return $this->sendResponse($quotation, $message);
        } else if ($input['order_type'] == '2') {
            // 包車旅遊
            // $tmpPriceArray = $this->getAddPrice('q',$input);
            $quotation = Quotation::create([
                'vendor_id' => $input['vendor_id'],
                'user_line_id' => $input['line_id'],
                'order_type' => $input['order_type'],
                'car_type' => $input['car_type'],
                'tour_type' => $input['tour_car'],
                // 'location_area_id' => $result->to_area_id,
                'location_city_id' => $input['to_city_id'],
                'location_district_id' => $input['to_district_id'],
                // 'location_area_name' => $result->to_area_name,
                'location_city_name' => $this->getLocationChinese($input['to_city_id'], 'city'),
                'location_district_name' => $this->getLocationChinese($input['to_district_id']),
                'total' => (int) $tmpPriceArray['tmpTotal'],
                'created_at' => now(),
                'note' => $input['note'],
            ]);
            $message = $tmpPriceArray['bagStr'];
            return $this->sendResponse($quotation, $message);
            // $result = 500
        }
        // $result = static::getQuote($data);
        // $data['price'] = $result->price ?? 0;
    }

    public function carorder(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            // 'quotation_id' => 'required',
            'line_id' => 'required',
            'passenger_name' => 'required',
            'passenger_mobile' => 'required',
            'passenger_address' => 'required',
            'appointment_date' => 'required',
            'flightno' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $lang = $input['lang'] ?? 'zh_TW';
        if ($lang == 'en-US') {
            $messageTitle = 'Your instant inquiry information is as follows:';
            $messageCarType = 'Car Type:';
            $messageOrderType = 'Order Type:';
            $messageRoute = 'Route:';
            $messageNumberOfPeople = 'Number of people:';
            $messageOtherService = 'Other Service:';
            $messageBags = 'Bags and Children Seat: ';
        } else {
            $messageTitle = '您的即時詢價資訊如下:';
            $messageCarType = '車型:';
            $messageOrderType = '類別:';
            $messageRoute = '往來地點:';
            $messageNumberOfPeople = '乘車人數:';
            $messageOtherService = '其他服務:';
            $messageBags = '行李與兒童座椅: ';
        }
        $tmpPriceArray = $this->getAddPrice('o', $input);
        Log::info('tmpPriceArray->> -', $tmpPriceArray);

        // Safely extract price details
        $priceData = $tmpPriceArray['price'];
        $locationFromName = null;
        $locationAreaId = null;
        $locationAreaName = null;
        $locationCityIdForPrice = null;
        $locationCityNameForPrice = null;
        $locationDistrictIdForPrice = null;
        $locationDistrictNameForPrice = null;

        if (is_object($priceData) || is_array($priceData)) {
            $locationFromName = $priceData['from_district_name'] ?? null;
            $locationAreaId = $priceData['to_area_id'] ?? null;
            $locationAreaName = $priceData['to_area_name'] ?? null;
            $locationCityIdForPrice = $priceData['to_city_id'] ?? null;
            $locationCityNameForPrice = $priceData['to_city_name'] ?? null;
            $locationDistrictIdForPrice = $priceData['to_district_id'] ?? null;
            $locationDistrictNameForPrice = $priceData['to_district_name'] ?? null;
        }

        // 檢查是否需要應用折扣（service_type=1 或 service_type=2）
        if (isset($input['service_type']) && ($input['service_type'] == 1 || $input['service_type'] == 2)) {
            $originalPrice = $tmpPriceArray['tmpTotal'];
            $tmpPriceArray['tmpTotal'] = (int) $tmpPriceArray['tmpTotal'] * 0.9;
            Log::info('Applied 10% discount:', [
                'service_type' => $input['service_type'],
                'original_price' => $originalPrice,
                'discounted_price' => $tmpPriceArray['tmpTotal']
            ]);
        }

        if ($input['quotation_id'] == 0 || $input['quotation_id'] == null) {
            if ($input['order_type'] == 0 || $input['order_type'] == 1) {
                $bagServices = empty($tmpPriceArray['bagStr']) ? '' : PHP_EOL . $messageBags . $tmpPriceArray['bagStr'] . PHP_EOL;
                $OtherServices = empty($tmpPriceArray['otherStr']) ? '' : $messageOtherService . $tmpPriceArray['otherStr'] . PHP_EOL;
                $PassengerServices = empty($tmpPriceArray['passengerStr']) ? PHP_EOL : PHP_EOL . $tmpPriceArray['passengerStr'];
                $message = $PassengerServices
                    . $messageOrderType . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . $messageCarType . $this->getCarTypeChinese($lang, $input['car_type']) . PHP_EOL
                    // '往來地點:'.$this->getLocationChinese($input['airport']).'-'.$tmpPriceArray['price']['to_city_name'].' '.$this->getLocationChinese($input['to_district_id']).PHP_EOL.
                    . $messageNumberOfPeople . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                $quotation = Quotation::create([
                    'status' => 1,
                    'vendor_id' => $input['vendor_id'],
                    'passenger_name' => $input['passenger_name'],
                    'passenger_mobile' => $input['passenger_mobile'],
                    'passenger_address' => $input['passenger_address'],
                    'pay_type' => $input['pay_type'],
                    'note' => $input['note'],
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    'location_from_id' => $input['airport'],  // Assuming $input['airport'] is the correct source for from_id
                    'location_from_name' => $locationFromName,
                    'location_area_id' => $locationAreaId,
                    'location_area_name' => $locationAreaName,
                    'location_city_id' => $locationCityIdForPrice,
                    'location_city_name' => $locationCityNameForPrice,
                    'location_district_id' => $locationDistrictIdForPrice,
                    'location_district_name' => $locationDistrictNameForPrice,
                    'num_of_people' => $input['num_of_people'],
                    'num_of_bags' => $input['num_of_bags'],
                    'child_seat' => $input['child_seat'],
                    'booster_pad' => $input['booster_pad'],
                    'other_service' => $input['other_service'],
                    'appointment_date' => $input['appointment_date'],
                    'flightno' => $input['flightno'],
                    'updated_at' => now(),
                    'total' => (int) $tmpPriceArray['tmpTotal']
                ]);
                return $this->sendResponse($quotation, $message);
            } else {
                // 包車旅遊
                $tmpPriceArray = $this->getAddPrice('q', $input);
                $quotation = Quotation::create([
                    'vendor_id' => $input['vendor_id'],
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    // 'location_area_id' => $result->to_area_id,
                    'location_city_id' => $input['to_city_id'],
                    'location_district_id' => $input['to_district_id'],
                    // 'location_area_name' => $result->to_area_name,
                    'location_city_name' => $this->getLocationChinese($input['to_city_id'], 'city'),
                    'location_district_name' => $this->getLocationChinese($input['to_district_id']),
                    'total' => (int) $tmpPriceArray['tmpTotal'],
                    'created_at' => now(),
                    'note' => $input['note'],
                    'appointment_date' => $input['appointment_date'],
                    'flightno' => $input['flightno'],
                ]);
                $message = $tmpPriceArray['passengerStr'] . PHP_EOL . $tmpPriceArray['bagStr'];
                return $this->sendResponse($quotation, $message);
            }
        }
        // $input['from_district_id'] = empty($input['airport']) ? '' : $input['airport'];
        $quotation = Quotation::where('id', $input['quotation_id'])
            ->where('user_line_id', $input['line_id'])
            ->first();
        // Log::info("quotation->> -", [$quotation]);
        // var_dump($aa);die;
        if ($quotation) {
            // 折扣邏輯已經在前面處理過了，這裡不需要重複處理
            $quotation->status = 1;
            $quotation->passenger_name = $input['passenger_name'];
            $quotation->passenger_mobile = $input['passenger_mobile'];
            $quotation->passenger_address = $input['passenger_address'];
            $quotation->pay_type = $input['pay_type'];
            $quotation->note = $input['note'];
            $quotation->appointment_date = $input['appointment_date'];
            $quotation->flightno = $input['flightno'];
            $quotation->user_line_id = $input['line_id'];
            $quotation->order_type = $input['order_type'];
            $quotation->car_type = $input['car_type'];
            $quotation->tour_type = $input['tour_car'];
            $quotation->service_type = $input['service_type'];
            if ($input['order_type'] == 2) {
                $quotation->location_city_id = $input['to_city_id'];
                $quotation->location_city_name = $this->getLocationChinese($input['to_city_id'], 'city');
                $quotation->location_district_id = $input['to_district_id'];
                $quotation->location_district_name = $this->getLocationChinese($input['to_district_id']);
            } else {
                $quotation->location_from_id = $input['airport'];  // Assuming $input['airport'] is the correct source for from_id
                $quotation->location_from_name = $locationFromName;
                $quotation->location_area_id = $locationAreaId;
                $quotation->location_area_name = $locationAreaName;
                $quotation->location_city_id = $locationCityIdForPrice;
                $quotation->location_city_name = $locationCityNameForPrice;
                $quotation->location_district_id = $locationDistrictIdForPrice;
                $quotation->location_district_name = $locationDistrictNameForPrice;
            }
            $quotation->num_of_people = $input['num_of_people'];
            $quotation->num_of_bags = $input['num_of_bags'];
            $quotation->child_seat = $input['child_seat'];
            $quotation->booster_pad = $input['booster_pad'];
            $quotation->other_service = $input['other_service'];
            $quotation->updated_at = now();
            $quotation->total = (int) $tmpPriceArray['tmpTotal'];
            $quotation->note = $input['note'];
            $quotation->save();
            $bagServices = empty($tmpPriceArray['bagStr']) ? PHP_EOL : PHP_EOL . '行李與兒童座椅: ' . $tmpPriceArray['bagStr'] . PHP_EOL;
            $OtherServices = empty($tmpPriceArray['otherStr']) ? PHP_EOL : PHP_EOL . '其他服務: ' . $tmpPriceArray['otherStr'];
            $PassengerServices = empty($tmpPriceArray['passengerStr']) ? PHP_EOL : PHP_EOL . $tmpPriceArray['passengerStr'] . PHP_EOL;
            if ($input['order_type'] == 2) {
                if ($input['tour_car'] == 1) {
                    // 一日遊
                    $message = '您的預約資訊如下:' . PHP_EOL . $PassengerServices
                        . '類別:' . '包車旅遊一日遊' . PHP_EOL
                        . '車型:' . $this->getCarTypeChinese($lang, $input['car_type']) . PHP_EOL
                        . '上車地點:' . $this->getLocationChinese($input['to_city_id'], 'city') . '-' . $this->getLocationChinese($input['to_district_id']);
                } else {
                    $message = '您的預約資訊如下:' . PHP_EOL . $PassengerServices
                        . '類別:' . '包車旅遊半日遊' . PHP_EOL
                        . '車型:' . $this->getCarTypeChinese($lang, $input['car_type']) . PHP_EOL
                        . '上車地點:' . $this->getLocationChinese($input['to_city_id'], 'city') . '-' . $this->getLocationChinese($input['to_district_id']);
                }
                if (!empty($input['note'])) {
                    $message .= PHP_EOL . '備註:' . $input['note'];
                }
            } else {
                if ($input['service_type'] == 0) {
                    $servicestr = '';
                } else if ($input['service_type'] == 1) {
                    $servicestr = '優惠類別: 特約社區(9折)' . PHP_EOL;
                } else if ($input['service_type'] == 2) {
                    $servicestr = '優惠類別: 特約廠商(9折)' . PHP_EOL;
                }
                $message = $PassengerServices . $servicestr
                    . '類別:' . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . '車型:' . $this->getCarTypeChinese($lang, $input['car_type']) . PHP_EOL
                    // '往來地點:'.$this->getLocationChinese($input['airport']).'-'.$tmpPriceArray['price']['to_city_name'].' '.$this->getLocationChinese($input['to_district_id']).PHP_EOL.
                    . '乘車人數:' . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                if (!empty($input['note'])) {
                    $message .= PHP_EOL . '備註:' . $input['note'];
                }
            }
            return $this->sendResponse($quotation, $message);
        } else {
            $data['total'] = 0;
            return $this->sendResponse($data, '目前查無該訂單!');
        }
    }

    public function getquobyorder(Request $request)
    {
        /**
         * LINE liff 取得報價
         * firbase app
         */
        $input = $request->all();
        $validator = Validator::make($input, [
            'quotation_id' => 'required',
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $result = Quotation::where('id', $input['quotation_id'])
            ->where('user_line_id', $input['line_id'])
            ->first();
        if ($result) {
            return $this->sendResponse($result, '成功取得資料.');
        } else {
            $data['price'] = 0;
            return $this->sendResponse($data, '目前查無該訂單!');
        }
    }

    public function getOrderTypeChinese($lang, $order_type)
    {
        if ($order_type == '0') {
            if ($lang == 'en') {
                return 'Pick up';
            } else {
                return '接機';
            }
        } else if ($order_type == '1') {
            if ($lang == 'en') {
                return 'Drop off';
            } else {
                return '送機';
            }
        } else if ($order_type == '2') {
            if ($lang == 'en') {
                return 'Charter Tour';
            } else {
                return '包車旅遊';
            }
        }
    }

    public function getCarTypeChinese($lang, $car_type)
    {
        if ($car_type == '5') {
            if ($lang == 'en') {
                return '5-seater Car';
            } else {
                return '五人座車小客車';
            }
        } else if ($car_type == '51') {
            if ($lang == 'en') {
                return '5-SUV';
            } else {
                return '五人座車休旅車';
            }
        } else if ($car_type == '7') {
            if ($lang == 'en') {
                return '7-SUV';
            } else {
                return '七人座車';
            }
        } else if ($car_type == '9') {
            if ($lang == 'en') {
                return '9-seater';
            } else {
                return '九人座車';
            }
        }
    }

    public function getLocationChinese($location_id, $city = null)
    {
        Log::info('FF Location ID CITY 123:: ' . $location_id . ' :: ' . $city);
        if ($location_id == '110903') {
            return '桃園機場(Taoyuan)';
        } else if ($location_id == '110900') {
            return '松山機場(Taipei)';
        } else if ($location_id == '120901') {
            return '台中機場(Taichung)';
        } else {
            if ($city == 'city') {
                Log::info('Location ID CITY:: ' . $location_id);
                return City::where('city_code', $location_id)->first()->city;
            } else {
                Log::info('Location ID:: ' . $location_id);
                return City::where('district_code', $location_id)->first()->city;
            }
        }
    }

    public function getAddPrice($type, $input)
    {
        Log::info('getAddPrice method started with input:', [
            'type' => $type,
            'order_type' => $input['order_type'],
            'order_type_type' => gettype($input['order_type'])
        ]);

        $tmpTotal = 0;
        $tmpPrice = 0;
        $message = '';
        $otherStr = '';
        $bagStr = '';
        $passengerStr = '';
        $basePrice = [];
        // $input['location_from_id'] = $input['airport'];
        $tmpOrderType = ($input['order_type'] == '1') ? '0' : $input['order_type'];
        Log::info('input->> 00  -', $input);
        if ($input['lang'] == 'en-US') {
            $message = 'The price is calculated as follows:';
            $messageUpLocation = 'Up Location: ';
            $messageTourOneDay = 'Tour one day: ';
            $messageTourHalfDay = 'Tour half day: ';
            $messageNote = 'Note:';
            $messageUpTime = 'Up Time: ';
            $messageArrivalAirport = 'Arrival Airport: ';
            $messageDown = 'Down: ';
            $messageUp = 'Up: ';
            $messageFlightTime = 'Flight Time: ';
            $messagetakeAirport = 'Take Airport: ';
            $messagePassengerName = 'Passenger Name: ';
            $messagePassengerMobile = 'Passenger Mobile: ';
            $messageLocation = 'Location:';
            $messageFlightNo = 'Flight Information:';
            $messagePayType = 'Pay Type:';
        } else {
            $message = '價格計算如下:';
            $messageUpLocation = '上車區域: ';
            $messageTourOneDay = '包車一日遊: ';
            $messageTourHalfDay = '包車一日遊: ';
            $messageNote = '備註:';
            $messageUpTime = '上車時間: ';
            $messageArrivalAirport = '抵達機場: ';
            $messageDown = '下車';
            $messageUp = '上車';
            $messageFlightTime = '班機時間: ';
            $messagetakeAirport = '接機機場: ';
            $messagePassengerName = '乘客姓名: ';
            $messagePassengerMobile = '乘客手機: ';
            $messageLocation = '地點:';
            $messageFlightNo = '航班資訊:';
            $messagePayType = '付款方式:';
        }
        if ($input['order_type'] == '0' || $input['order_type'] == '1') {
            $basePrice = AirportPrice::where('car_type', $input['car_type'])
                ->where('order_type', $tmpOrderType)
                ->where('vendor_id', $input['vendor_id'])
                ->where('from_district_id', $input['airport'])
                ->where('to_city_id', $input['to_city_id'])
                ->where('to_district_id', $input['to_district_id']);

            // 方法1：使用 toSql() 輸出純SQL語句
            Log::info('SQL Query:', [
                'sql' => $basePrice->toSql(),
                'bindings' => $basePrice->getBindings(),
                'input' => $input
            ]);

            $basePrice = $basePrice->first();
            if (!$basePrice) {
                return [
                    'price' => null,
                    'tmpTotal' => $tmpTotal,
                    'otherStr' => $otherStr,
                    'bagStr' => $bagStr,
                    'passengerStr' => $passengerStr,
                ];
            }
            // 檢查 order_type 的值和類型
            Log::info('order_type value and type check:', [
                'order_type' => $input['order_type'],
                'type' => gettype($input['order_type']),
                'is_string_zero' => $input['order_type'] === '0',
                'is_numeric_zero' => $input['order_type'] === 0,
                'raw_input' => $input
            ]);

            if ($input['order_type'] === '0') {
                // 接機加200
                // Log::info('Before adding 200, tmpPrice is: ' . $tmpPrice);
                $tmpPrice += 200;
                // Log::info('After adding 200, tmpPrice is now: ' . $tmpPrice);
            }
            if ($input['child_seat'] > 0 && $input['child_seat'] < 3) {
                $tmpPrice += $input['child_seat'] * 100;
            }
            // 增高墊 +200
            if (!empty($input['booster_pad']) && $input['booster_pad'] > 0 && $input['booster_pad'] < 3) {
                $tmpPrice += $input['booster_pad'] * 200;
            }

            if (!empty($input['other_service'])) {
                $othrtPrice = 0;
                foreach ($input['other_service'] as $service) {
                    if ($service == '1') {
                        if ($input['vendor_id'] == '4') {
                            $othrtPrice += 100;
                            $otherStr .= '舉牌(+100),';
                        } else if ($input['vendor_id'] == '7') {
                            $othrtPrice += 231;
                            $otherStr .= '舉牌(+231),';
                        }
                    } elseif ($service == '3') {
                        $othrtPrice += 100;
                        $otherStr .= '停靠點(一個點+100),';
                    } elseif ($service == '2') {
                        if($input['car_type'] == '5' || $input['car_type'] == '7'){
                            $othrtPrice += 100;
                            $otherStr .= '夜間加成(+100),';
                        }else {
                            $othrtPrice += 200;
                            $otherStr .= '夜間加成(+200),';
                        }
                        // $othrtPrice += 200;
                        // $otherStr .= '夜間加成(+200),';
                    }
                }
                $tmpPrice += $othrtPrice;
            }
            Log::info('Final price calculation:', [
                'basePrice' => $basePrice->price,
                'tmpPrice' => $tmpPrice,
                'order_type' => $input['order_type']
            ]);
            $tmpTotal = $basePrice->price + $tmpPrice;
            Log::info('Final total price: ' . $tmpTotal);
            if (!empty($input['num_of_bags'])) {
                $bagStr .= '行李數:' . $input['num_of_bags'] . '件,';
            }
            if (!empty($input['child_seat'])) {
                $bagStr .= '兒童座椅:' . $input['child_seat'] . '件,';
            }
            if (!empty($input['booster_pad'])) {
                $bagStr .= '增高墊:' . $input['booster_pad'] . '件,';
            }
        } else {
            // 包車旅遊
            if ($input['tour_car'] == 1) {
                // 一日遊
                switch ($input['car_type']) {
                    case '5':
                        if ($input['vendor_id'] == '10') {
                            $tmpTotal = 5000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 4000;
                        }
                        break;
                    case '51':
                        $tmpTotal = 5000;
                        break;
                    case '7':
                        $tmpTotal = 4000;
                        break;
                    case '9':
                        if ($input['vendor_id'] == '10') {
                            $tmpTotal = 7000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 5500;
                        }
                        break;
                }
                $bagStr = $messageUpLocation . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id'])
                    . PHP_EOL . $messageTourOneDay . $tmpTotal;
                if (!empty($input['note'])) {
                    $bagStr .= PHP_EOL . $messageNote . $input['note'];
                }
            } else {
                // 半日遊
                switch ($input['car_type']) {
                    case '5':
                        if ($input['vendor_id'] == '10') {
                            $tmpTotal = 2500;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 2000;
                        }
                        break;
                    case '51':
                        $tmpTotal = 2500;
                        break;
                    case '7':
                        $tmpTotal = 2500;
                        break;
                    case '9':
                        if ($input['vendor_id'] == '10') {
                            $tmpTotal = 3500;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 2500;
                        }
                        break;
                }
                $bagStr = $messageUpLocation . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id'])
                    . PHP_EOL . $messageTourHalfDay . $tmpTotal;
                if (!empty($input['note'])) {
                    $bagStr .= PHP_EOL . $messageNote . $input['note'];
                }
            }
        }
        if (!empty($input['passenger_name'])) {
            $tmpUp = '';
            $tmpTime = $messageUpTime;
            $tmpAir = $messageArrivalAirport;
            if ($input['order_type'] == '0') {
                $tmpUp = $messageDown;
                $tmpTime = $messageFlightTime;
                $tmpAir = $messagetakeAirport;
            } else if ($input['order_type'] == '1' || $input['order_type'] == '2') {
                $tmpUp = $messageUp;
            }
            if ($input['order_type'] == '0' || $input['order_type'] == '1') {
                $tmpAirport = $tmpAir . $this->getLocationChinese($input['airport']);
            } else {
                $tmpAirport = '';
            }
            // $passengerStr = $tmpLocation.'區域: '.$this->getLocationChinese($input['to_city_id']).' '.$this->getLocationChinese($input['to_district_id']).PHP_EOL;
            $passengerStr .= $messagePassengerName . $input['passenger_name'] . PHP_EOL;
            $passengerStr .= $messagePassengerMobile . ($input['passenger_mobile'] ?? '') . PHP_EOL;
            $passengerStr .= empty($tmpAirport) ? '' : $tmpAirport . PHP_EOL;
            $passengerStr .= $tmpUp . $messageLocation . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']) . ' ' . ($input['passenger_address'] ?? '') . PHP_EOL;
            $passengerStr .= $tmpTime . ($input['appointment_date'] ?? '') . PHP_EOL;
            if ($input['order_type'] != '2') {
                $passengerStr .= $messageFlightNo . ($input['flightno'] ?? '') . PHP_EOL;
            }
            $passengerStr .= $messagePayType . ($input['pay_type'] ?? '') . PHP_EOL;
            $passengerStr .= '=================' . PHP_EOL;
        }
        // Log::info("input basePrice 00 ->> - ", $basePrice->toArray());
        if (!$basePrice) {
            $basePrice = [
                'from_district_name' => '',
                'to_area_id' => '',
                'to_area_name' => '',
                'to_city_id' => '',
                'to_city_name' => '',
                'to_district_name' => ''
            ];
        }

        $result = [
            'price' => $basePrice,
            'tmpTotal' => $tmpTotal,
            'otherStr' => $otherStr,
            'bagStr' => $bagStr,
            'passengerStr' => $passengerStr,
        ];

        Log::info('getAddPrice method finished with result:', [
            'tmpTotal' => $tmpTotal,
            'order_type' => $input['order_type']
        ]);

        return $result;
    }
}
