<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Car;
use App\Models\User;
use App\Models\Driver;
use App\Models\Mytest;
use App\Models\Vendor;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\Department;
use App\Traits\CreateImage;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Validator;

class DispatchController extends Controller
{
    use CreateImage;
    public function list(Request $request)
    {
        $input = $request->all();
        $dispatch = [];
        $validator = Validator::make($input, [
            'line_id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        if(!$driver){
            return $this->sendError('查無此司機!請與車行洽詢!');
        }
        if($input['search'] == 'today'){
            $dispatch = Dispatch::with('customer')
                            ->where('driver_id', '=', $driver->id)
                            ->where('deleted_at', '=', null)
                            ->whereDate('start_date', '=', Carbon::now()->toDateString())
                            ->orderBy('start_date', 'asc')->get();
        }else if($input['search'] == 'tomorrow'){
            $dispatch = Dispatch::with('customer')
                            ->where('driver_id', '=', $driver->id)
                            ->where('deleted_at', '=', null)
                            ->whereDate('start_date', '=', Carbon::now()->addDay()->toDateString())
                            ->orderBy('start_date', 'asc')->get();
        }
        else if($input['search'] == 'week'){
            $dispatch = Dispatch::with('customer')
                            ->where('driver_id', '=', $driver->id)
                            ->where('deleted_at', '=', null)
                            ->whereDate('start_date', '>=', Carbon::now()->startOfWeek()->toDateString())
                            ->whereDate('start_date', '<=', Carbon::now()->endOfWeek()->toDateString())
                            ->orderBy('start_date', 'desc')->get();
        }else if($input['search'] == 'month'){
            $dispatch = Dispatch::with('customer')
                            ->where('driver_id', '=', $driver->id)
                            ->where('deleted_at', '=', null)
                            ->whereDate('start_date', '>=', Carbon::now()->startOfMonth()->toDateString())
                            ->whereDate('start_date', '<=', Carbon::now()->endOfMonth()->toDateString())
                            ->orderBy('start_date', 'desc')->get();
        }else if($input['search'] == 'last30'){
            $dispatch = Dispatch::with('customer')
                            ->where('driver_id', '=', $driver->id)
                            ->where('deleted_at', '=', null)
                            ->orderBy('start_date', 'desc')
                            ->take(200)->get();
        }else{
            $dispatch = Dispatch::with('customer')->where('driver_id', '=', $driver->id)->orderBy('start_date', 'desc')->get();
        }
        if(!$dispatch){
            return $this->sendError('查無此車輛!請與車行洽詢!');
        }
        foreach ($dispatch as $key => $value) {
            if($value->vendor_id == 1){
                $dispatch[$key]->showUpButton = false;
            }else{
                $dispatch[$key]->showUpButton = true;
            }
            // 計算出發時間和現在之間的差距
            $startTime = Carbon::parse($value->start_date);
            $currentTime = Carbon::now();

            // 判斷是否在3小時以內
            $minutesDifference  = $currentTime->diffInMinutes($startTime, false);
            // Log::info('$minutesDifference >> ', [$minutesDifference]);
            if($value->vendor_id >= 6 && $minutesDifference <= 180 && $minutesDifference >= 0){
                $dispatch[$key]->showStartUpButton = true;
            }else{
                $dispatch[$key]->showStartUpButton = false;
            }
            if($value->vendor_id == 6){
                $dispatch[$key]->showFinishButton = true;
                $dispatch[$key]->signaHight = "100%";
            }else{
                $dispatch[$key]->showFinishButton = false;
                $dispatch[$key]->signaHight = "60%";
            }
            if(!empty($value->pickup_and_dropoff_location) && $value->pickup_and_dropoff_location !== '[]'){
                $dispatch[$key]->tag = "多點";
            }
        }

        // $dispatch->customer_name = $dispatch->toArray();
        return $this->sendResponse($dispatch, '查詢成功!');
    }

    public function uploadsignature(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'dispatch_no' => 'required',
            'line_id' => 'required',
            'pay_type' => 'required',
            'imgBase64' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $signatureData = $request->input('imgBase64');
        if(empty($signatureData)){
            $signature_path = '';
            // return  $this->sendResponse('message', 'add successfully.');
        }else{
            $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
            $signatureData = str_replace(' ', '+', $signatureData);
            $signature = base64_decode($signatureData);
            $fileName = 'st_' . $input['dispatch_no'] . '.png';
            Storage::put('public/signatures/' . $fileName, $signature);
            $signature_path = storage_path('app/public/signatures/' . $fileName);
            $updateData = Dispatch::where('dispatch_no', '=', $input['dispatch_no'])->update([
                'signature_path' => 'signatures/' .$fileName,
                'pay_type' => $input['pay_type'],
                'status' => 2,
            ]);
            $dispatch = Dispatch::where('dispatch_no', '=', $input['dispatch_no'])->first();
            if($dispatch->vendor_id == 6){
                $dispatch->status = 4;
                $dispatch->save();
            }
            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->customer_title = $customer->title;
            $dispatch->driver_name = $driver->name;
            $dispatch->car_license = $car->license;
            if($dispatch->vendor_id == 1){
                $makeImageResult = Dispatch::generateImage($dispatch, $signature_path);
            }else if($dispatch->vendor_id == 6 || $dispatch->vendor_id == 9){
                $makeImageResult = Dispatch::generateImageV3('dispatch1', $dispatch, $signature_path);
            }else{
                $makeImageResult = Dispatch::generateImageV2('dispatch1', $dispatch, $signature_path);
            }
            if(!$makeImageResult){
                return $this->sendError('簽名圖片生成失敗!請與車行洽詢!');
            }else{
                Dispatch::where('id', $dispatch->id)
                    ->update([
                        // 'signature_file' => 'signatures/' . $fileName,
                        'image_path' => $makeImageResult,
                        'down_time' => Carbon::now(),
                    ]);
            }
            return  $this->sendResponse($dispatch, 'File uploaded successfully.');
        }
    }
    public function uploadsignature2(Request $request)
    {
        // 外派司機 簽名上傳
        $input = $request->all();
        $validator = Validator::make($input, [
            'dispatch_no' => 'required',
            'driver_name' => 'required',
            'car_license' => 'required',
            'imgBase64' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $signatureData = $request->input('imgBase64');
        if(empty($signatureData)){
            $signature_path = '';
            // return  $this->sendResponse('message', 'add successfully.');
        }else{
            $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
            $signatureData = str_replace(' ', '+', $signatureData);
            $signature = base64_decode($signatureData);
            $fileName = 'st_' . $input['dispatch_no'] . '.png';
            Storage::put('public/signatures/' . $fileName, $signature);
            $signature_path = storage_path('app/public/signatures/' . $fileName);
            $updateData = Dispatch::where('dispatch_no', '=', $input['dispatch_no'])->update([
                'signature_path' => 'signatures/' .$fileName,
                // 'pay_type' => $input['pay_type'],
                'status' => 2,
            ]);
            $dispatch = Dispatch::where('dispatch_no', '=', $input['dispatch_no'])->first();
            // $driver = Driver::where('id', $dispatch->driver_id)->first();
            // $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->customer_title = $customer->title;
            $dispatch->driver_name = $input['driver_name'];
            $dispatch->car_license = $input['car_license'];
            if($dispatch->vendor_id == 9){
                Log::info('uploadsignature2: before generateImageV3', [
                    'dispatch' => $dispatch,
                    'signature_path' => $signature_path,
                ]);
                $makeImageResult = Dispatch::generateImageV3('dispatch1', $dispatch, $signature_path);
            }else{
                $makeImageResult = Dispatch::generateImage($dispatch, $signature_path);
            }
            if(!$makeImageResult){
                return $this->sendError('簽名圖片生成失敗!請與車行洽詢!');
            }else{
                Dispatch::where('id', $dispatch->id)
                    ->update([
                        // 'signature_file' => 'signatures/' . $fileName,
                        'driver2' => $input['driver_name'],
                        'car_license2' => $input['car_license'],
                        'image_path' => $makeImageResult,
                        'down_time' => Carbon::now(),
                    ]);
            }
            return  $this->sendResponse($dispatch, 'File uploaded successfully.');
        }
    }
    public function uploadsignature5(Request $request)
    {
        // 瑞陞外派司機 簽名上傳
        $input = $request->all();
        $validator = Validator::make($input, [
            'dispatch_id' => 'required',
            // 'driver_name' => 'required',
            // 'car_license' => 'required',
            'imgBase64' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $signatureData = $request->input('imgBase64');
        if(empty($signatureData)){
            $signature_path = '';
            // return  $this->sendResponse('message', 'add successfully.');
        }else{
            $dispatch = Dispatch::where('id', '=', $input['dispatch_id'])->first();
            $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
            $signatureData = str_replace(' ', '+', $signatureData);
            $signature = base64_decode($signatureData);
            $fileName = 'st_' . $dispatch->dispatch_no . '.png';
            Storage::put('public/signatures/' . $fileName, $signature);
            $signature_path = storage_path('app/public/signatures/' . $fileName);
            $updateData = Dispatch::where('id', '=', $input['dispatch_id'])->update([
                'signature_path' => 'signatures/' .$fileName,
                // 'pay_type' => $input['pay_type'],
                // 'status' => 2,
            ]);

            $driver = Driver::where('id', $dispatch->driver_id)->first();
            $car = Car::where('driver_id', $dispatch->driver_id)->first();
            $customer = Customer::where('id', $dispatch->customer_id)->first();
            if($dispatch->department_id!=null){
                $department = Department::where('id', $dispatch->department_id)->first();
                $dispatch->department_name = $department->name;
            }
            $dispatch->customer_title = $customer->title;
            if($dispatch->driver_zs_id == null){
                $dispatch->driver_name = $driver->name;
                $dispatch->car_license = $car->license;
            }else{
                $extDriver = Driver::where('id', $dispatch->driver_zs_id)->first();
                $extCar = Car::where('driver_id', $dispatch->driver_zs_id)->first();
                $dispatch->driver_name = $extDriver->name;
                $dispatch->car_license = $extCar->license;
            }

            $makeImageResult = Dispatch::generateImageV3('dispatch1',$dispatch, $signature_path);
            if(!$makeImageResult){
                return $this->sendError('簽名圖片生成失敗!請與車行洽詢!');
            }else{
                Dispatch::where('id', $dispatch->id)
                    ->update([
                        // 'signature_file' => 'signatures/' . $fileName,
                        // 'driver2' => $input['driver_name'],
                        // 'car_license2' => $input['car_license'],
                        'image_path' => $makeImageResult,
                        'down_time' => Carbon::now(),
                    ]);
            }
            return  $this->sendResponse($dispatch, 'File uploaded successfully.');
        }
    }
    public function uploadsignature3(Request $request){
        // 空白簽單 簽名上傳
        $input = $request->all();
        // var_dump($input);
        // die;
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'pay_type' => 'required',
            'imgBase64' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $signatureData = $request->input('imgBase64');
        $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
        $signatureData = str_replace(' ', '+', $signatureData);
        $signature = base64_decode($signatureData);
        $newDispatchNo = Dispatch::generateDispatchNumber();
        $fileName = 'st_' . $newDispatchNo . '.png';
        // echo $fileName; die;
        Storage::put('public/signatures/' . $fileName, $signature);
        $signature_path = storage_path('app/public/signatures/' . $fileName);
        // //new Dispatch
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = new Dispatch();
        $dispatch->dispatch_no = $newDispatchNo;
        $dispatch->source = 1;
        $dispatch->status = 101;
        $dispatch->customer_id = 1;
        $dispatch->passenger_name = $input['passenger_name'];
        $dispatch->passenger_mobile = $input['passenger_mobile'];
        $dispatch->driver_id = $driver->id;
        $dispatch->signature_path = $signature_path;
        $dispatch->save();
        return  $this->sendResponse($dispatch, 'save successfully.');
        // $updateData = Dispatch::where('dispatch_no', '=', $input['dispatch_no'])->update([
        //     'signature_path' => 'signatures/' .$fileName,
        //     // 'pay_type' => $input['pay_type'],
        //     'status' => 2,
        // ]);
    }
    public function myview(Request $request){
        $someVariable = "'%台積電%'";
        $data = DB::select("select customer_id, COUNT(id) AS COUNT, SUM(rental_cost) AS sum FROM dispatches WHERE customer_id=2 GROUP BY customer_id");
        // foreach ($data as $key => $value) {
        //     $data[$key]->aa = Dispatch::where('customer_address', $value->customer_address)
        //                             ->where('customer_name', 'like', '%佳能%')
        //                             ->get();
        // }
        $data[0]->all = Dispatch::where('customer_id', '=', '2')
                                ->get();
        return view('report.myreport', compact('data'));
    }
    public function ordersByTimeRange($range, $driver){
        $now = Carbon::now();

        switch ($range) {
            case 'today':
                $startDate = $now->startOfDay();
                $endDate = $now->endOfDay();
                // die('bb' . $startDate);
                break;
            case 'week':
                $startDate = $now->startOfWeek();
                $endDate = $now->endOfWeek();
                break;
            case 'month':
                $startDate = $now->startOfMonth();
                $endDate = $now->endOfMonth();
                break;
            default:
                // 處理其他情況，例如自定義日期範圍
                // $startDate = '';
                // $endDate = '';
                return Dispatch::with('customer')->where('driver_id', '=', $driver->id)->orderBy('id', 'desc')->get();
        }
        return Dispatch::with('customer')
                            ->where('driver_id', '=', $driver->id)
                            ->whereBetween('start_date', [$startDate, $endDate])
                            ->orderBy('id', 'desc')->get();
    }
    public function mkepaper(Request $request){
        // echo 'aaa';
        $dispatch = Dispatch::find(89);
        $customer = Customer::where('id', $dispatch->customer_id)->first();
        if($dispatch->department_id != null){
            $department = Department::where('id', $dispatch->department_id)->first();
            $dispatch->department_name = $department->name;
        }
        $dispatch->customer_title = $customer->title;
        $dispatch->driver_name = '張三張三';
        $dispatch->car_license = 'AA-1234';
        $signature = null;
        $rs = Dispatch::generateImageV2('dispatch2', $dispatch, $signature);
        $dispatch->image2_path = $rs;
        unset($dispatch->customer_title);
        unset($dispatch->driver_name);
        unset($dispatch->car_license);
        unset($dispatch->department_name);
        $dispatch->save();
        dd($rs);
    }
    public function callordertime(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = Dispatch::find($input['id']);
        $dispatch->status = 6;
        $dispatch->confirm_order_time =Carbon::now();
        $dispatch->save();
        return  $this->sendResponse($dispatch, '更新成功.');
    }
    public function callfinishtime(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = Dispatch::find($input['id']);
        $dispatch->status = 2;
        $dispatch->finish_time =Carbon::now();
        $dispatch->save();
        return  $this->sendResponse($dispatch, '更新成功.');
    }
    public function calluptime(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = Dispatch::find($input['id']);
        $dispatch->up_time =Carbon::now();
        $dispatch->save();
        return  $this->sendResponse($dispatch, '更新成功.');
    }
    public function callstartuptime(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = Dispatch::find($input['id']);
        $dispatch->status = 4;
        $dispatch->car_start_time =Carbon::now();
        $dispatch->save();
        $user = User::find(7);
        Notification::make()
            ->title('派車通知')
            // ->body('您的派車已啟動!')
            // ->sendToDatabase($user);
            ->broadcast($user);
        // event(new DispatchCreated($dispatch));
        return  $this->sendResponse($dispatch, '更新成功.');
    }
    public function uploadnote(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = Dispatch::find($input['id']);
        $dispatch->driver_note =$input['driver_note'];
        $dispatch->save();
        return  $this->sendResponse($dispatch, '更新成功.');
    }
    public function getdispatch(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'dpno' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $mydpno = Crypt::decryptString($input['dpno']);
        // Log::info('mydpno >> ', [$mydpno]);
        $dispatch = Dispatch::where('dispatch_no', '=', $mydpno)->first();
        if($dispatch){
            // foreach ($dispatch as $key => $value) {
                if($dispatch->vendor_id == 1){
                    $dispatch->showUpButton = false;
                }else{
                    $dispatch->showUpButton = true;
                }
                // 計算出發時間和現在之間的差距
                $startTime = Carbon::parse($dispatch->start_date);
                $currentTime = Carbon::now();

                // 判斷是否在3小時以內
                $minutesDifference  = $currentTime->diffInMinutes($startTime, false);
                // Log::info('$minutesDifference >> ', [$minutesDifference]);
                if($dispatch->vendor_id == 6 && $minutesDifference <= 180 && $minutesDifference >= 0){
                    $dispatch->showStartUpButton = true;
                }else{
                    $dispatch->showStartUpButton = false;
                }
                if($dispatch->vendor_id == 6){
                    $dispatch->showFinishButton = true;
                    $dispatch->signaHight = "100%";
                }else{
                    $dispatch->showFinishButton = false;
                    $dispatch->signaHight = "60%";
                }
            // }
            if($dispatch->status == 2){
                return $this->sendError('此案件已結案!');
            }else if($dispatch->status == 1){
                return  $this->sendResponse($dispatch, '查詢成功.');
            }else if($dispatch->status == 4){
                return  $this->sendResponse($dispatch, '查詢成功.');
            }else{
                return $this->sendError('此案件未啟用!');
            }
        }else{
            return $this->sendError('查無此案件!');
        }
        // return $this->sendError('此案件未啟用!');

    }
    public function getdispatchbyzs(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'dpid' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $mydpid = $input['dpid'];
        $dispatch = Dispatch::where('id', '=', $mydpid)->first();
        if($dispatch){
            // foreach ($dispatch as $key => $value) {
                if($dispatch->vendor_id == 1){
                    $dispatch->showUpButton = false;
                }else{
                    $dispatch->showUpButton = true;
                }
                // 計算出發時間和現在之間的差距
                $startTime = Carbon::parse($dispatch->start_date);
                $currentTime = Carbon::now();

                // 判斷是否在3小時以內
                $minutesDifference  = $currentTime->diffInMinutes($startTime, false);
                // Log::info('$minutesDifference >> ', [$minutesDifference]);
                if($dispatch->vendor_id == 6 && $minutesDifference <= 180 && $minutesDifference >= 0){
                    $dispatch->showStartUpButton = true;
                    // Log::info('showStartUpButton >> TRUE');
                }else{
                    $dispatch->showStartUpButton = false;
                    // Log::info('showStartUpButton >> false');
                }
                if($dispatch->vendor_id == 6){
                    $dispatch->showFinishButton = true;
                    $dispatch->signaHight = "100%";
                }else{
                    $dispatch->showFinishButton = false;
                    $dispatch->signaHight = "60%";
                }
            // }
            if($dispatch->status == 2){
                return $this->sendError('此案件已結案!');
            }else if($dispatch->status == 1){
                return  $this->sendResponse($dispatch, '查詢成功.');
            }else if($dispatch->status == 4){
                return  $this->sendResponse($dispatch, '查詢成功.');
            }else{
                return $this->sendError('此案件未啟用!');
            }
        }else{
            return $this->sendError('查無此案件!');
        }
        // return $this->sendError('此案件未啟用!');

    }
    public function testdata(Request $request){
        $input = $request->all();
        // $validator = Validator::make($input, [
        //     'line_id' => 'required|max:255',
        // ]);
        // if($validator->fails()){
        //     return $this->sendError('Validation Error.', $validator->errors());
        // }
        $driver = Dispatch::where('customer_id', '=', 35)->get();
        if($driver){
            return  $this->sendResponse($driver, '查詢成功.');
        }else{
            return $this->sendError('查無此司機!');
        }
    }
    public function getcustomer(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'token' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $customer = Customer::where('vendor_id', '=', 1)->get();
        if($customer){
            return  $this->sendResponse($customer, '查詢成功.');
        }else{
            return $this->sendError('查無此司機!');
        }
    }
    public function savecustomer(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'token' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $customer = new Customer();
        $customer->vendor_id = 1;
        $customer->title = $input['title'];
        $customer->address = empty($input['address']) ? '' : $input['address'];
        $customer->contact = empty($input['contact']) ? '' : $input['contact'];
        $customer->telephone = empty($input['telephone']) ? '' : $input['telephone'];;
        $customer->save();
        if($customer){
            $allCustomer = Customer::where('vendor_id', '=', 1)->get();
            return  $this->sendResponse($allCustomer, '新增成功.');
        }else{
            return $this->sendError('查無此司機!');
        }
    }
    public function drawepaper(Request $request){
        $input = $request->all();
        Log::info('draw paper >> ', $input);
        $validator = Validator::make($input, [
            'token' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // echo 'draw paper >> ' . $input['token'];
        $dispatch = new Dispatch();
        try {
            $dispatch->dispatch_no = Dispatch::generateDispatchNumber();
            $dispatch->source = 3;
            $dispatch->customer_id = 35;
            $dispatch->passenger_name = '中壢';
            $dispatch->passenger_mobile = '0912345678';
            $dispatch->driver_id = 1;
            $dispatch->start_date = Carbon::now();
            $result = $dispatch->save();
            if($result){
                return  $this->sendResponse($result, '新增成功.');
            }else{
                return $this->sendError('查無此司機!');
            }
        } catch (\Exception $e) {
            echo $e->getMessage();
        }

    }
    public function drawdriverinfo(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            'token' => 'required|max:255',
            'dispatch_id' => 'required|max:8',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch = Dispatch::where('id', '=', $input['dispatch_id'])->first();
        // $driver = Driver::where('id', '=', $input['driver_id'])->first();
        // $this->generateImage('driver_info', $driver);
        // var_dump($driver);
        // $this->generateImage('order_info', $dispatch);
    }
    public function txtcreatedispatch(Request $request){
        // 字串建立 派車單
        $input = $request->all();
        $validator = Validator::make($input, [
            'token' => 'required|max:255',
            'dispatch_str' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        if($input['token']!='123456'){
            return $this->sendError('Validation Error.', 'Token 錯誤!');
        }
        $dispatch_str = $input['dispatch_str']; // get the data
        $dispatch_array = explode(',', $dispatch_str);
        $result = [];

        // 逐行處理，將每一行分割成鍵值對
        foreach ($dispatch_array as $line) {
            list($key, $value) = explode(':', $line);
            $result[trim($key)] = trim($value);
        }
        var_dump($result);
        // $dispatch = new Dispatch();
        // $dispatch->dispatch_no = $input['dispatch_str'];
        // $dispatch->source = 3;
        // $dispatch->customer_id = 35;
        // $dispatch->passenger_name = '中壢';
        // $dispatch->passenger_mobile = '0912345678';
        // $dispatch->driver_id = 1;
        // $dispatch->start_date = Carbon::now();
        // $result = $dispatch->save();
        // if($result){
        //     return  $this->sendResponse($result, '新增成功.');
        // }else{
        //     return $this->sendError('查無此司機!');
        // }
    }
}
