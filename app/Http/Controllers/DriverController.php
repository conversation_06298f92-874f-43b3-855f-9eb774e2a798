<?php

namespace App\Http\Controllers;

use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Log;

class DriverController extends Controller
{
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required|max:255',
            'name' => 'required|max:32',
            'sex' => 'required',
            'mobile' => 'required|max:32',
            'address' => 'required|max:128',
            'person_id' => 'required|max:32',
            'car' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // Log::info(print_r($input, true));
        $vendor = Vendor::where('join_code', '=', $input['join_code'])->first();
        // Log::info(print_r($vendor, true));
        if(!$vendor){
            return $this->sendError('查無此邀請碼!請與車行洽詢!');
        }
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        Log::info(print_r($driver, true));
        if($driver){
            $driver->line_id = $input['line_id'];
            $driver->nickname = $input['nickname'];
            $driver->name = $input['name'];
            $driver->sex = $input['sex'];
            $driver->mobile = $input['mobile'];
            $driver->address = $input['address'];
            $driver->vendor_id = $vendor->id;
            $driver->save();
            $car = Car::where('driver_id', '=', $driver->id)->first();
            if(!$car){
                $car = new Car();
                $car->driver_id = $driver->id;
                $car->brand = $input['car']['brand'];
                // $car->model = $input['car']['model'];
                $car->license = $input['car']['license'];
                $car->save();
            }else{
                $car->brand = $input['car']['brand'];
                $car->model = $input['car']['model'];
                $car->license = $input['car']['license'];
                $car->save();
            }
            return $this->sendResponse($driver, '更新成功');
        }else{
            Log::info('新增司機'. json_encode($input));
            DB::beginTransaction();
            try {
                $driver = new Driver();
                $driver->line_id = $input['line_id'];
                $driver->nickname = empty($input['nickname']) ? '' : $input['nickname'];
                $driver->avatar = empty($input['avatarUrl']) ? '' : $input['avatarUrl'];
                $driver->person_id = $input['person_id'];
                $driver->name = $input['name'];
                $driver->sex = $input['sex'];
                $driver->address = $input['address'];
                $driver->mobile = $input['mobile'];
                $driver->vendor_id = $vendor->id;
                $driver->save();
                // 更新車輛
                $car = new Car();
                $car->driver_id = $driver->id;
                $car->brand = $input['car']['brand'];
                // $car->model = $input['car']['model'];
                $car->license = $input['car']['license'];
                $car->save();
                DB::commit();
                return $this->sendResponse($driver, '註冊成功');
            } catch (\Exception $e) {
                DB::rollback();
                return $this->sendError('發生錯誤!請與系統商聯繫!');
            }
        }
    }
    public function edit(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $car = Car::whereBelongsTo($driver)->get();
        $driver->car = $car;
        return $this->sendResponse($driver, '成功取得資料.');
    }
    public function getdriver(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'driver_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = Driver::where('id', '=', $input['driver_id'])->first();
        if(!$driver){
            return $this->sendError('查無此司機!');
        }
        return $this->sendResponse($driver, '讀取成功');
    }
}
